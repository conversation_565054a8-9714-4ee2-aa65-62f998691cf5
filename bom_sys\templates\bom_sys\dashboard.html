{% extends 'base.html' %}

{% block title %}主页 - 物料清单管理系统{% endblock %}

{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}

{% block content %}
<div class="wrapper">
    <!-- 导航栏 -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- 左侧导航栏切换按钮 -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
        </ul>

        <!-- 右侧导航栏 - 标签页区域 -->
        <ul class="navbar-nav ml-auto" id="nav-tabs">
            <!-- 标签页将动态添加到这里 -->
            <li class="nav-item">
                <a class="nav-link active" data-toggle="tab" href="#dashboard-tab" id="tab-dashboard-link">
                    <i class="fas fa-tachometer-alt"></i> 仪表盘
                </a>
            </li>
        </ul>

        <!-- 用户菜单 -->
        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
                    <i class="fas fa-user-circle"></i>
                    <span class="d-none d-md-inline">{{ user_nick|default:username }}</span>
                </a>
                <div class="dropdown-menu dropdown-menu-right">
                    <a href="#" class="dropdown-item" data-toggle="modal" data-target="#profileEditModal">
                        <i class="fas fa-user mr-2"></i> 个人资料
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="{% url 'logout' %}" class="dropdown-item">
                        <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                    </a>
                </div>
            </li>
        </ul>
    </nav>

    <!-- 左侧边栏 -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
        <!-- 品牌Logo -->
        <a href="#" class="brand-link">
            <span class="brand-text font-weight-light">物料清单管理系统</span>
        </a>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 用户面板 -->
            <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                <div class="image">
                    <i class="fas fa-user-circle fa-2x text-light"></i>
                </div>
                <div class="info">
                    <a href="#" class="d-block">{{ user_nick|default:username }}</a>
                </div>
            </div>

            <!-- 侧边栏菜单 -->
            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                    <li class="nav-item">
                        <a href="#" class="nav-link active menu-link" data-url="{% url 'dashboard' %}" data-title="仪表盘" data-icon="fas fa-tachometer-alt" data-id="dashboard">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <p>
                                仪表盘
                            </p>
                        </a>
                    </li>

                    
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="nav-icon fas fa-boxes"></i>
                            <p>
                                物料管理
                                <i class="fas fa-angle-left right"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="#" class="nav-link menu-link" data-url="{% url 'material_list' %}?iframe=1" data-title="物料清单" data-icon="fas fa-list" data-id="material-list">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>物料清单</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link menu-link" data-url="{% url 'material_query' %}?iframe=1" data-title="物料查询" data-icon="fas fa-search" data-id="material-query">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>物料查询</p>
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="nav-icon fas fa-file-invoice"></i>
                            <p>
                                BOM管理
                                <i class="fas fa-angle-left right"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="#" class="nav-link menu-link" data-url="{% url 'bom_list_new' %}?iframe=1" data-title="BOM清单" data-icon="fas fa-list-alt" data-id="bom-list-new">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>BOM清单</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link menu-link" data-url="{% url 'import_bom' %}?iframe=1" data-title="导入BOM" data-icon="fas fa-file-import" data-id="import-bom">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>导入BOM</p>
                                </a>
                            </li>
                        </ul>
                    </li>
                     
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="nav-icon fas fa-database"></i>
                            <p>
                                基础数据
                                <i class="fas fa-angle-left right"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="#" class="nav-link menu-link" data-url="{% url 'data_dictionary_list' %}?iframe=1" data-title="数据字典" data-icon="fas fa-book" data-id="data-dictionary">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>数据字典</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{% url 'customer_list' %}?iframe=1" class="nav-link menu-link" data-url="{% url 'customer_list' %}?iframe=1" data-title="客户信息" data-icon="fas fa-user-tie" data-id="customer">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>客户信息</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{% url 'properties_type_list' %}?iframe=1" class="nav-link menu-link" data-url="{% url 'properties_type_list' %}?iframe=1" data-title="物料属性" data-icon="fas fa-tags" data-id="properties-type">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>物料属性</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{% url 'process_route_list' %}?iframe=1" class="nav-link menu-link" data-url="{% url 'process_route_list' %}?iframe=1" data-title="工艺路线" data-icon="fas fa-route" data-id="process-route">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>工艺路线</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{% url 'equipment_list' %}?iframe=1" class="nav-link menu-link" data-url="{% url 'equipment_list' %}?iframe=1" data-title="设备管理" data-icon="fas fa-tools" data-id="equipment-list">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>设备管理</p>
                                </a>
                            </li>
                        </ul>
                    </li>                   
                    
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="nav-icon fas fa-cogs"></i>
                            <p>
                                系统设置
                                <i class="fas fa-angle-left right"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="#" class="nav-link menu-link" data-url="{% url 'user_list' %}?iframe=1" data-title="用户管理" data-icon="fas fa-users" data-id="user-list">
                                    <i class="nav-icon fas fa-users"></i>
                                    <p>用户管理</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link menu-link" data-url="{% url 'system_log' %}?iframe=1" data-title="日志管理" data-icon="fas fa-history" data-id="system-log">
                                    <i class="nav-icon fas fa-history"></i>
                                    <p>日志管理</p>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
    </aside>

    <!-- 内容区域 -->
    <div class="content-wrapper">
        <!-- 标签页内容区域 -->
        <div class="tab-content" id="tab-content">
            <!-- 默认仪表盘标签 -->
            <div class="tab-pane fade show active" id="dashboard-tab">
                <!-- 主体内容 -->
                <section class="content">
                    <div class="container-fluid">
                        <!-- 消息提示 -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                    {{ message }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            {% endfor %}
                        {% endif %}
                        
                        <!-- 信息卡片 -->
                        <div class="row">
                            <div class="col-lg-3 col-6">
                                <div class="small-box bg-info">
                                    <div class="inner">
                                        <h3>{{ material_count }}</h3>
                                        <p>物料总数</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-boxes"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-3 col-6">
                                <div class="small-box bg-success">
                                    <div class="inner">
                                        <h3>{{ bom_count }}</h3>
                                        <p>BOM总数</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-3 col-6">
                                <div class="small-box bg-warning">
                                    <div class="inner">
                                        <h3>{{ properties_type_count }}</h3>
                                        <p>物料属性分类</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-tags"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-3 col-6">
                                <div class="small-box bg-danger">
                                    <div class="inner">
                                        <h3>{{ temp_bom_count }}</h3>
                                        <p>临时BOM总数</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 图表区域 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">最近添加的物料</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>物料编码</th>
                                                        <th>物料名称</th>
                                                        <th>客户图号</th>
                                                        <th>添加时间</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for material in recent_materials %}
                                                    <tr>
                                                        <td>{{ material.material_no }}</td>
                                                        <td>{{ material.material_name }}</td>
                                                        <td>{{ material.material_drawingno }}</td>
                                                        <td>{{ material.material_uptime|date:"Y-m-d H:i" }}</td>
                                                    </tr>
                                                    {% empty %}
                                                    <tr>
                                                        <td colspan="4" class="text-center">暂无数据</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">最近更新的BOM</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>BOM编号</th>
                                                        <th>产品名称</th>
                                                        <th>客户图号</th>
                                                        <th>更新时间</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for bom in recent_boms %}
                                                    <tr>
                                                        <td>{{ bom.bom_id }}</td>
                                                        <td>{{ bom.material.material_name }}</td>
                                                        <td>{{ bom.material.material_drawingno }}</td>
                                                        <td>{{ bom.update_time|date:"Y-m-d H:i" }}</td>
                                                    </tr>
                                                    {% empty %}
                                                    <tr>
                                                        <td colspan="4" class="text-center">暂无数据</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            
            <!-- 其他标签页将在这里动态创建 -->
        </div>
    </div>

</div>

<!-- 标签页模板（用于克隆） -->
<template id="tab-template">
    <li class="nav-item">
        <a class="nav-link" data-toggle="tab" href="#tab-{id}">
            <i class="{icon}"></i> {title}
            <button type="button" class="close tab-close" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </a>
    </li>
</template>

<!-- 标签内容模板（用于克隆） -->
<template id="tab-content-template">
    <div class="tab-pane fade" id="tab-{id}">
        <iframe src="{url}" frameborder="0" style="width: 100%;  border: none;" class="tab-iframe" data-widget="iframe" allowfullscreen></iframe>
    </div>
</template>

<!-- 个人资料编辑模态窗口 -->
<div class="modal fade" id="profileEditModal" tabindex="-1" role="dialog" aria-labelledby="profileEditModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="profileEditModalLabel">编辑个人资料</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="profileEditForm" method="post" action="javascript:void(0);">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="id_user_nick">昵称</label>
                        <input type="text" class="form-control" id="id_user_nick" name="user_nick" value="{{ user_nick|default:'' }}">
                    </div>
                    <div class="form-group">
                        <label for="id_new_password">新密码 (留空表示不修改)</label>
                        <input type="password" class="form-control" id="id_new_password" name="new_password">
                    </div>
                    <div class="form-group">
                        <label for="id_confirm_password">确认新密码</label>
                        <input type="password" class="form-control" id="id_confirm_password" name="confirm_password">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 初始化AdminLTE IFrame组件
        if ($.fn.IFrame) {
            $.fn.IFrame.Constructor.Default = $.extend({}, $.fn.IFrame.Constructor.Default, {
                autoIframeMode: true,
                autoItemActive: true,
                autoShowNewTab: true,
                autoDarkMode: false,
                allowDuplicates: false,
                allowReload: true,
                loadingScreen: false,
                useNavbarItems: true
            });
        }

        // 自动隐藏提示信息
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
        
        // 标签页管理
        const $navTabs = $('#nav-tabs');
        const $tabContent = $('#tab-content');
        
        // 初始化dashboard标签，确保它有正确的ID
        $('#tab-dashboard-link').attr('href', '#tab-dashboard');
        $('#dashboard-tab').attr('id', 'tab-dashboard');
        
        // 点击菜单项创建新标签页或切换到已有标签
        $('.menu-link').on('click', function(e) {
            e.preventDefault();
            const url = $(this).data('url');
            const title = $(this).data('title');
            const icon = $(this).data('icon');
            const id = $(this).data('id');
            
            // 移除所有菜单项的激活状态
            $('.menu-link').removeClass('active');
            // 设置当前菜单项为激活状态
            $(this).addClass('active');
            
            // 检查标签是否已存在
            const tabId = 'tab-' + id;
            
            // 特殊处理仪表盘标签
            if (id === 'dashboard') {
                // 激活默认的仪表盘标签
                activateTab('tab-dashboard');
                return;
            }
            
            if ($('#' + tabId).length === 0) {
                // 创建新标签
                createNewTab(id, title, icon, url);
            } else {
                // 激活已存在的标签并刷新iframe内容
                activateTab(tabId);
                // 刷新iframe内容
                const iframe = $('#' + tabId + ' iframe');
                const currentSrc = iframe.attr('src');
                // 添加或更新时间戳参数以强制刷新
                const timestamp = new Date().getTime();
                let newSrc = currentSrc;
                if (currentSrc.indexOf('?') !== -1) {
                    // 已有参数，添加或更新t参数
                    if (currentSrc.indexOf('t=') !== -1) {
                        newSrc = currentSrc.replace(/t=\d+/, 't=' + timestamp);
                    } else {
                        newSrc = currentSrc + '&t=' + timestamp;
                    }
                } else {
                    // 没有参数，添加t参数
                    newSrc = currentSrc + '?t=' + timestamp;
                }
                iframe.attr('src', newSrc);
            }
        });
        
        // 关闭标签按钮点击事件（使用事件委托）
        $navTabs.on('click', '.tab-close', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const $tab = $(this).closest('.nav-item');
            const tabId = $tab.find('a').attr('href').substring(1); // 去掉#号
            
            // 如果关闭的是当前活动标签，则激活前一个标签
            if ($tab.find('a').hasClass('active')) {
                const $prevTab = $tab.prev('.nav-item');
                if ($prevTab.length) {
                    const prevTabId = $prevTab.find('a').attr('href').substring(1);
                    activateTab(prevTabId);
                } else {
                    const $nextTab = $tab.next('.nav-item');
                    if ($nextTab.length) {
                        const nextTabId = $nextTab.find('a').attr('href').substring(1);
                        activateTab(nextTabId);
                    }
                }
            }
            
            // 移除标签和内容
            $tab.remove();
            $('#' + tabId).remove();
        });
        
        // 标签点击事件，确保点击时能正确切换和高亮显示
        $navTabs.on('click', 'a[data-toggle="tab"]', function(e) {
            e.preventDefault();
            const tabId = $(this).attr('href').substring(1); // 去掉#号
            activateTab(tabId);
            
            // 如果有对应的菜单项，也设置为激活状态
            const menuId = tabId.replace('tab-', '');
            $('.menu-link').removeClass('active');
            $(`.menu-link[data-id="${menuId}"]`).addClass('active');
            
            // 注意：这里不刷新iframe，保持现有逻辑
        });
        
        // 激活标签的函数
        function activateTab(tabId) {
            // 移除所有标签和内容的激活状态
            $navTabs.find('a').removeClass('active');
            $('.tab-pane').removeClass('show active');
            
            // 设置当前标签和内容为激活状态
            $(`a[href="#${tabId}"]`).addClass('active');
            $(`#${tabId}`).addClass('show active');
        }
        
        // 创建新标签页的函数
        function createNewTab(id, title, icon, url) {
            // 生成标签HTML
            const tabId = 'tab-' + id;
            
            // 创建标签头
            let tabTemplate = $('#tab-template').html();
            tabTemplate = tabTemplate
                .replace('{id}', id)
                .replace('{title}', title)
                .replace('{icon}', icon);
                
            const $newTab = $(tabTemplate);
            $newTab.find('a').attr('id', tabId + '-link').attr('href', '#' + tabId);
            $navTabs.append($newTab);
            
            // 创建标签内容
            let contentTemplate = $('#tab-content-template').html();
            contentTemplate = contentTemplate
                .replace(/{id}/g, id)
                .replace('{url}', url);
                
            const $newContent = $(contentTemplate);
            $tabContent.append($newContent);
            
            // 激活新标签
            activateTab(tabId);
            
            // 初始化 iframe
            try {
                // 添加一个延迟，确保DOM已更新
                setTimeout(function() {
                    // 确保iframe已加载
                    $('#' + tabId + ' iframe').on('load', function() {
                        // 避免循环引用导致的JSON序列化错误
                        try {
                            // 不使用AdminLTE的IFrame初始化方法，而是使用自定义方法
                            const iframe = this;
                            $(iframe).css('height', '100%');
                            $(iframe).parent().css('height', '100%');
                        } catch (e) {
                            // 初始化iframe失败
                        }
                    });
                }, 100);
            } catch (e) {
                // 初始化iframe失败
            }
        }
        
        // 全局可访问的创建标签函数
        window.createNewTab = createNewTab;
    });
</script>
{% endblock %} 