/**
 * 物料清单管理系统 - 主JS文件
 */

// 文档就绪函数
$(document).ready(function() {
    // 侧边栏菜单激活状态
    const currentUrl = window.location.pathname;
    $('.nav-sidebar a').each(function() {
        const linkUrl = $(this).attr('href');
        if (linkUrl && currentUrl.indexOf(linkUrl) !== -1) {
            $(this).addClass('active');
            $(this).parents('.nav-item').addClass('menu-open');
            $(this).parents('.nav-treeview').prev('.nav-link').addClass('active');
        }
    });

    // 表单验证
    $('form').submit(function(event) {
        const requiredFields = $(this).find('[required]');
        let valid = true;
        
        requiredFields.each(function() {
            if (!$(this).val()) {
                valid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!valid) {
            event.preventDefault();
            alert('请填写所有必填字段');
        }
    });

    // 自动关闭提示框
    // setTimeout(function() {
    //     $('.alert').alert('close');
    // }, 5000);

    // 数据表格排序和搜索
    if ($.fn.DataTable) {
        $('.dataTable').DataTable({
            "paging": true,
            "ordering": true,
            "info": true,
            "searching": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Chinese.json"
            }
        });
    }
    
    // 如果在iframe中，设置链接目标
    if (window.self !== window.top) {
        setupIframeLinks();
    }

    // 初始化个人资料编辑模态窗口
    $("#profileEditModal").on("show.bs.modal", function(e) {
        // 从服务器获取最新的用户信息
        $.ajax({
            url: '/profile/get/',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if (data.success) {
                    $("#id_user_nick").val(data.user_nick || '');
                }
            }
        });
    });
    
    // 处理个人资料表单提交
    $("#profileEditForm").submit(function(e) {
        e.preventDefault();
        
        // 验证表单
        if ($("#id_new_password").val() !== $("#id_confirm_password").val()) {
            alert("两次输入的密码不一致");
            return false;
        }
        
        // 获取CSRF令牌
        const csrftoken = $("[name=csrfmiddlewaretoken]").val();
        
        // 准备表单数据
        const formData = {
            'user_nick': $("#id_user_nick").val(),
            'new_password': $("#id_new_password").val(),
            'csrfmiddlewaretoken': csrftoken
        };
        
        // 提交表单数据
        $.ajax({
            url: '/profile/edit/',
            type: 'POST',
            data: formData,
            success: function(response) {
                // 关闭模态窗口
                $("#profileEditModal").modal('hide');

                // 显示成功消息
                if (response.success) {
                    // 显示成功消息
                    alert("个人资料更新成功");
                    // 刷新页面以显示更新后的昵称
                    location.reload();
                } else {
                    alert(response.error || "更新失败，请重试");
                }
            },
            error: function(xhr, status, error) {
                console.error("个人资料更新错误:", status, error);
                alert("发生错误，请重试");
            }
        });
    });

    // 添加导入BOM功能
    $(document).on('click', '#exportBom, .nav-link[href="#import-bom"]', function(e) {
        e.preventDefault();
        window.location.href = '/import_bom/';
    });
});

/**
 * 全局通知函数 - 在页面右下角显示浮窗通知
 * @param {string} title - 通知标题
 * @param {string} message - 通知内容
 * @param {string} type - 通知类型 (success, info, warning, danger)
 */
function showNotification(title, message, type) {
    // 确保通知容器存在
    let container = $('.notification-container');
    if (container.length === 0) {
        container = $('<div class="notification-container"></div>');
        $('body').append(container);
    }
    
    // 创建通知元素
    const notification = $(`
        <div class="notification alert alert-${type} alert-dismissible fade show">
            <strong>${title}</strong> ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `);
    
    // 添加到容器
    container.append(notification);
    
    // 自动关闭
    setTimeout(function() {
        notification.addClass('fade-out');
        setTimeout(function() {
            notification.remove();
        }, 300); // 等待动画完成
    }, 3000);
    
    // 点击关闭按钮时移除通知
    notification.find('.close').on('click', function() {
        notification.addClass('fade-out');
        setTimeout(function() {
            notification.remove();
        }, 300);
    });
}

// 处理Django消息，转换为浮窗通知
$(document).ready(function() {
    // 查找页面中的Django消息
    $('.alert-dismissible').each(function() {
        const $alert = $(this);
        
        // 获取消息内容
        const content = $alert.html();
        const type = $alert.hasClass('alert-success') ? 'success' : 
                     $alert.hasClass('alert-info') ? 'info' : 
                     $alert.hasClass('alert-warning') ? 'warning' : 
                     $alert.hasClass('alert-danger') ? 'danger' : 'info';
        
        // 隐藏原始消息
        $alert.hide();
        
        // 创建浮窗通知
        // 从内容中提取标题和消息
        const closeButton = '<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>';
        let cleanContent = content.replace(closeButton, '').trim();
        
        // 尝试提取标题和消息
        let title = '系统通知';
        let message = cleanContent;
        
        // 如果内容包含<strong>标签，则使用它作为标题
        const strongMatch = cleanContent.match(/<strong>(.*?)<\/strong>(.*)/);
        if (strongMatch) {
            title = strongMatch[1];
            message = strongMatch[2];
        }
        
        // 显示通知
        showNotification(title, message, type);
    });
});

// 处理消息提示框
$(document).ready(function() {
    // 处理alert的自动消失
    $('.alert').each(function() {
        // 检查是否设置了不自动消失的属性
        if ($(this).data('auto-dismiss') !== 'false') {
            // 默认5秒后自动隐藏提示框
            var alert = $(this);
            setTimeout(function() {
                alert.alert('close');
            }, 5000);
        }
    });
});

// 确认操作
function confirmAction(message) {
    return confirm(message || '确定要执行此操作吗？');
}

// 预览图片
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const imgElement = $(`#${previewId}`);
            imgElement.attr('src', e.target.result);
            
            // 将图片包装在预览容器中（如果尚未包装）
            if (!imgElement.parent().hasClass('image-preview-container')) {
                imgElement.wrap('<div class="image-preview-container"></div>');
            }
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// 初始化图片预览功能
$(document).ready(function() {
    // 处理页面上所有图片预览
    initImagePreview();
    
    // 关闭预览
    $('#imagePreviewClose, #imagePreviewOverlay').on('click', function(e) {
        if (e.target === this || $(e.target).closest('#imagePreviewClose').length) {
            $('#imagePreviewOverlay').removeClass('active');
        }
    });
    
    // 阻止预览图片上的点击事件冒泡
    $('#previewImage').on('click', function(e) {
        e.stopPropagation();
    });
});

// 初始化图片预览功能
function initImagePreview() {
    // 查找所有图片
    $('img').each(function() {
        const $img = $(this);
        
        // 跳过已处理的图片
        if ($img.data('preview-initialized')) {
            return;
        }
        
        // 标记为已处理
        $img.data('preview-initialized', true);
        
        // 将图片包装在预览容器中（如果尚未包装）
        if (!$img.parent().hasClass('image-preview-container') && !$img.hasClass('no-preview')) {
            $img.wrap('<div class="image-preview-container"></div>');
            
            // 绑定点击事件
            $img.parent('.image-preview-container').on('click', function() {
                const imgSrc = $img.attr('src');
                $('#previewImage').attr('src', imgSrc);
                $('#imagePreviewOverlay').addClass('active');
            });
        }
    });
    
    // 使用MutationObserver监听DOM变化，处理动态添加的图片
    const observer = new MutationObserver(function(mutations) {
        let hasNewImages = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                // 检查是否有新的图片元素被添加
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];
                    if (node.nodeType === 1) {
                        const $node = $(node);
                        if ($node.is('img') || $node.find('img').length > 0) {
                            hasNewImages = true;
                        }
                    }
                }
            }
        });
        
        // 如果有新的图片，重新初始化
        if (hasNewImages) {
            initImagePreview();
        }
    });
    
    // 配置观察器
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// 设置iframe内部链接，防止在iframe内部导航
function setupIframeLinks() {
    // 获取所有链接
    $('a').each(function() {
        const $link = $(this);
        const href = $link.attr('href');
        
        // 排除锚点链接、JavaScript链接和已经有target的链接
        if (href && href !== '#' && !href.startsWith('javascript:') && !$link.attr('target')) {
            // 如果链接不是打开新标签的操作
            if (!$link.hasClass('menu-link') && !$link.hasClass('open-tab')) {
                // 设置链接在当前iframe中打开
                $link.attr('data-iframe-link', 'true');
                
                // 添加点击事件监听器
                $link.on('click', function(e) {
                    // 阻止默认行为
                    e.preventDefault();
                    
                    // 在当前iframe中导航
                    window.location.href = href;
                });
            }
        }
    });
    
    // 处理表单提交
    $('form').each(function() {
        const $form = $(this);
        
        // 如果表单没有指定target
        if (!$form.attr('target')) {
            // 设置表单提交在当前iframe中处理结果
            $form.attr('data-iframe-form', 'true');
        }
    });
}

// 为父窗口提供的函数，用于在主框架中打开新标签页
function openNewTab(id, title, icon, url) {
    // 尝试获取父窗口中的函数
    if (window.parent && window.parent.createNewTab) {
        window.parent.createNewTab(id, title, icon, url);
    }
}

// 全局变量用于存储父窗口中的createNewTab函数
window.createNewTab = function(id, title, icon, url) {
    // 这个函数会在dashboard.html页面中被覆盖为真正的实现
}