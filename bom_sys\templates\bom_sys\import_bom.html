{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}导入BOM{% endblock %}

{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- 等待框 -->
    <div class="modal fade" id="loadingModal" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                        <span class="sr-only">正在处理...</span>
                    </div>
                    <h5 class="mb-2">正在导入BOM数据</h5>
                    <p class="text-muted mb-0">请耐心等待，导入过程可能需要几分钟...</p>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">选择客户和BOM文件</h3>
                        </div>
                        <div class="card-body">
                            {% if messages %}
                                {% for message in messages %}
                                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible" data-auto-dismiss="false">
                                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                                        {{ message }}
                                    </div>
                                {% endfor %}
                            {% endif %}
                            
                            {% if error_details %}
                                <div class="card mb-4 bg-danger">
                                    <div class="card-header">
                                        <h3 class="card-title">导入错误详情</h3>
                                        <div class="card-tools">
                                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                                        <ol class="text-white">
                                            {% for error in error_details %}
                                                <li>{{ error }}</li>
                                            {% endfor %}
                                        </ol>
                                    </div>
                                </div>
                            {% endif %}
                            
                            <form method="post" enctype="multipart/form-data" id="importBomForm">
                                {% csrf_token %}
                                <div class="form-group">
                                    <label for="customer">选择客户</label>
                                    <select class="form-control select2" id="customer" name="customer" required>
                                        <option value="">-- 请选择客户 --</option>
                                        {% for customer in customers %}
                                            <option value="{{ customer.customer_id }}">{{ customer.customer_name }} ({{ customer.customer_code }})</option>
                                        {% endfor %}
                                    </select>
                                    <small class="form-text text-muted">选择要导入BOM的客户</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="bom_file">选择BOM文件</label>
                                    <div class="input-group">
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="bom_file" name="bom_file" accept=".xls,.xlsx" required>
                                            <label class="custom-file-label" for="bom_file">选择文件</label>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">支持Excel格式(.xls, .xlsx)的BOM文件</small>
                                </div>
                                
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload mr-1"></i> 导入BOM
                                    </button>
                                    <a href="{% url 'download_import_template' %}" class="btn btn-outline-info ml-2" target="_blank">
                                        <i class="fas fa-download mr-1"></i> 下载导入模板
                                    </a>
                                </div>
                            </form>
                            
                            <div class="card mt-4">
                                <div class="card-header bg-info">
                                    <h3 class="card-title">导入说明</h3>
                                </div>
                                <div class="card-body">
                                    <h5>BOM导入要求：</h5>
                                    <ul>
                                        <li><strong>推荐：</strong>点击"下载导入模板"按钮获取标准格式的Excel模板</li>
                                        <li>Excel文件格式必须符合系统要求的BOM结构</li>
                                        <li>导入前请确认Excel文件中的物料信息正确无误</li>
                                        <li>导入过程可能需要一些时间，请耐心等待</li>
                                        <li>导入完成后可以查看导入结果并进行必要的修改</li>
                                    </ul>
                                    
                                    <div class="alert alert-warning mt-3">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>
                                        <strong>注意：</strong> 导入操作将根据Excel文件内容创建新的物料及BOM结构，请确保数据准确性。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'css/select2-bootstrap-5-theme.min.css' %}">
<style>
    .select2-container--bootstrap-5 .select2-selection {
        height: calc(2.25rem + 2px);
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
        height: calc(2.25rem + 2px);
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/select2.min.js' %}"></script>

<script>
    $(document).ready(function() {
        // 判断是否需要关闭等待框
        {% if close_loading %}
            $('#loadingModal').modal('hide');
        {% endif %}
        
        // 初始化Select2
        $('#customer').select2({
            theme: 'bootstrap-5',
            placeholder: '请选择客户',
            allowClear: true
        });
        
        // 美化文件输入框
        // 自定义文件输入框显示选择的文件名
        $('#bom_file').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName || '选择文件');
        });
        
        
        // 表单验证
        $('#importBomForm').on('submit', function(e) {
            
            var customerSelected = $('#customer').val();
            var fileSelected = $('#bom_file').val();
            
            if (!customerSelected) {
                e.preventDefault();
                alert('请选择客户');
                return false;
            }
            
            if (!fileSelected) {
                e.preventDefault();
                alert('请选择BOM文件');
                return false;
            }
            
            try {
                // 直接使用原生JS操作DOM，避免可能的jQuery冲突
                document.getElementById('loadingModal').classList.add('show');
                document.getElementById('loadingModal').style.display = 'block';
                document.getElementById('loadingModal').setAttribute('aria-hidden', 'false');

                // 添加模态背景
                var modalBackdrop = document.createElement('div');
                modalBackdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(modalBackdrop);

                // 防止页面滚动
                document.body.classList.add('modal-open');
            } catch(err) {
                console.error('显示等待框时出错:', err);

                // 尝试使用jQuery方式显示
                try {
                    $('#loadingModal').modal('show');
                } catch(jqErr) {
                    console.error('jQuery方式显示等待框也失败:', jqErr);
                }
            }
            return true;
        });
    });
</script>
{% endblock %} 