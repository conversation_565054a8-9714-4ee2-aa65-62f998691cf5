{% extends 'base.html' %}
{% load static %}

{% block title %}批量编辑数据字典 - 物料清单管理系统{% endblock %}

{% block body_class %}{% if is_iframe %}{% else %}hold-transition sidebar-mini layout-fixed{% endif %}{% endblock %}

{% block content %}
{% if is_iframe %}
    <!-- 主体内容 -->
    <section class="content">
        <div class="container-fluid">
            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">批量编辑 - {{ parent_name }}</h3>
                    <div class="card-tools">
                        <a href="{% url 'data_dictionary_tree' %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回树形视图
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'data_dictionary_batch_edit' %}{% if is_iframe %}?iframe=1{% endif %}">
                        {% csrf_token %}
                        <input type="hidden" name="parent_id" value="{{ parent_id }}">
                        
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>代码</th>
                                        <th>标签</th>
                                        <th>排序</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 现有项 -->
                                    {% for item in dict_items %}
                                    <tr>
                                        <td>
                                            <input type="hidden" name="dict_id" value="{{ item.datadictionary_id }}">
                                            <input type="text" class="form-control" name="dict_name" value="{{ item.datadictionary_name }}" required>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="dict_code" value="{{ item.datadictionary_code }}" required>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="dict_tag" value="{{ item.datadictionary_tag }}">
                                        </td>
                                        <td>
                                            <input type="number" class="form-control" name="dict_order" value="{{ item.datadictionary_order }}">
                                        </td>
                                    </tr>
                                    {% endfor %}
                                    
                                    <!-- 新增项 -->
                                    <tr class="table-success">
                                        <td colspan="4" class="text-center">
                                            <strong>新增项</strong>
                                        </td>
                                    </tr>
                                    {% for i in "12345" %}
                                    <tr class="new-item-row">
                                        <td>
                                            <input type="text" class="form-control" name="new_name" placeholder="名称">
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="new_code" placeholder="代码">
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="new_tag" placeholder="标签">
                                        </td>
                                        <td>
                                            <input type="number" class="form-control" name="new_order" placeholder="排序">
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="form-group mt-3">
                            <button type="submit" class="btn btn-primary">保存所有更改</button>
                            <a href="{% url 'data_dictionary_tree' %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
{% else %}
    <div class="wrapper">
        <!-- 导航栏 -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- 左侧导航栏切换按钮 -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
            </ul>

            <!-- 右侧导航栏 -->
            <ul class="navbar-nav ml-auto">
                <!-- 用户信息下拉菜单 -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline">{{ user_nick|default:username }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> 个人资料
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{% url 'logout' %}" class="dropdown-item">
                            <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                        </a>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- 左侧边栏 -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- 品牌Logo -->
            <a href="#" class="brand-link">
                <span class="brand-text font-weight-light">物料清单管理系统</span>
            </a>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 用户面板 -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-light"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block">{{ user_nick|default:username }}</a>
                    </div>
                </div>

                <!-- 侧边栏菜单 -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'dashboard' %}" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>
                                    仪表盘
                                </p>
                            </a>
                        </li>
                        
                        <li class="nav-item menu-open">
                            <a href="#" class="nav-link active">
                                <i class="nav-icon fas fa-database"></i>
                                <p>
                                    基础数据管理
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{% url 'data_dictionary_tree' %}" class="nav-link active">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>数据字典</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'customer_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>客户信息</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 主体内容 -->
            <section class="content">
                <div class="container-fluid">
                    <!-- 消息提示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">批量编辑 - {{ parent_name }}</h3>
                            <div class="card-tools">
                                <a href="{% url 'data_dictionary_tree' %}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left"></i> 返回树形视图
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <form method="post" action="{% url 'data_dictionary_batch_edit' %}">
                                {% csrf_token %}
                                <input type="hidden" name="parent_id" value="{{ parent_id }}">
                                
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>名称</th>
                                                <th>代码</th>
                                                <th>标签</th>
                                                <th>排序</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 现有项 -->
                                            {% for item in dict_items %}
                                            <tr>
                                                <td>
                                                    <input type="hidden" name="dict_id" value="{{ item.datadictionary_id }}">
                                                    <input type="text" class="form-control" name="dict_name" value="{{ item.datadictionary_name }}" required>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control" name="dict_code" value="{{ item.datadictionary_code }}" required>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control" name="dict_tag" value="{{ item.datadictionary_tag }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="dict_order" value="{{ item.datadictionary_order }}">
                                                </td>
                                            </tr>
                                            {% endfor %}
                                            
                                            <!-- 新增项 -->
                                            <tr class="table-success">
                                                <td colspan="4" class="text-center">
                                                    <strong>新增项</strong>
                                                </td>
                                            </tr>
                                            {% for i in "12345" %}
                                            <tr class="new-item-row">
                                                <td>
                                                    <input type="text" class="form-control" name="new_name" placeholder="名称">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control" name="new_code" placeholder="代码">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control" name="new_tag" placeholder="标签">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="new_order" placeholder="排序">
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="form-group mt-3">
                                    <button type="submit" class="btn btn-primary">保存所有更改</button>
                                    <a href="{% url 'data_dictionary_tree' %}" class="btn btn-secondary">取消</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </div>

    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 动态添加新行
        $('.new-item-row').last().after(
            '<tr>' +
            '<td colspan="4" class="text-center">' +
            '<button type="button" class="btn btn-success btn-sm" id="add-more-row">' +
            '<i class="fas fa-plus"></i> 添加更多行' +
            '</button>' +
            '</td>' +
            '</tr>'
        );
        
        // 点击添加更多行按钮
        $('#add-more-row').on('click', function() {
            const newRow = 
                '<tr class="new-item-row">' +
                '<td><input type="text" class="form-control" name="new_name" placeholder="名称"></td>' +
                '<td><input type="text" class="form-control" name="new_code" placeholder="代码"></td>' +
                '<td><input type="text" class="form-control" name="new_tag" placeholder="标签"></td>' +
                '<td><input type="number" class="form-control" name="new_order" placeholder="排序"></td>' +
                '</tr>';
            
            $(this).closest('tr').before(newRow);
        });
    });
</script>
{% endblock %} 