import pandas as pd
import json
import os
import uuid
import re
import zipfile
from datetime import datetime
from django.db import transaction
from django.db.models import Max
from django.conf import settings
from django.utils import timezone
from pathlib import Path
from .models import Material, Bom, Customer, PropertiesType

def get_local_now():
    """获取本地时间（无时区信息）"""
    return datetime.now()


def get_process_codes_from_dictionary(process_names):
    """
    根据工序名称在数据字典中查找对应的工序代码

    Args:
        process_names: 工序名称字符串，以|分隔，如"机加|精车OP1|精车OP2"

    Returns:
        str: 对应的工序代码字符串，以|分隔，如"code1|code2|code3"
             如果某个工序名称找不到对应代码，则该位置为空字符串
    """
    if not process_names:
        return None

    try:
        from .models import Datadictionary

        # 分割工序名称
        process_list = [name.strip() for name in process_names.split('|') if name.strip()]
        if not process_list:
            return None

        # 获取所有工序的数据字典项
        try:
            # 首先获取processes根节点
            root = Datadictionary.objects.get(
                datadictionary_tag='processes',
                datadictionary_pid=0
            )

            # 获取所有工序子项
            process_dict_items = Datadictionary.objects.filter(
                datadictionary_pid=root.datadictionary_id
            ).values('datadictionary_name', 'datadictionary_code')

            # 创建名称到代码的映射
            name_to_code = {}
            for item in process_dict_items:
                if item['datadictionary_name'] and item['datadictionary_code']:
                    name_to_code[item['datadictionary_name']] = item['datadictionary_code']

        except Datadictionary.DoesNotExist:
            # 如果processes根节点不存在，返回空代码
            return '|'.join(['' for _ in process_list])

        # 为每个工序名称查找对应的代码
        code_list = []
        for process_name in process_list:
            code = name_to_code.get(process_name, '')  # 如果找不到，使用空字符串
            code_list.append(code)

        return '|'.join(code_list)

    except Exception as e:
        # 如果出错，返回与工序数量相同的空代码
        process_list = [name.strip() for name in process_names.split('|') if name.strip()]
        return '|'.join(['' for _ in process_list])


def extract_excel_image(excel_file_path, cell_address, customer=None):
    """
    从Excel文件中提取指定单元格的DISPIMG函数引用的图片
    
    DISPIMG函数工作原理：
    1. Excel单元格中的DISPIMG函数格式为: =DISPIMG("ID_XXXXX",1)
    2. ID_XXXXX在xl/cellimages.xml文件中对应<xdr:cNvPr>标签的name属性
    3. 在相同<etc:cellImage>元素中的<a:blip>标签的r:embed属性包含关系ID(如rId1)
    4. xl/_rels/cellimages.xml.rels文件将关系ID映射到实际的图片文件路径
    
    Args:
        excel_file_path: Excel文件路径
        cell_address: 单元格地址，如'M5'
        customer: 客户对象，用于构建存储路径
        
    Returns:
        str: 保存后的图片路径，相对于MEDIA_URL
    """
    try:
        # 基础导入目录
        base_import_dir = os.path.join(settings.MEDIA_ROOT, 'import')
        os.makedirs(base_import_dir, exist_ok=True)
        
        # 构建包含客户名称和日期的路径
        today = get_local_now().strftime('%Y%m%d')
        customer_name = "default"
        if customer:
            # 使用客户代码作为文件夹名称，避免中文路径问题
            customer_name = customer.customer_code
        
        # 创建客户目录和日期目录
        customer_dir = os.path.join(base_import_dir, customer_name)
        os.makedirs(customer_dir, exist_ok=True)
        
        date_dir = os.path.join(customer_dir, today)
        os.makedirs(date_dir, exist_ok=True)
        
        print(f"尝试从Excel文件{excel_file_path}的单元格{cell_address}中提取DISPIMG图片")
        
        # 从Excel文件(ZIP)中提取图片
        try:
            # 使用pandas读取Excel来获取单元格内容，指定dtype=str保持原始格式
            df = pd.read_excel(excel_file_path, engine='openpyxl', header=None, dtype=str)
            
            # 解析单元格地址
            col_letter = ''
            row_num = 0
            for char in cell_address:
                if char.isalpha():
                    col_letter += char
                elif char.isdigit():
                    row_num = row_num * 10 + int(char)
            
            # 将列字母转换为数字索引 (A=0, B=1, etc.)
            col_idx = 0
            if col_letter:
                for c in col_letter.upper():
                    col_idx = col_idx * 26 + (ord(c) - ord('A'))
            
            # 获取单元格值 (DataFrame行索引从0开始，所以需要-1来转换，因为Excel行号从1开始)
            cell_row_idx = row_num - 1
            if cell_row_idx < 0 or cell_row_idx >= len(df) or col_idx >= len(df.columns):
                print(f"单元格索引超出范围: 行={cell_row_idx}, 列={col_idx}")
                return None
            
            cell_value = df.iloc[cell_row_idx, col_idx]
            print(f"单元格 {cell_address} 的值: {cell_value}")
            
            # 处理单元格值为 None 或 nan 的情况
            if pd.isna(cell_value) or cell_value is None:
                print(f"单元格 {cell_address} 值为空")
                return None
            
            # 检查单元格值是否包含DISPIMG函数
            if not isinstance(cell_value, str) or "DISPIMG" not in cell_value:
                print(f"单元格 {cell_address} 不包含DISPIMG函数")
                return None
            
            # 提取DISPIMG函数中的ID
            image_id_match = re.search(r'DISPIMG\("(ID_[^"]+)"', cell_value)
            if not image_id_match:
                print(f"无法从DISPIMG函数中提取图片ID: {cell_value}")
                return None
            
            image_id = image_id_match.group(1)
            print(f"从单元格 {cell_address} 提取到图片ID: {image_id}")
            
            # 将Excel文件作为ZIP打开
            with zipfile.ZipFile(excel_file_path, 'r') as zip_ref:
                # 首先检查xl/cellimages.xml文件是否存在
                if "xl/cellimages.xml" not in zip_ref.namelist():
                    print("在Excel文件中未找到cellimages.xml文件")
                    return None
                
                # 读取cellimages.xml文件
                cellimages_content = zip_ref.read("xl/cellimages.xml").decode('utf-8', errors='ignore')
                print("成功读取cellimages.xml文件")
                
                # 查找图片ID对应的cellImage元素和embed ID
                # XML格式：<xdr:cNvPr name="ID_XXX"/><...><a:blip r:embed="rIdX"/>
                cell_image_match = re.search(rf'<xdr:cNvPr[^>]*name="{image_id}".*?<a:blip[^>]*r:embed="(rId\d+)"', 
                                            cellimages_content, re.DOTALL)
                
                if not cell_image_match:
                    print(f"在cellimages.xml中未找到图片ID {image_id} 对应的元素")
                    # 为了调试，打印cellimages.xml的一部分内容
                    print("cellimages.xml内容片段:")
                    print(cellimages_content[:500])
                    return None
                
                embed_id = cell_image_match.group(1)
                print(f"找到图片ID {image_id} 对应的嵌入ID: {embed_id}")
                
                # 检查cellimages.xml.rels文件是否存在
                if "xl/_rels/cellimages.xml.rels" not in zip_ref.namelist():
                    print("在Excel文件中未找到cellimages.xml.rels文件")
                    return None
                
                # 读取cellimages.xml.rels文件
                rels_content = zip_ref.read("xl/_rels/cellimages.xml.rels").decode('utf-8', errors='ignore')
                print("成功读取cellimages.xml.rels文件")
                
                # 查找embed ID对应的图片路径
                # XML格式：<Relationship Id="rIdX" Target="media/imageX.png" ...>
                target_match = re.search(rf'<Relationship[^>]*Id="{embed_id}"[^>]*Target="([^"]+)"', rels_content)
                
                if not target_match:
                    print(f"在cellimages.xml.rels中未找到嵌入ID {embed_id} 对应的目标文件")
                    # 为了调试，打印rels文件的一部分内容
                    print("cellimages.xml.rels内容片段:")
                    print(rels_content[:500])
                    return None
                
                # 获取图片路径，通常格式为"media/imageX.png"
                image_path = target_match.group(1)
                print(f"找到嵌入ID {embed_id} 对应的图片路径: {image_path}")
                
                # 构建完整的图片路径
                full_image_path = f"xl/{image_path}"
                if full_image_path not in zip_ref.namelist():
                    # 尝试不带"xl/"前缀
                    full_image_path = image_path
                    if full_image_path not in zip_ref.namelist():
                        print(f"在ZIP文件中未找到图片文件: {full_image_path} 或 {image_path}")
                        return None
                
                # 获取文件扩展名
                file_ext = os.path.splitext(full_image_path)[1].lower()
                if not file_ext:
                    file_ext = '.png'
                
                # 生成唯一文件名，使用原图片文件名作为前缀以便于识别
                original_filename = os.path.basename(full_image_path)
                file_name = f"{uuid.uuid4()}{file_ext}"
                save_path = os.path.join(date_dir, file_name)
                
                # 提取并保存文件
                try:
                    image_data = zip_ref.read(full_image_path)
                    if len(image_data) == 0:
                        print(f"图片文件 {full_image_path} 内容为空")
                        return None
                    
                    with open(save_path, 'wb') as f:
                        f.write(image_data)
                    
                    if not os.path.exists(save_path) or os.path.getsize(save_path) == 0:
                        print(f"图片文件保存失败或大小为0: {save_path}")
                        return None
                    
                    print(f"成功保存图片: {save_path}, 大小: {os.path.getsize(save_path)} 字节")
                    return f"/media/import/{customer_name}/{today}/{file_name}"
                    
                except Exception as e:
                    print(f"提取或保存图片文件时出错: {str(e)}")
                    return None
                    
        except Exception as e:
            print(f"处理DISPIMG函数图片时出错: {str(e)}")
            return None
            
    except Exception as e:
        print(f"提取图片失败: {str(e)}")
        return None


def parse_bom_excel(file_path, customer_id, user_id=None):
    """
    解析BOM Excel文件
    
    Args:
        file_path: Excel文件路径
        customer_id: 客户ID
        user_id: 当前登录用户ID
        
    Returns:
        tuple: (成功导入数量, 失败数量, 错误信息列表)
    """
    try:
        # 读取Excel文件，设置keep_default_na=False可以避免将空字符串转换为NaN
        # 设置dtype=str将所有数据当作字符串处理，避免浮点数转换问题
        df = pd.read_excel(file_path, keep_default_na=False, dtype=str)
        
        # 将空字符串手动转换为NaN，以便后续处理
        df = df.replace('', pd.NA)
        
        # 获取客户信息
        try:
            customer = Customer.objects.get(pk=customer_id)
        except Customer.DoesNotExist:
            return 0, 0, ["客户不存在"]

        # 预先获取用户信息，避免在循环中重复查询
        tech_manager_name = ""
        if user_id:
            try:
                from .models import User
                user = User.objects.get(user_id=user_id)
                tech_manager_name = user.user_nick or user.user_name
            except User.DoesNotExist:
                tech_manager_name = ""
        
        # 确定实际数据行范围
        # 筛选出所有A列不为空的行（第一列是关键标识符）
        valid_rows = df[~pd.isna(df.iloc[:, 0])]
        
        # 存储成功和失败的计数
        success_count = 0
        fail_count = 0
        error_messages = []
        
        # 存储所有节点的层级关系
        node_hierarchy = []
        
        # 存储上一个节点的信息，用于构建层级关系
        prev_nodes = {i: None for i in range(8)}  # 支持0-7级
        prev_level = -1
        
        # 记录每个级别的最后一个节点ID
        level_last_node_id = {i: None for i in range(8)}
        
        # 分批处理数据，避免长时间事务
        batch_size = 50  # 每批处理的行数
        total_rows = len(valid_rows)
        
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_rows = valid_rows.iloc[batch_start:batch_end]
            
            # 使用事务处理每一批数据
            try:
                with transaction.atomic():
                    # 遍历当前批次的行
                    for index, row in batch_rows.iterrows():
                        # 跳过A列为空的行
                        if pd.isna(row.iloc[0]):
                            continue
                        
                        try:
                            # 确定当前行的层级
                            level = None
                            for i in range(1, 9):  # 检查B(1)到I(8)列
                                if not pd.isna(row.iloc[i]) and str(row.iloc[i]).strip() == str(i-1):
                                    level = i-1
                                    break
                            
                            if level is None:
                                error_messages.append(f"第{index+1}行: 无法确定层级")
                                fail_count += 1
                                continue
                            
                            # 创建物料记录，传递预先获取的用户信息
                            try:
                                material_data = process_material_row(row, customer, file_path, index, user_id, tech_manager_name)
                                if material_data is None:
                                    error_messages.append(f"第{index+1}行: 物料数据处理失败")
                                    fail_count += 1
                                    continue
                            except ValueError as ve:
                                # 捕获具体的ValueError异常，如物料属性分类不存在
                                error_messages.append(f"第{index+1}行: {str(ve)}")
                                fail_count += 1
                                continue
                            except Exception as e:
                                # 捕获其他所有异常
                                error_messages.append(f"第{index+1}行: 物料数据处理失败 - {str(e)}")
                                fail_count += 1
                                continue
                            
                            material_id = material_data['material_id']
                            
                            # 创建BOM记录
                            bom_record = Bom()
                            bom_record.bom_level = level
                            bom_record.bom_material = material_id
                            bom_record.bom_num = row.iloc[11] # 数量 L列
                            try:
                                bom_record.bom_order= int(row.iloc[0]) # 排序 A列
                            except (ValueError, TypeError):
                                bom_record.bom_order= 99
                            
                            # 处理损耗率 U列 - 浮点型
                            try:
                                loss_rate_value = row.iloc[20]
                                if not pd.isna(loss_rate_value) and str(loss_rate_value).strip():
                                    bom_record.bom_lossrate = float(loss_rate_value)
                            except (ValueError, TypeError):
                                pass  # 如果转换失败，不设置该字段
                            
                            # 处理零件用量 S列 - 整型
                            try:
                                part_count_value = row.iloc[18]
                                if not pd.isna(part_count_value) and str(part_count_value).strip():
                                    bom_record.bom_partcount = int(float(part_count_value))
                            except (ValueError, TypeError):
                                pass  # 如果转换失败，不设置该字段
                            
                            # 处理生产数量 R列 - 整型
                            try:
                                produce_count_value = row.iloc[17]
                                if not pd.isna(produce_count_value) and str(produce_count_value).strip():
                                    bom_record.bom_producecount = int(float(produce_count_value))
                            except (ValueError, TypeError):
                                pass  # 如果转换失败，不设置该字段
                            
                            # 根据物料的临时状态设置BOM状态
                            # material_data中包含了物料的临时状态信息
                            if 'temp_state' in material_data:
                                # 设置对应的bom_state值
                                bom_record.bom_state = material_data['temp_state']
                            else:
                                # 默认为临时新增
                                bom_record.bom_state = 1
                            
                            # 设置导入用户ID
                            if user_id:
                                bom_record.bom_ImportUser = user_id

                            # 设置BOM时间字段
                            current_time = get_local_now()
                            if material_data.get('temp_state') == 1:  # 新增BOM
                                bom_record.bom_addTime = current_time
                                bom_record.bom_updateTime = current_time
                            else:  # 修改BOM
                                bom_record.bom_updateTime = current_time
                            
                            # 确定父节点ID
                            if level == 0:  # 顶级节点
                                bom_record.bom_pid = 0
                            else:
                                # 查找上一级父节点
                                parent_level = level - 1
                                parent_found = False
                                
                                while parent_level >= 0:
                                    if level_last_node_id[parent_level] is not None:
                                        bom_record.bom_pid = level_last_node_id[parent_level]
                                        parent_found = True
                                        break
                                    parent_level -= 1
                                
                                if not parent_found or bom_record.bom_pid is None:
                                    error_messages.append(f"第{index+1}行: 无法确定父节点")
                                    fail_count += 1
                                    continue
                            
                            # 保存BOM记录
                            bom_record.save()
                            
                            # 更新当前层级的最后一个节点ID
                            level_last_node_id[level] = bom_record.bom_id
                            
                            # 清空所有更高级别的最后节点记录
                            for i in range(level + 1, 8):
                                level_last_node_id[i] = None
                            
                            success_count += 1
                            
                        except Exception as e:
                            error_messages.append(f"第{index+1}行: {str(e)}")
                            fail_count += 1
            
            except Exception as batch_error:
                # 如果批处理失败，记录错误并继续下一批
                error_messages.append(f"批处理失败 (行 {batch_start+1} 到 {batch_end}): {str(batch_error)}")
                # 将批中的所有行标记为失败
                fail_count += (batch_end - batch_start)
                
                # 尝试重新连接数据库
                from django.db import connection
                connection.close()
                connection.connect()
        
        return success_count, fail_count, error_messages
    
    except Exception as e:
        # 确保关闭并重新连接数据库
        try:
            from django.db import connection
            connection.close()
            connection.connect()
        except:
            pass
        return 0, 0, [f"解析Excel文件失败: {str(e)}"]


def process_material_row(row, customer, excel_file_path, index, user_id=None, tech_manager_name=""):
    """
    处理Excel行中的物料数据

    Args:
        row: Excel中的一行数据
        customer: 客户对象
        excel_file_path: Excel文件路径，用于提取图片
        index: DataFrame的行索引
        user_id: 当前登录用户ID
        tech_manager_name: 预先获取的技术负责人姓名，避免重复查询

    Returns:
        dict: 包含物料ID和状态的字典
    """
    try:
        # 定义处理单元格值的函数
        def process_cell_value(value):
            if pd.isna(value):
                return None
            
            # 将值转为字符串并去除前后空白
            str_value = str(value).strip()

            # 处理特殊字符"/"或"—"为空值
            if str_value in ['/', '—', '-']:
                return None

            # 注意：对于工艺路线，"0"值会在上层逻辑中特殊处理，这里保留原始值
                
            # 处理数字格式，保持原始文本格式
            if isinstance(value, (int, float)):
                # 如果是整数，去掉小数点后的零
                if value == int(value):
                    str_value = str(int(value))
                # 如果以 .0 结尾，去掉 .0
                elif str_value.endswith('.0'):
                    str_value = str_value[:-2]
                    
            return str_value
        
        # 获取关键字段
        material_no = process_cell_value(row.iloc[9])  # J列: 物料编码
        drawing_no = process_cell_value(row.iloc[10])  # K列: 客户图号
        material_version = process_cell_value(row.iloc[16])  # Q列: 版本
        # 如果版本列为空，默认填写为"A"
        if material_version is None or material_version == "":
            material_version = "A"
        
        # 根据客户名称和图号查询数据库中的物料
        existing_material = None
        temp_state = 1  # 默认为新增
        temp_true_id = None
        
        if drawing_no:
            existing_materials = Material.objects.filter(
                material_drawingno=drawing_no,
                material_customer=customer.customer_code
            )
            
            if existing_materials.exists():
                # 如果有相同图号和客户的物料，再根据版本检查
                for mat in existing_materials:
                    if mat.material_version == material_version:
                        # 找到了完全匹配的物料
                        existing_material = mat
                        temp_state = 2  # 修改
                        temp_true_id = mat.material_id
                        break
        
        # 如果是修改现有物料(temp_state=2)，直接复制原有物料的所有数据
        if temp_state == 2 and existing_material:
            material = Material()
            
            # 从现有物料复制所有字段
            for field in Material._meta.fields:
                if field.name != 'material_id':
                    setattr(material, field.name, getattr(existing_material, field.name))
            
            # 设置临时状态字段
            material.material_tempstate = temp_state
            # 设置正确的临时ID字段，注意字段名区分大小写
            if temp_true_id:
                material.material_tempTrueId = temp_true_id  # 这是正确的字段名
            
            # 设置用户ID
            if user_id:
                material.material_tempUserId = user_id

            # 更新修改时间（对于修改现有物料的情况）
            material.material_updateTime = get_local_now()

            # 保存物料
            material.save()
            
            return {
                'material_id': material.material_id,
                'temp_state': temp_state
            }
        
        # 如果是新增物料(temp_state=1)，则从Excel中读取数据
        # M列: 图片 - 如果提供了Excel路径，尝试提取图片
        material_pic = None
        if excel_file_path:
            # 构造M列单元格地址，index是DataFrame的索引，所以需要+2（Excel从1开始计数+1，标题行占一行+1）
            cell_address = f"M{index + 2}"
            
            # 提取图片
            material_pic = extract_excel_image(excel_file_path, cell_address, customer=customer)
        
        material_name = process_cell_value(row.iloc[13])  # N列: 物料名称
        material_quality = process_cell_value(row.iloc[14])  # O列: 材质
        material_spec = process_cell_value(row.iloc[15])  # P列: 规格
        material_unit = process_cell_value(row.iloc[19])  # T列: 计量单位
        material_attr_name = process_cell_value(row.iloc[23])  # X列: 物料属性分类
        
        # 存货属性(Y-AF列)
        customer_supply = "是" if not pd.isna(row.iloc[24]) and str(row.iloc[24]).upper() == "Y" else "否"  # Y列: 客供
        outsourcing = "是" if not pd.isna(row.iloc[25]) and str(row.iloc[25]).upper() == "Y" else "否"  # Z列: 外购
        sales = "是" if not pd.isna(row.iloc[26]) and str(row.iloc[26]).upper() == "Y" else "否"  # AA列: 销售
        self_made = "是" if not pd.isna(row.iloc[27]) and str(row.iloc[27]).upper() == "Y" else "否"  # AB列: 自制
        production_consumption = "是" if not pd.isna(row.iloc[28]) and str(row.iloc[28]).upper() == "Y" else "否"  # AC列: 生产耗用
        virtual_item = "是" if not pd.isna(row.iloc[29]) and str(row.iloc[29]).upper() == "Y" else "否"  # AD列: 虚拟件
        subcontract = "否"  # 委外默认否
        batch_management = "是" if not pd.isna(row.iloc[30]) and str(row.iloc[30]).upper() == "Y" else "否"  # AE列: 批号管理
        cutting_size = process_cell_value(row.iloc[31])  # AF列: 下料尺寸
        
        # 供应商信息
        material_supp_casting = process_cell_value(row.iloc[32])  # AG列: 铸造
        material_supp_machining = process_cell_value(row.iloc[33])  # AH列: 机加
        material_supp_sheetmetal = process_cell_value(row.iloc[34])  # AI列: 钣金
        material_supp_purchase = process_cell_value(row.iloc[35])  # AJ列: 采购
        material_supp_purchase_manager = process_cell_value(row.iloc[36])  # AK列: 采购负责人
        
        # 工序清单(AM-BP列)
        processes = []
        for i in range(38, 68):  # AM(38)到BP(67)列
            value = process_cell_value(row.iloc[i])
            # 过滤掉值为"0"的工序
            if value and value != "0":
                processes.append(value)

        # 过滤掉末尾连续的"0"值，如 "检验|入库(原材料)|0|0|0|0"
        while processes and processes[-1] == "0":
            processes.pop()

        material_process = "|".join(processes) if processes else None
        
        # 初始化工艺详情变量
        material_process_times = None
        material_process_device = None
        material_process_price = None
        material_process_type = None
        material_process_outsupplier = None
        material_process_outprice = None
        material_process_important = None
        material_process_code = None

        # 如果工序列表为空或为0，尝试从AL列获取工艺路线编号并查询工艺路线内容
        if not material_process or material_process == "0":
            route_no = process_cell_value(row.iloc[37])  # AL(37)列
            if route_no:
                try:
                    from .models import ProcessRoute
                    process_route = ProcessRoute.objects.get(processroute_no=route_no)
                    material_process = process_route.processroute_content
                    # 同时获取工艺详情
                    material_process_times = process_route.processroute_times
                    material_process_device = process_route.processroute_device
                    material_process_price = process_route.processroute_price
                    material_process_type = process_route.processroute_type
                    material_process_outsupplier = process_route.processroute_outsupplier
                    material_process_outprice = process_route.processroute_outprice
                    material_process_important = process_route.processroute_important
                    material_process_code = process_route.processroute_code  # 新增：获取工序代码
                except ProcessRoute.DoesNotExist:
                    # 如果找不到对应的工艺路线，保持material_process为None
                    pass
        else:
            # 如果工序列表不为空，根据工序名称在数据字典中查找对应的工序代码
            if material_process:
                material_process_code = get_process_codes_from_dictionary(material_process)
        
        # 创建inventory的JSON字符串
        inventory = {
            "customer_supply": customer_supply,
            "outsourcing": outsourcing,
            "sales": sales,
            "self_made": self_made,
            "subcontract": subcontract,
            "production_consumption": production_consumption,
            "batch_management": batch_management,
            "virtual_item": virtual_item,
            "cutting_size": cutting_size or ""
        }
        
        # 检查物料属性分类是否存在
        material_attr = None
        material_attr_rule = None
        if material_attr_name:
            try:
                prop_type = PropertiesType.objects.get(propertiestype_name=material_attr_name)
                material_attr = prop_type.propertiestype_code
                material_attr_rule = prop_type.propertiesType_Rule
            except PropertiesType.DoesNotExist:
                # 抛出具体的异常，包含物料属性分类名称
                raise ValueError(f"物料属性分类 '{material_attr_name}' 不存在")
        
        # 如果没有物料编码且需要新增，生成新的编码
        if not material_no and temp_state == 1:
            if material_attr:
                # 中缀规则：如果是"客户码"则使用客户代码，否则使用规则中的值
                category_rule = material_attr_rule or '客户码'
                middle_code = customer.customer_code if category_rule == '客户码' else category_rule
                
                # 查询当前分类+中缀的最大编号
                prefix = f"{material_attr}{middle_code}"
                max_code = Material.objects.filter(
                    material_no__startswith=prefix
                ).aggregate(Max('material_no'))['material_no__max']
                
                if max_code and max_code.startswith(prefix):
                    try:
                        # 提取编号部分（最后4位）并加1
                        num = int(max_code[-4:]) + 1
                    except:
                        num = 1
                else:
                    num = 1
                
                # 生成新的物料编码：分类码+中缀+4位自增序号
                material_no = f"{prefix}{num:04d}"
        
        # 确定当前版本是否是最大版本
        is_max_version = True
        
        # 获取相同客户名称和图号的所有物料版本
        related_materials = Material.objects.filter(
            material_tempstate=0,
            material_customer=customer.customer_code,
            material_drawingno=drawing_no
        )
        
        # 判断是否有比当前版本更高的版本
        if related_materials.exists():
            # 分离字母版本和数字版本
            letter_versions = [m.material_version for m in related_materials if m.material_version and m.material_version.isalpha() and len(m.material_version) == 1]
            number_versions = [m.material_version for m in related_materials if m.material_version and m.material_version.isdigit()]
            
            # 判断当前版本是否是最大版本
            if material_version and material_version.isalpha() and len(material_version) == 1:
                # 如果是字母版本，与其他字母版本比较
                if letter_versions:
                    max_letter = max(letter_versions)
                    if material_version < max_letter:
                        is_max_version = False
                # 字母版本始终大于数字版本，所以不需要与数字版本比较
            elif material_version and material_version.isdigit():
                # 如果是数字版本，与其他数字版本比较
                if number_versions:
                    max_number = max([int(v) for v in number_versions])
                    if int(material_version) < max_number:
                        is_max_version = False
                # 如果存在字母版本，数字版本始终小于字母版本
                if letter_versions:
                    is_max_version = False
        
        # 创建新的物料记录
        material = Material()
        
        # 设置物料数据
        material.material_drawingno = drawing_no
        material.material_name = material_name
        material.material_no = material_no
        material.material_img = material_pic
        material.material_quality = material_quality
        material.material_spec = material_spec
        material.material_version = material_version
        material.material_unit = material_unit
        material.material_customer = customer.customer_code
        material.material_customername = customer.customer_name
        material.material_attr = material_attr
        material.material_attrname = material_attr_name
        material.material_inventory = json.dumps(inventory, ensure_ascii=False)
        material.material_supp_casting = material_supp_casting
        material.material_supp_machining = material_supp_machining
        material.material_supp_sheetmetal = material_supp_sheetmetal
        material.material_supp_purchase = material_supp_purchase
        material.material_supp_prochasemanager = material_supp_purchase_manager

        # 设置技术负责人（使用预先获取的用户信息）
        material.material_techmanager = tech_manager_name
        material.material_processroute = material_process
        # 设置工艺详情字段
        material.material_ProcessrouteTimes = material_process_times
        material.material_ProcessrouteDevice = material_process_device
        material.material_ProcessroutePrice = material_process_price
        material.material_ProcessrouteType = material_process_type
        material.material_ProcessrouteOutSupplier = material_process_outsupplier
        material.material_ProcessrouteOutPrice = material_process_outprice
        material.material_ProcessrouteImportant = material_process_important
        material.material_ProcessrouteCode = material_process_code  # 新增：保存工序代码
        # 使用本地时间（因为USE_TZ=False）
        material.material_uptime = get_local_now()
        material.material_state = 1 if is_max_version else 2  # 根据是否为最大版本设置状态

        # 设置时间字段
        current_time = get_local_now()
        if temp_state == 1:  # 新增物料
            material.material_addTime = current_time
            material.material_updateTime = current_time
        else:  # 修改物料
            material.material_updateTime = current_time
        
        # 设置临时状态字段
        material.material_tempstate = temp_state
        if temp_true_id:
            material.material_tempTrueId = temp_true_id  # 使用正确的字段名
        
        # 设置用户ID
        if user_id:
            material.material_tempUserId = user_id
        
        # 保存物料
        material.save()
        
        return {
            'material_id': material.material_id,
            'temp_state': temp_state
        }
    
    except Exception as e:
        print(f"处理物料行失败: {str(e)}")
        # 重新抛出异常，而不是返回None
        raise


def delete_temp_bom_data(user_id=None):
    """
    删除临时BOM数据
    
    Args:
        user_id: 当前登录用户ID，如果提供则只删除该用户导入的临时数据
        
    Returns:
        bool: 操作成功返回True，否则返回False
    """
    try:
        # 确保数据库连接是活跃的
        from django.db import connection
        connection.close()
        connection.connect()
        
        with transaction.atomic():
            # 根据用户ID筛选需要删除的数据
            if user_id:
                # 只删除当前用户导入的临时BOM数据
                Bom.objects.filter(bom_state__gt=0, bom_ImportUser=user_id).delete()
                
                # 只删除当前用户导入的临时物料数据
                Material.objects.filter(material_tempstate__gt=0, material_tempUserId=user_id).delete()
        return True
    except Exception as e:
        print(f"删除临时数据失败: {str(e)}")
        # 尝试重新连接数据库
        try:
            from django.db import connection
            connection.close()
            connection.connect()
        except:
            pass
        return False



