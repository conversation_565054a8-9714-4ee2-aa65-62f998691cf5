# BOM系统代码规范配置

# 项目设置
projectType: python
framework: django
linting: true

# 应用结构规则
appStructureRules:
  - 所有代码统一放在bom_sys应用中
  - 不创建额外的Django应用
  - 按功能模块组织代码，保持bom_sys应用内部结构清晰

# Python代码规范
pythonStyleGuide: pep8
maxLineLength: 120
tabSize: 4
useSpaces: true
pythonVersion: "3.10"
lintIgnorePatterns: ["**/migrations/*"]

# Django最佳实践
djangoStyleGuide:
  - 使用类视图而非函数视图
  - 模型字段定义时使用verbose_name
  - 表单字段使用label和help_text
  - 使用翻译字符串_()
  - urls.py中定义app_name
  - 使用Django的ORM而非原始SQL
  - 在views中使用Django的messages框架

# 前端代码规范
htmlStyleGuide:
  - 使用HTML5语法
  - 使用Bootstrap组件库
  - 遵循AdminLTE布局规范
  - 模板继承base.html
  - 块级元素使用适当的命名
  
cssStyleGuide:
  - 使用Bootstrap类优先
  - 自定义CSS放在static/css/style.css
  - 使用响应式布局

jsStyleGuide:
  - 函数使用驼峰命名法
  - 使用jQuery框架
  - 使用分号结束语句
  - 变量声明使用const或let

# 命名规范
namingConventions:
  - 模型类名使用单数形式
  - URL名称使用下划线
  - 视图函数/类使用下划线
  - 模板文件使用下划线
  - 静态文件使用下划线
  - 数据库表使用单数形式

# 文档规范
documentationRules:
  - 模型类添加docstring
  - 视图函数/类添加docstring
  - 复杂函数添加注释
  - README.md保持更新

# Git规范
gitRules:
  - 提交信息使用中文
  - 单一职责原则
  - 按功能模块提交

# 编辑器设置
editorConfig:
  - end_of_line: lf
  - insert_final_newline: true
  - trim_trailing_whitespace: true 