from django.shortcuts import render, redirect, get_object_or_404
from django.views import View
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q
from .models import Equipment

class EquipmentListView(View):
    """设备列表视图"""
    def get(self, request):
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')

        search_term = request.GET.get('search', '')
        equipment_list = Equipment.objects.all()
        
        # 搜索功能
        if search_term:
            equipment_list = equipment_list.filter(
                Q(equipment_no__icontains=search_term) | 
                Q(equipment_name__icontains=search_term) |
                Q(equipment_department__icontains=search_term) |
                Q(equipment_line__icontains=search_term)
            )
            
        # 排序
        equipment_list = equipment_list.order_by('equipment_id')
            
        context = {
            'equipment_list': equipment_list,
            'search_term': search_term,
        }
        return render(request, 'bom_sys/equipment_list.html', context)


class EquipmentAddView(View):
    """设备添加视图"""
    def get(self, request):
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        return render(request, 'bom_sys/equipment_add.html')

    def post(self, request):
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        # 获取表单数据
        equipment_department = request.POST.get('equipment_department', '')
        equipment_line = request.POST.get('equipment_line', '')
        equipment_no = request.POST.get('equipment_no', '')
        equipment_name = request.POST.get('equipment_name', '')
        equipment_spec = request.POST.get('equipment_spec', '')
        equipment_config = request.POST.get('equipment_config', '')
        equipment_entry_date = request.POST.get('equipment_entry_date') or None
        equipment_factory_no = request.POST.get('equipment_factory_no', '')
        equipment_origin = request.POST.get('equipment_origin', '')
        equipment_manufacturer = request.POST.get('equipment_manufacturer', '')
        equipment_mfg_date = request.POST.get('equipment_mfg_date') or None
        equipment_system_name = request.POST.get('equipment_system_name', '')
        equipment_contact = request.POST.get('equipment_contact', '')
        equipment_phone = request.POST.get('equipment_phone', '')
        equipment_process_range = request.POST.get('equipment_process_range', '')
        equipment_process_feature = request.POST.get('equipment_process_feature', '')
        equipment_quantity = request.POST.get('equipment_quantity') or 0
        equipment_planned_quantity = request.POST.get('equipment_planned_quantity') or 0
        equipment_rated_power = request.POST.get('equipment_rated_power') or 0
        equipment_aux_power = request.POST.get('equipment_aux_power') or 0
        equipment_simultaneous_rate = request.POST.get('equipment_simultaneous_rate') or 0
        equipment_config_power = request.POST.get('equipment_config_power') or 0

        # 创建新设备
        equipment = Equipment(
            equipment_department=equipment_department,
            equipment_line=equipment_line,
            equipment_no=equipment_no,
            equipment_name=equipment_name,
            equipment_spec=equipment_spec,
            equipment_config=equipment_config,
            equipment_entry_date=equipment_entry_date,
            equipment_factory_no=equipment_factory_no,
            equipment_origin=equipment_origin,
            equipment_manufacturer=equipment_manufacturer,
            equipment_mfg_date=equipment_mfg_date,
            equipment_system_name=equipment_system_name,
            equipment_contact=equipment_contact,
            equipment_phone=equipment_phone,
            equipment_process_range=equipment_process_range,
            equipment_process_feature=equipment_process_feature,
            equipment_quantity=equipment_quantity,
            equipment_planned_quantity=equipment_planned_quantity,
            equipment_rated_power=equipment_rated_power,
            equipment_aux_power=equipment_aux_power,
            equipment_simultaneous_rate=equipment_simultaneous_rate,
            equipment_config_power=equipment_config_power
        )
        equipment.save()
        
        messages.success(request, '设备添加成功')
        return redirect('equipment_list')


class EquipmentEditView(View):
    """设备编辑视图"""
    def get(self, request, equipment_id):
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        equipment = get_object_or_404(Equipment, equipment_id=equipment_id)
        context = {
            'equipment': equipment
        }
        return render(request, 'bom_sys/equipment_edit.html', context)

    def post(self, request, equipment_id):
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        equipment = get_object_or_404(Equipment, equipment_id=equipment_id)
        
        # 获取表单数据
        equipment.equipment_department = request.POST.get('equipment_department', '')
        equipment.equipment_line = request.POST.get('equipment_line', '')
        equipment.equipment_no = request.POST.get('equipment_no', '')
        equipment.equipment_name = request.POST.get('equipment_name', '')
        equipment.equipment_spec = request.POST.get('equipment_spec', '')
        equipment.equipment_config = request.POST.get('equipment_config', '')
        equipment.equipment_entry_date = request.POST.get('equipment_entry_date') or None
        equipment.equipment_factory_no = request.POST.get('equipment_factory_no', '')
        equipment.equipment_origin = request.POST.get('equipment_origin', '')
        equipment.equipment_manufacturer = request.POST.get('equipment_manufacturer', '')
        equipment.equipment_mfg_date = request.POST.get('equipment_mfg_date') or None
        equipment.equipment_system_name = request.POST.get('equipment_system_name', '')
        equipment.equipment_contact = request.POST.get('equipment_contact', '')
        equipment.equipment_phone = request.POST.get('equipment_phone', '')
        equipment.equipment_process_range = request.POST.get('equipment_process_range', '')
        equipment.equipment_process_feature = request.POST.get('equipment_process_feature', '')
        equipment.equipment_quantity = request.POST.get('equipment_quantity') or 0
        equipment.equipment_planned_quantity = request.POST.get('equipment_planned_quantity') or 0
        equipment.equipment_rated_power = request.POST.get('equipment_rated_power') or 0
        equipment.equipment_aux_power = request.POST.get('equipment_aux_power') or 0
        equipment.equipment_simultaneous_rate = request.POST.get('equipment_simultaneous_rate') or 0
        equipment.equipment_config_power = request.POST.get('equipment_config_power') or 0
        
        equipment.save()
        
        messages.success(request, '设备更新成功')
        return redirect('equipment_list')


class EquipmentDeleteView(View):
    """设备删除视图"""
    def post(self, request, equipment_id):
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')

        try:
            equipment = get_object_or_404(Equipment, equipment_id=equipment_id)
            equipment_name = equipment.equipment_name or equipment.equipment_no or f"ID:{equipment_id}"
            equipment.delete()

            messages.success(request, f'设备"{equipment_name}"删除成功')
            return redirect('equipment_list')
        except Exception as e:
            messages.error(request, f'删除设备失败: {str(e)}')
            return redirect('equipment_list')