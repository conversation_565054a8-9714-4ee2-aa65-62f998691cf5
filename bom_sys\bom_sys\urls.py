"""bom_sys URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView
from . import views
from .views import (
    LoginView, DashboardView, MaterialListView, MaterialQueryView, MaterialAddView,
    # ... existing views ...
    ImportBOMView,  # 新增导入BOM视图
    BOMImportPreviewView, # 新增导入BOM预览视图
    BomListNewView, # 新增BOM清单(新)视图
    BomTreeView, # 新增BOM树视图
    BomExportExcelView, # 新增BOM导出Excel视图
)
from .views_equipment import EquipmentListView, EquipmentAddView, EquipmentEditView, EquipmentDeleteView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('dashboard/', views.DashboardView.as_view(), name='dashboard'),
    # 用户管理URL
    path('users/', views.UserListView.as_view(), name='user_list'),
    path('users/add/', views.UserAddView.as_view(), name='user_add'),
    path('users/edit/<int:user_id>/', views.UserEditView.as_view(), name='user_edit'),
    path('users/delete/<int:user_id>/', views.UserDeleteView.as_view(), name='user_delete'),
    # 个人资料URL
    path('profile/get/', views.ProfileView.as_view(), name='profile_get'),
    path('profile/edit/', views.ProfileView.as_view(), name='profile_edit'),
    # 基础数据管理URL
    path('data-dictionary/', views.DataDictionaryTreeView.as_view(), name='data_dictionary_list'),
    path('data-dictionary/add/', views.DataDictionaryAddView.as_view(), name='data_dictionary_add'),
    path('data-dictionary/edit/<int:dict_id>/', views.DataDictionaryEditView.as_view(), name='data_dictionary_edit'),
    path('data-dictionary/delete/<int:dict_id>/', views.DataDictionaryDeleteView.as_view(), name='data_dictionary_delete'),
    path('data-dictionary/tree/', views.DataDictionaryTreeView.as_view(), name='data_dictionary_tree'),
    path('data-dictionary/batch-edit/', views.DataDictionaryBatchEditView.as_view(), name='data_dictionary_batch_edit'),
    path('customers/', views.CustomerListView.as_view(), name='customer_list'),
    path('customers/add/', views.CustomerAddView.as_view(), name='customer_add'),
    path('customers/edit/<int:customer_id>/', views.CustomerEditView.as_view(), name='customer_edit'),
    path('customers/delete/<int:customer_id>/', views.CustomerDeleteView.as_view(), name='customer_delete'),
    # 物料属性URL
    path('properties-types/', views.PropertiesTypeListView.as_view(), name='properties_type_list'),
    path('properties-types/delete/<int:prop_id>/', views.PropertiesTypeDeleteView.as_view(), name='properties_type_delete'),
  
    # 物料管理URL
    path('materials/add/', views.MaterialAddView.as_view(), name='material_add'),
    path('materials/edit/<int:material_id>/', views.MaterialAddView.as_view(), name='material_edit'),
    path('materials/', views.MaterialListView.as_view(), name='material_list'),
    path('materials/query/', views.MaterialQueryView.as_view(), name='material_query'),
    path('materials/delete/<int:material_id>/', views.MaterialDeleteView.as_view(), name='material_delete'),
    path('get_max_material_code/', views.get_max_material_code, name='get_max_material_code'),
    path('check-duplicate-material/', views.check_duplicate_material, name='check_duplicate_material'),
    path('api/get_max_version/', views.get_max_version, name='get_max_version'),
    
    # BOM管理URL
    # BOM管理
    path('bom/', views.BomListView.as_view(), name='bom_list'),
    path('bom-new/', views.BomListNewView.as_view(), name='bom_list_new'),
    path('bom-tree/<int:bom_id>/', views.BomTreeView.as_view(), name='bom_tree_view'),
    path('get_customer_bom/', views.get_customer_bom, name='get_customer_bom'),
    path('get_bom_tree_data/', views.get_bom_tree_data, name='get_bom_tree_data'),

    # BOM导出Excel
    path('bom-export-excel/', BomExportExcelView.as_view(), name='bom_export_excel'),
    path('bom-export-excel-template2/', views.bom_export_excel_template2, name='bom_export_excel_template2'),

    # BOM节点删除
    path('delete_bom_node/', views.delete_bom_node, name='delete_bom_node'),
    
    # 文件预览URL
    path('view-file/<str:file_type>/<int:material_id>/', views.view_file, name='view_file'),
    
    # 工艺路线URL
    path('process-routes/', views.ProcessRouteListView.as_view(), name='process_route_list'),
    path('process-routes/add/', views.ProcessRouteAddView.as_view(), name='process_route_add'),
    path('process-routes/edit/<int:route_id>/', views.ProcessRouteEditView.as_view(), name='process_route_edit'),
    path('process-routes/delete/<int:route_id>/', views.ProcessRouteDeleteView.as_view(), name='process_route_delete'),
    # SMB路径获取API
    path('get_smb_base_path/', views.get_smb_base_path_api, name='get_smb_base_path'),

    # 生成资料文件夹路径API
    path('generate_datafolder_path/', views.generate_datafolder_path_api, name='generate_datafolder_path'),

    # 物料绑定相关
    path('search_materials_by_customer/', views.search_materials_by_customer, name='search_materials_by_customer'),
    path('search_materials_for_bind/', views.search_materials_for_bind, name='search_materials_for_bind'),
    path('bind_material_to_bom/', views.bind_material_to_bom, name='bind_material_to_bom'),
    path('bind_material_to_temp_bom/', views.bind_material_to_temp_bom, name='bind_material_to_temp_bom'),

    # BOM结构操作相关
    path('move_bom_node/', views.move_bom_node, name='move_bom_node'),
    path('delete_bom_node_new/', views.delete_bom_node_new, name='delete_bom_node_new'),
    path('add_bom_child_node/', views.add_bom_child_node, name='add_bom_child_node'),

    # 物料删除影响检查
    path('check_material_delete_impact/', views.check_material_delete_impact, name='check_material_delete_impact'),
    
    # 导入BOM
    path('import_bom/', ImportBOMView.as_view(), name='import_bom'),
    path('bom_import_preview/<int:customer_id>/', BOMImportPreviewView.as_view(), name='bom_import_preview'),
    path('confirm_bom_import/', views.confirm_bom_import, name='confirm_bom_import'),
    
    # 设备管理路由
    path('equipment/list/', EquipmentListView.as_view(), name='equipment_list'),
    path('equipment/add/', EquipmentAddView.as_view(), name='equipment_add'),
    path('equipment/edit/<int:equipment_id>/', EquipmentEditView.as_view(), name='equipment_edit'),
    path('equipment/delete/<int:equipment_id>/', EquipmentDeleteView.as_view(), name='equipment_delete'),

    # 系统日志管理
    path('system/log/', views.SystemLogView.as_view(), name='system_log'),

    # 下载导入模板
    path('download_import_template/', views.download_import_template, name='download_import_template'),

    # 默认URL重定向
    path('', RedirectView.as_view(url='login/', permanent=False)),
]

# 添加静态文件URL配置（仅在DEBUG模式下）
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
