{% extends 'base.html' %}
{% load static %}

{% block title %}编辑设备{% endblock %}

{% block content %}
<!-- Content Header -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>编辑设备</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">首页</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'equipment_list' %}">设备管理</a></li>
                    <li class="breadcrumb-item active">编辑设备</li>
                </ol>
            </div>
        </div>
    </div>
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">设备信息</h3>
                    </div>
                    <!-- /.card-header -->
                    <form method="post" id="equipment-form">
                        {% csrf_token %}
                        <div class="card-body">
                            <div class="row">
                                <!-- 基本信息 -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_department">使用部门</label>
                                        <input type="text" class="form-control" id="equipment_department" name="equipment_department" value="{{ equipment.equipment_department }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_line">线组别</label>
                                        <input type="text" class="form-control" id="equipment_line" name="equipment_line" value="{{ equipment.equipment_line }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_no">设备编号</label>
                                        <input type="text" class="form-control" id="equipment_no" name="equipment_no" value="{{ equipment.equipment_no }}" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_name">设备名称</label>
                                        <input type="text" class="form-control" id="equipment_name" name="equipment_name" value="{{ equipment.equipment_name }}" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_spec">规格型号</label>
                                        <input type="text" class="form-control" id="equipment_spec" name="equipment_spec" value="{{ equipment.equipment_spec }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_config">配置</label>
                                        <input type="text" class="form-control" id="equipment_config" name="equipment_config" value="{{ equipment.equipment_config }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_entry_date">入厂日期</label>
                                        <input type="date" class="form-control" id="equipment_entry_date" name="equipment_entry_date" value="{{ equipment.equipment_entry_date|date:'Y-m-d' }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_factory_no">出厂编号</label>
                                        <input type="text" class="form-control" id="equipment_factory_no" name="equipment_factory_no" value="{{ equipment.equipment_factory_no }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_origin">进口/国产</label>
                                        <select class="form-control" id="equipment_origin" name="equipment_origin">
                                            <option value="domestic" {% if equipment.equipment_origin == 'domestic' %}selected{% endif %}>国产</option>
                                            <option value="imported" {% if equipment.equipment_origin == 'imported' %}selected{% endif %}>进口</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_manufacturer">制造商(品牌)</label>
                                        <input type="text" class="form-control" id="equipment_manufacturer" name="equipment_manufacturer" value="{{ equipment.equipment_manufacturer }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_mfg_date">制造日期</label>
                                        <input type="date" class="form-control" id="equipment_mfg_date" name="equipment_mfg_date" value="{{ equipment.equipment_mfg_date|date:'Y-m-d' }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_system_name">设备系统名称</label>
                                        <input type="text" class="form-control" id="equipment_system_name" name="equipment_system_name" value="{{ equipment.equipment_system_name }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_contact">联系人</label>
                                        <input type="text" class="form-control" id="equipment_contact" name="equipment_contact" value="{{ equipment.equipment_contact }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_phone">电话</label>
                                        <input type="text" class="form-control" id="equipment_phone" name="equipment_phone" value="{{ equipment.equipment_phone }}">
                                    </div>
                                </div>
                                
                                <!-- 加工信息 -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="equipment_process_range">加工范围</label>
                                        <textarea class="form-control" id="equipment_process_range" name="equipment_process_range" rows="2">{{ equipment.equipment_process_range }}</textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="equipment_process_feature">加工特点</label>
                                        <textarea class="form-control" id="equipment_process_feature" name="equipment_process_feature" rows="2">{{ equipment.equipment_process_feature }}</textarea>
                                    </div>
                                </div>
                                
                                <!-- 数量功率信息 -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_quantity">数量/台</label>
                                        <input type="number" class="form-control" id="equipment_quantity" name="equipment_quantity" value="{{ equipment.equipment_quantity }}" min="0">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_planned_quantity">预设数量</label>
                                        <input type="number" class="form-control" id="equipment_planned_quantity" name="equipment_planned_quantity" value="{{ equipment.equipment_planned_quantity }}" min="0">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_rated_power">额定功率(KW)</label>
                                        <input type="number" class="form-control" id="equipment_rated_power" name="equipment_rated_power" value="{{ equipment.equipment_rated_power }}" min="0" step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_aux_power">辅助功率(KW)</label>
                                        <input type="number" class="form-control" id="equipment_aux_power" name="equipment_aux_power" value="{{ equipment.equipment_aux_power }}" min="0" step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_simultaneous_rate">同时率</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="equipment_simultaneous_rate" name="equipment_simultaneous_rate" value="{{ equipment.equipment_simultaneous_rate }}" min="0" max="100">
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="equipment_config_power">配置功率(KW)</label>
                                        <input type="number" class="form-control" id="equipment_config_power" name="equipment_config_power" value="{{ equipment.equipment_config_power }}" min="0" step="0.01">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- /.card-body -->
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{% url 'equipment_list' %}" class="btn btn-default">取消</a>
                        </div>
                    </form>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
 
    });
</script>
{% endblock %} 
