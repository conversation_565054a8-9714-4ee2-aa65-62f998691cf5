{% extends 'base.html' %}
{% load static %}

{% block title %}设备管理{% endblock %}

{% block content %}
<!-- Content Header -->


<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">设备列表</h3>
                        <div class="card-tools">
                            <form method="get" class="form-inline">
                                <div class="input-group input-group-sm" style="width: 150px;">
                                    <input type="text" name="search" class="form-control float-right" placeholder="搜索" value="{{ search_term }}">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div class="mb-3">
                            <a href="{% url 'equipment_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 添加设备
                            </a>
                        </div>
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>使用部门</th>
                                    <th>线组别</th>
                                    <th>设备编号</th>
                                    <th>设备名称</th>
                                    <th>规格型号</th>
                                    <th>进口/国产</th>
                                    <th>数量/台</th>
                                    <th>预投数量</th>
                                    <th>设备总数</th>
                                    <th>合计功率(KW)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for equipment in equipment_list %}
                                <tr>
                                    <td>{{ equipment.equipment_id }}</td>
                                    <td>{{ equipment.equipment_department }}</td>
                                    <td>{{ equipment.equipment_line }}</td>
                                    <td>{{ equipment.equipment_no }}</td>
                                    <td>{{ equipment.equipment_name }}</td>
                                    <td>{{ equipment.equipment_spec }}</td>
                                    <td>
                                        {% if equipment.equipment_origin == 'domestic' %}
                                            国产
                                        {% elif equipment.equipment_origin == 'imported' %}
                                            进口
                                        {% else %}
                                            {{ equipment.equipment_origin }}
                                        {% endif %}
                                    </td>
                                    <td>{{ equipment.equipment_quantity }}</td>
                                    <td>{{ equipment.equipment_planned_quantity }}</td>
                                    <td>{{ equipment.total_quantity }}</td>
                                    <td>{{ equipment.total_power }}</td>
                                    <td>
                                        <a href="{% url 'equipment_edit' equipment.equipment_id %}" class="btn btn-info btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm delete-btn" data-toggle="modal" data-target="#delete-modal" data-id="{{ equipment.equipment_id }}" data-name="{{ equipment.equipment_name }}">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="12" class="text-center">暂无设备数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>

<!-- 删除确认模态框 -->
<div class="modal fade" id="delete-modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">确认删除</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除设备"<span id="equipment-name"></span>"吗？</p>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <form id="delete-form" method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 删除确认
        $('.delete-btn').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            $('#equipment-name').text(name);
            $('#delete-form').attr('action', '{% url "equipment_delete" 0 %}'.replace('0', id));
        });

        // 处理删除表单提交
        $('#delete-form').on('submit', function(e) {
            e.preventDefault();

            var form = $(this);
            var actionUrl = form.attr('action');

            // 显示加载状态
            var submitBtn = form.find('button[type="submit"]');
            var originalText = submitBtn.text();
            submitBtn.prop('disabled', true).text('删除中...');

            // 发送AJAX请求
            $.ajax({
                url: actionUrl,
                type: 'POST',
                data: form.serialize(),
                success: function(response) {
                    // 关闭模态框
                    $('#delete-modal').modal('hide');
                    // 刷新页面
                    window.location.reload();
                },
                error: function(xhr, status, error) {
                    // 恢复按钮状态
                    submitBtn.prop('disabled', false).text(originalText);

                    // 显示错误信息
                    alert('删除失败，请重试。错误信息：' + (xhr.responseText || error));
                }
            });
        });
    });
</script>
{% endblock %}