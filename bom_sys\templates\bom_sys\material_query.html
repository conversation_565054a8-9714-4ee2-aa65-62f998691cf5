{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}物料查询{% endblock %}

{% block content %}
<div class="">
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- 搜索条件 -->
            <div class="card card-primary card-outline mb-3">
                <div class="card-body">
                    <form method="get" action="{% url 'material_query' %}" id="searchForm">
                        {% if is_iframe %}
                        <input type="hidden" name="iframe" value="1">
                        {% endif %}
                        <div class="row align-items-end">
                            <div class="col-md-2">
                                <div class="form-group mb-0">
                                    <label for="material_code">物料编码</label>
                                    <input type="text" class="form-control" id="material_code" name="material_code" value="{{ query_params.material_code }}" placeholder="支持模糊查询">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group mb-0">
                                    <label for="drawing_number">客户图号</label>
                                    <input type="text" class="form-control" id="drawing_number" name="drawing_number" value="{{ query_params.drawing_number }}" placeholder="支持模糊查询">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-0">
                                    <label for="customer_id">客户名称</label>
                                    <select class="form-control select2" id="customer_id" name="customer_id" data-placeholder="请选择或输入客户名称">
                                        <option value="">全部</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.customer_code }}" {% if query_params.customer_id == customer.customer_code %}selected{% endif %}>{{ customer.customer_name }}({{ customer.customer_code }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group mb-0">
                                    <label for="material_state">图纸状态</label>
                                    <select class="form-control" id="material_state" name="material_state">
                                        <option value="1" {% if query_params.material_state == '1' or not query_params.material_state %}selected{% endif %}>启用</option>
                                        <option value="-1" {% if query_params.material_state == '-1' %}selected{% endif %}>全部</option>
                                        <option value="2" {% if query_params.material_state == '2' %}selected{% endif %}>停用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-0">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="button" id="clearSearch" class="btn btn-secondary ml-2">
                                        <i class="fas fa-eraser"></i> 清空条件
                                    </button>
                                    <input type="hidden" name="per_page" value="{{ query_params.per_page|default:'10' }}">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 物料列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">物料查询结果</h3>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>物料编码</th>
                                <th>客户图号</th>
                                <th>客户名称</th>
                                <th>版本</th>
                                <th>图纸状态</th>
                                <th>客户图纸</th>
                                <th>生产图纸</th>
                                <th>工艺图纸</th>
                                <th>检测单</th>
                                <th>资料文件夹</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for material in materials %}
                            <tr>
                                <td>{{ material.material_no }}</td>
                                <td>{{ material.material_drawingno }}</td>
                                <td>
                                    {% for customer in customers %}
                                        {% if customer.customer_code == material.material_customer %}
                                            {{ customer.customer_name }}({{ customer.customer_code }})
                                        {% endif %}
                                    {% endfor %}
                                </td>
                                <td>{{ material.material_version }}</td>
                                <td>
                                    {% if material.material_state == 1 %}
                                        <span class="badge badge-success">启用</span>
                                    {% elif material.material_state == 2 %}
                                        <span class="badge badge-secondary">停用</span>
                                    {% else %}
                                        <span class="badge badge-light">未知</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if material.material_drawing_customer %}
                                    <a href="{% url 'view_file' 'customer' material.material_id %}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-file-pdf"></i> 预览
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无文件</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if material.material_drawing_finished %}
                                    <a href="{% url 'view_file' 'finished' material.material_id %}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-file-pdf"></i> 预览
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无文件</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if material.material_drawing_workmanship %}
                                    <a href="{% url 'view_file' 'workmanship' material.material_id %}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-file-pdf"></i> 预览
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无文件</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if material.material_drawing_testingform %}
                                    <a href="{% url 'view_file' 'testingform' material.material_id %}" target="_blank" class="btn btn-sm btn-warning">
                                        <i class="fas fa-file-excel"></i> 下载
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无文件</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary open-folder-btn" 
                                            data-customer="{{ material.material_customer }}" 
                                            data-customername="{% for c in customers %}{% if c.customer_code == material.material_customer %}{{ c.customer_name }}{% endif %}{% endfor %}"
                                            data-attr="{{ material.material_attr }}" 
                                            data-attrname="{{ material.material_attrname }}"
                                            data-no="{{ material.material_no }}" 
                                            data-drawingno="{{ material.material_drawingno }}" 
                                            data-name="{{ material.material_name }}" 
                                            data-version="{{ material.material_version }}">
                                        <i class="fas fa-folder-open"></i> 打开
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="10" class="text-center">没有找到符合条件的物料记录</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <!-- 分页 -->
                <div class="card-footer clearfix">
                    <ul class="pagination pagination-sm m-0 float-right">
                        {% if materials.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                    首页
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ materials.previous_page_number }}{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                    上一页
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">首页</a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#">上一页</a>
                            </li>
                        {% endif %}
                        
                        {% for i in materials.paginator.page_range %}
                            {% if materials.number == i %}
                                <li class="page-item active">
                                    <a class="page-link" href="#">{{ i }}</a>
                                </li>
                            {% elif i > materials.number|add:'-3' and i < materials.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                        {{ i }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if materials.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ materials.next_page_number }}{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                    下一页
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ materials.paginator.num_pages }}{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                    末页
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                    <div class="float-left">
                        显示 {{ materials.start_index }} 到 {{ materials.end_index }} 条，共 {{ materials.paginator.count }} 条记录
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}
{% block extra_css %}
<!-- Select2 CSS -->
<link href="/static/css/select2.min.css" rel="stylesheet" />
<link href="/static/css/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<link rel="stylesheet" href="/static/css/jquery-ui.css">
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="/static/js/select2.min.js"></script>
<script src="/static/js/jquery-ui.min.js"></script>
<script>
    // 定义全局变量，供material_query.js使用
    var GENERATE_DATAFOLDER_PATH_URL = '{% url "generate_datafolder_path" %}';
</script>
<script src="/static/js/material_query.js"></script>
{% endblock %} 