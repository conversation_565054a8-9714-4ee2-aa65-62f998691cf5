/**
 * 物料查询页面的JavaScript功能
 */
$(document).ready(function() {
    
    // 强制绑定点击事件（使用事件委托）
    $(document).on('click', '.open-folder-btn', function(e) {
        
        e.preventDefault();
        
        // 获取按钮上的数据属性
        const customerCode = $(this).data('customer');
        const customerName = $(this).data('customername');
        const categoryCode = $(this).data('attr');
        const categoryName = $(this).data('attrname');
        const materialCode = $(this).data('no');
        const drawingNumber = $(this).data('drawingno');
        const materialName = $(this).data('name');
        const version = $(this).data('version');
        

        
        // 检查必要数据是否存在
        let missingFields = [];
        
        if (!customerCode || !customerName) {
            missingFields.push('客户信息');
        }
        
        if (!categoryCode || !categoryName) {
            missingFields.push('物料属性分类');
        }
        
        if (!drawingNumber) {
            missingFields.push('客户图号');
        }
        
        if (!materialCode) {
            missingFields.push('物料编码');
        }
        
        if (!materialName) {
            missingFields.push('物料名称');
        }
        
        if (!version) {
            missingFields.push('版本');
        }
        
        // 如果有缺失字段，提示用户
        if (missingFields.length > 0) {
            const missingFieldsText = missingFields.join('、');
            alert(`缺少${missingFieldsText}，无法打开资料文件夹`);
            return;
        }
        
        // 显示加载状态
        const $btn = $(this);
        const originalHtml = $btn.html();
        $btn.html('<i class="fas fa-spinner fa-spin"></i> 处理中...');
        $btn.prop('disabled', true);
        
        // 通过AJAX请求服务端API生成资料文件夹路径
        $.ajax({
            url: GENERATE_DATAFOLDER_PATH_URL, // 这个变量会在HTML中定义
            type: 'GET',
            data: {
                material_customer: customerCode,
                material_customername: customerName,
                material_attr: categoryCode,
                material_attrname: categoryName,
                material_no: materialCode,
                material_drawingno: drawingNumber,
                material_name: materialName,
                material_version: version
            },
            dataType: 'json',
            success: function(response) {

                if (response.success && response.universal_link) {
                    // 打开生成的链接
                    window.location.href = response.universal_link;
                } else {
                    // 显示错误信息
                    alert(response.message || '生成资料文件夹路径失败，请联系管理员');
                }
                // 恢复按钮状态
                $btn.html(originalHtml);
                $btn.prop('disabled', false);
            },
            error: function(xhr, status, error) {
                console.error('生成资料文件夹路径请求失败:', error);
                alert('无法连接到服务器，请稍后重试');
                // 恢复按钮状态
                $btn.html(originalHtml);
                $btn.prop('disabled', false);
            }
        });
    });

    // 初始化客户名称下拉框
    $('#customer_id').select2({
        theme: 'bootstrap-5',
        width: '100%',
        allowClear: true,
        tags: true, // 允许用户输入不在列表中的值
        placeholder: '请选择或输入客户名称',
        language: {
            noResults: function() {
                return "没有找到匹配的客户";
            },
            searching: function() {
                return "搜索中...";
            }
        },
        // 启用搜索功能
        minimumInputLength: 0,
        // 自定义匹配方法，同时匹配名称和代码
        matcher: function(params, data) {
            // 如果没有搜索词，返回所有数据
            if ($.trim(params.term) === '') {
                return data;
            }
            
            // 搜索词转为小写
            var term = params.term.toLowerCase();
            
            // 获取选项文本和代码
            var text = data.text.toLowerCase();
            var code = $(data.element).data('code') ? $(data.element).data('code').toLowerCase() : '';
            
            // 如果名称或代码包含搜索词，返回该数据
            if (text.indexOf(term) > -1 || code.indexOf(term) > -1) {
                return data;
            }
            
            // 否则返回null表示不匹配
            return null;
        }
    });
    
    // 清空搜索条件按钮
    $('#clearSearch').on('click', function() {
        // 清空所有输入框和下拉框
        $('#material_code').val('');
        $('#drawing_number').val('');
        $('#customer_id').val('').trigger('change');
        $('#material_state').val('1'); // 默认设为启用状态
        
        // 保留每页显示数量和iframe参数
        const perPage = $('input[name="per_page"]').val();
        const isIframe = $('input[name="iframe"]').val();
        
        
        // 恢复每页显示数量
        $('input[name="per_page"]').val(perPage);
        
        // 如果有iframe参数，恢复它
        if(isIframe) {
            $('input[name="iframe"]').val(isIframe);
        }
        
        // 恢复状态为启用
        $('#material_state').val('1');
        
        // 提交表单
        $('#searchForm').submit();
    });

    // 确保页面第一次加载时默认状态为"启用"
    $(window).on('load', function() {
        if (!window.location.search || window.location.search === '?iframe=1') {
            // 如果没有查询参数或只有iframe参数，设置状态为启用并提交表单
            $('#material_state').val('1');
            if (!window.location.search) {
                $('#searchForm').submit();
            }
        }
    });
    
    // 修复分页时搜索条件丢失问题
    $('a.page-link').each(function() {
        var href = $(this).attr('href');
        if (href && !href.startsWith('#')) {
            // 确保URL中包含所有搜索参数
            var searchParams = new URLSearchParams(window.location.search);
            var pageParams = new URLSearchParams(href);
            
            // 保留page参数
            var page = pageParams.get('page');
            
            // 重建URL，确保包含所有当前参数
            var newUrl = '?';
            if (page) {
                newUrl += 'page=' + page;
            }
            
            // 添加所有其他参数
            searchParams.forEach(function(value, key) {
                if (key !== 'page') {
                    // 特殊处理material_state为空字符串的情况
                    if (key === 'material_state' && value === '') {
                        newUrl += '&' + key + '=';
                    } else if (value) {
                        newUrl += '&' + key + '=' + value;
                    }
                }
            });
            
            $(this).attr('href', newUrl);
        }
    });
});
