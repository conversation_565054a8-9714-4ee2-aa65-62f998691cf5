from django import template

register = template.Library()

@register.filter
def none_to_empty(value):
    """将None值和0值转换为空字符串"""
    if value is None or value == 'None' or value == 'null' or value == '0':
        return ''
    return value

@register.filter
def safe_default(value, default=''):
    """安全的默认值过滤器，处理None值和0值"""
    if value is None or value == 'None' or value == 'null' or value == '' or value == '0':
        return default
    return value
