using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Reflection;

namespace FolderOpener
{
    class Program
    {
        // 获取应用程序当前运行的目录
        private static string GetApplicationDirectory()
        {
            try
            {
                return Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            }
            catch
            {
                return Directory.GetCurrentDirectory();
            }
        }

        // 自定义URL解码方法（以防WebUtility类不可用）
        private static string UrlDecode(string text)
        {
            try
            {
                // 尝试使用WebUtility.UrlDecode
                return WebUtility.UrlDecode(text);
            }
            catch
            {
                // 如果WebUtility不可用，使用简单的替换方法
                text = text.Replace("+", " ");
                
                // 替换%XX编码
                Regex regex = new Regex("%([0-9A-F]{2})");
                return regex.Replace(text, match =>
                {
                    try
                    {
                        byte[] bytes = new byte[1];
                        bytes[0] = Convert.ToByte(match.Groups[1].Value, 16);
                        return Encoding.UTF8.GetString(bytes);
                    }
                    catch
                    {
                        return match.Value; // 如果转换失败，保留原样
                    }
                });
            }
        }
        
        static void Main(string[] args)
        {
            string appDirectory = GetApplicationDirectory();
            string logPath = Path.Combine(appDirectory, "debug_log.txt");
            
            try
            {
                // 清空并开始新日志
                File.WriteAllText(logPath, "C# 应用程序启动\r\n");
                File.AppendAllText(logPath, string.Format("应用程序目录: {0}\r\n", appDirectory));
                
                // 检查是否有参数
                if (args.Length == 0)
                {
                    File.AppendAllText(logPath, "错误: 没有提供参数\r\n");
                    return;
                }
                
                // 记录原始参数
                File.AppendAllText(logPath, string.Format("收到参数: {0}\r\n", string.Join(" ", args)));
                
                string input = args[0];
                
                // 从协议URL中提取路径
                string path = input;
                if (path.StartsWith("universallink://", StringComparison.OrdinalIgnoreCase))
                {
                    path = path.Substring("universallink://".Length);
                }
                else if (path.StartsWith("universallink:/", StringComparison.OrdinalIgnoreCase))
                {
                    path = path.Substring("universallink:/".Length);
                }
                
                File.AppendAllText(logPath, string.Format("提取的路径: {0}\r\n", path));
                
                // 处理特殊标记
                path = path.Replace("___OP___", "(")
                           .Replace("___CP___", ")")
                           .Replace("___SP___", " ")
                           .Replace("$COLON$", ":")
                           .Replace("$LPAREN$", "(")
                           .Replace("$RPAREN$", ")");
                
                File.AppendAllText(logPath, string.Format("处理标记后: {0}\r\n", path));
                
                // 修复盘符格式
                Match driveMatch = Regex.Match(path, @"^([a-zA-Z])[\\/](.+)$");
                if (driveMatch.Success)
                {
                    string driveLetter = driveMatch.Groups[1].Value;
                    string restOfPath = driveMatch.Groups[2].Value;
                    path = string.Format("{0}:/{1}", driveLetter, restOfPath);
                    File.AppendAllText(logPath, string.Format("修复盘符: {0}\r\n", path));
                }
                
                // URL解码
                string decodedPath = UrlDecode(path);
                File.AppendAllText(logPath, string.Format("URL解码后: {0}\r\n", decodedPath));
                
                // 将正斜杠转换为反斜杠
                decodedPath = decodedPath.Replace('/', '\\');
                File.AppendAllText(logPath, string.Format("转换为反斜杠: {0}\r\n", decodedPath));
                
                // 如果路径存在，则打开它
                File.AppendAllText(logPath, string.Format("即将执行: explorer.exe \"{0}\"\r\n", decodedPath));
                
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = string.Format("\"{0}\"", decodedPath),
                    UseShellExecute = true
                };
                
                Process.Start(psi);
                
                File.AppendAllText(logPath, "执行成功\r\n");
            }
            catch (Exception ex)
            {
                // 记录任何异常
                File.AppendAllText(logPath, string.Format("错误: {0}\r\n{1}\r\n", ex.Message, ex.StackTrace));
            }
        }
    }
} 