/**
 * 物料清单表单处理逻辑
 */
$(document).ready(function() {
    // 初始化Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // 初始化图片预览功能
    if (typeof initImagePreview === 'function') {
        initImagePreview();
    }
    
    // 为已有的产品图片添加点击预览功能
    $('#productImagePreview img.image-preview').on('click', function() {
        const imgSrc = $(this).attr('src');
        $('#previewImage').attr('src', imgSrc);
        $('#imagePreviewOverlay').addClass('active');
    });

    // 显示模式指示器
    $('#modeIndicator').fadeIn(500);
    
    // 检测来源页面并设置隐藏字段
    const referrer = document.referrer;
    let sourceType = 'direct'; // 默认为直接访问
    
    if (referrer.includes('/materials/')) {
        sourceType = 'material_list';
    }
    
    // 如果表单中没有来源字段，添加一个
    if (!$('#sourceType').length) {
        $('<input>').attr({
            type: 'hidden',
            id: 'sourceType',
            name: 'sourceType',
            value: sourceType
        }).appendTo('#materialBomForm');
    } else {
        $('#sourceType').val(sourceType);
    }
    
    // 页面加载完成后，移除可能存在的保存遮罩层
    removeSavingMessage();
    
    // 检查URL参数是否包含success参数
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('success')) {
        // 获取success参数的值
        const isSuccess = urlParams.get('success') === 'true';
        
        if (isSuccess) {
            // 发送消息通知其他标签页刷新物料列表
            localStorage.setItem('refreshMaterialList', Date.now().toString());
            
            // 显示成功消息
            showNotification('保存成功', '物料信息已成功保存', 'success');
        } else {
            // 显示失败消息
            showNotification('保存失败', '物料信息保存失败，请检查输入', 'error');
        }
        
        // 清除URL参数
        const newUrl = window.location.pathname;
        history.pushState({}, '', newUrl);
    }
    
    // 监听localStorage变化，用于接收其他标签页的刷新通知
    window.addEventListener('storage', function(e) {
        if (e.key === 'refreshMaterialList') {
            // 如果当前页面是物料列表页面，刷新内容
            if (window.location.pathname.includes('/materials/')) {
                // 延迟一点执行刷新，避免频繁刷新
                setTimeout(function() {
                    location.reload();
                }, 500);
            }
        }
    });
    
    // 初始化存货属性滑块开关
    initInventorySwitches();
    
    // 初始化Bootstrap标签页
    // 直接使用jQuery方式绑定事件
    $('#materialTabs button[data-bs-toggle="tab"]').on('click', function (e) {
        e.preventDefault();
        var target = $(this).data('bs-target');
        
        // 移除所有标签页的active类
        $('#materialTabs button').removeClass('active');
        $('.tab-pane').removeClass('show active');
        
        // 添加当前标签页的active类
        $(this).addClass('active');
        $(target).addClass('show active');
    });

    // 标签页切换逻辑
    $('#nextTab').on('click', function() {
        // 找到当前活动的标签页
        const activeTab = $('#materialTabs button.active');
        // 找到下一个标签页
        const nextTab = activeTab.parent().next().find('button');
        if (nextTab.length) {
            // 获取目标面板
            var target = nextTab.data('bs-target');
            
            // 移除所有标签页的active类
            $('#materialTabs button').removeClass('active');
            $('.tab-pane').removeClass('show active');
            
            // 添加当前标签页的active类
            nextTab.addClass('active');
            $(target).addClass('show active');
        }
    });

    $('#prevTab').on('click', function() {
        // 找到当前活动的标签页
        const activeTab = $('#materialTabs button.active');
        // 找到上一个标签页
        const prevTab = activeTab.parent().prev().find('button');
        if (prevTab.length) {
            // 获取目标面板
            var target = prevTab.data('bs-target');
            
            // 移除所有标签页的active类
            $('#materialTabs button').removeClass('active');
            $('.tab-pane').removeClass('show active');
            
            // 添加当前标签页的active类
            prevTab.addClass('active');
            $(target).addClass('show active');
        }
    });

    
    // 创建新增版本按钮函数
    function createNewVersionButton() {
        // 先移除可能已存在的按钮
        $('.new-version-btn').remove();
        
        // 如果是编辑模式，添加按钮
        if ($('#formMode').val() === 'edit') {
            const newVersionButton = $('<button type="button" class="btn btn-outline-success ml-2 new-version-btn"><i class="fas fa-code-branch mr-2"></i>新增版本</button>');
            $('button[type="submit"]').after(newVersionButton);
            
            // 绑定点击事件
            newVersionButton.on('click', function() {
                if (confirm('确定要创建新版本吗？将保留当前信息并自动更新版本号。')) {
                    createNewVersion();
                }
            });
        }
    }
    
    // 初始调用一次，根据当前模式创建按钮
    createNewVersionButton();
    
    // 客户图号和客户名称查询功能 - 使用失去焦点事件
    let lastDrawingNumber = $('#drawingNumber').val().trim(); // 记录上一次查询的图号
    let lastCustomerName = $('#customerName').val(); // 记录上一次查询的客户名称
    let lastVersion = $('#version').val(); // 记录上一次查询的版本号
    let originalVersion = $('#version').val(); // 保存原始版本号，用于编辑模式下的比较
    
    // 创建查询函数
    function queryMaterial() {
        const drawingNumber = $('#drawingNumber').val().trim();
        const customerName = $('#customerName').val();
        // 如果图号和客户名称都为空，或与上次查询的相同，则不执行查询
        if (!drawingNumber || !customerName || (drawingNumber === lastDrawingNumber && customerName === lastCustomerName)) {
            return;
        }
        
        // 更新上次查询的值
        lastDrawingNumber = drawingNumber;
        lastCustomerName = customerName;
        
        // 显示加载状态
        $('#modeIndicator').removeClass('mode-new mode-edit').addClass('mode-loading');
        $('#modeText').html('<i class="fas fa-spinner fa-spin mr-2"></i>正在查询...');

        // 发送AJAX请求
        $.ajax({
            url: window.location.pathname,
            type: 'GET',
            data: {
                drawing_number: drawingNumber,
                customer_name: customerName
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success && response.material) {
                    // 查询成功，切换到编辑模式
                    $('#modeIndicator').removeClass('mode-new mode-loading').addClass('mode-edit');
                    $('#modeText').text('编辑模式');
                    
                    // 填充表单数据
                    fillFormWithMaterial(response.material);
                    
                    // 更新表单模式
                    $('#formMode').val('edit');
                    
                    // 更新提交按钮文本
                    $('button[type="submit"]').html('<i class="fas fa-save mr-2"></i>保存修改');
                    
                    // 创建新增版本按钮
                    createNewVersionButton();
                    
                    // 显示成功消息
                    showNotification('查询成功', '已加载物料信息，可以进行编辑', 'success');
                } else {
                    // 查询失败，恢复新增模式
                    $('#modeIndicator').removeClass('mode-loading mode-edit').addClass('mode-new');
                    $('#modeText').text('新增模式');
                    
                    // 如果当前是编辑模式，清空除了客户名称和客户图号外的所有表单信息
                    if ($('#formMode').val() === 'edit') {
                        clearFormExceptCustomerInfo();
                        
                        // 更新表单模式
                        $('#formMode').val('add');
                        $('#edit_id').val('');
                        // 更新提交按钮文本
                        $('button[type="submit"]').html('<i class="fas fa-save mr-2"></i>提交保存');
                        
                        // 移除新增版本按钮
                        $('.new-version-btn').remove();
                    }
                    
                    // 显示错误消息
                    if (response.message && response.message !== '未找到匹配的物料信息') {
                        showNotification('未找到物料', response.message, 'warning');
                    } else {
                        showNotification('未找到物料', '未找到匹配的物料信息，已清空表单', 'warning');
                    }
                }
            },
            error: function(xhr, status, error) {
                // 查询失败，恢复新增模式
                $('#modeIndicator').removeClass('mode-loading mode-edit').addClass('mode-new');
                $('#modeText').text('新增模式');
                
                // 显示错误消息
                showNotification('查询失败', '服务器错误，请稍后重试', 'error');

            }
        });
    }
    
    // 添加版本号变更时的重复检查
    function checkDuplicateMaterial() {
        const drawingNumber = $('#drawingNumber').val().trim();
        const customerName = $('#customerName').val();
        const version = $('#version').val();
        const materialId = $('#formMode').val() === 'edit' ? window.location.pathname.split('/').pop() : null;
        const isEditMode = $('#formMode').val() === 'edit';
        
        // 如果任一字段为空，则不执行检查
        if (!drawingNumber || !customerName || !version) {
            return;
        }
        
        // 如果是编辑模式，并且版本没有改变，则不执行检查
        if (isEditMode && version === originalVersion) {
            // 清除验证错误
            $('#version').removeClass('is-invalid');
            $('#version').next('.invalid-feedback').remove();
            return;
        }
        
        // 如果与上次检查的值相同，则不执行检查
        if (drawingNumber === lastDrawingNumber && customerName === lastCustomerName && version === lastVersion) {
            return;
        }
        
        // 更新上次检查的值
        lastDrawingNumber = drawingNumber;
        lastCustomerName = customerName;
        lastVersion = version;
        
        // 发送AJAX请求检查是否存在重复记录
        $.ajax({
            url: '/check-duplicate-material/',
            type: 'GET',
            data: {
                drawing_number: drawingNumber,
                customer_name: customerName,
                version: version,
                material_id: materialId
            },
            success: function(response) {
                if (response.duplicate) {
                    // 显示重复警告
                    showNotification('重复警告', `已存在相同的物料记录！客户名称：${customerName}，客户图号：${drawingNumber}，版本：${version}，物料编码：${response.material_no}`, 'warning');
                    
                    // 添加表单验证错误
                    $('#version').addClass('is-invalid');
                    if (!$('#version').next('.invalid-feedback').length) {
                        $('#version').after('<div class="invalid-feedback">已存在相同的物料记录，请修改版本号</div>');
                    }
      } else {
                    // 清除验证错误
                    $('#version').removeClass('is-invalid');
                    $('#version').next('.invalid-feedback').remove();
                }
            },
            error: function(xhr, status, error) {
                console.error('检查重复物料失败:', error);
            }
        });
    }
    
    // 清空除了客户名称和客户图号外的所有表单信息
    function clearFormExceptCustomerInfo() {
        // 设置填充标志，防止触发不必要的编码生成
        isFillingForm = true;
        
        // 保存客户名称和客户图号
        const customerName = $('#customerName').val();
        const drawingNumber = $('#drawingNumber').val();

        // 重置表单
        $('#materialBomForm')[0].reset();
        
        // 明确重置每个文本字段，但保留客户名称和客户图号
        $('#materialCode').val('');
        $('#materialName').val('');
        $('#version').val('');
        $('#material_quality').val('');
        $('#materialSpec').val('');
        $('#lossRate').val('');
        $('#quantity').val('');
        $('#productionQuantity').val('');
        $('#partUsage').val('');
        $('#responsible').val('');
        $('#castingSupplier').val('');
        $('#machiningSupplier').val('');
        $('#sheetMetalSupplier').val('');
        $('#procurementSupplier').val('');
        $('#processNumber').val('');
        $('#cuttingSize').val('');
        
        $('#customerName').val(customerName);
        $('#drawingNumber').val(drawingNumber);
        
        // 重置其他Select2控件
        if ($.fn.select2) {
            $('#materialCategory').val('').trigger('change');
            $('#semifinishedStatus').val('').trigger('change');
            $('#unit').val('').trigger('change');
            $('#workshop').val('').trigger('change');
        }
        
        // 重置单选按钮
        $('input[name=customerSupply][value="否"]').prop('checked', true);
        $('input[name=outsourcing][value="否"]').prop('checked', true);
        $('input[name=sales][value="否"]').prop('checked', true);
        $('input[name=selfMade][value="否"]').prop('checked', true);
        $('input[name=subcontract][value="否"]').prop('checked', true);
        $('input[name=productionConsumption][value="否"]').prop('checked', true);
        $('input[name=batchManagement][value="否"]').prop('checked', true);
        $('input[name=virtualItem][value="否"]').prop('checked', true);
        // 重置存货属性滑块开关
        const switchIds = [
            'customerSupply',
            'outsourcing',
            'sales',
            'selfMade',
            'subcontract',
            'productionConsumption',
            'batchManagement',
            'virtualItem'
        ];
        
        // 将所有滑块开关重置为"否"
        switchIds.forEach(function(id) {
            // 重置复选框状态
            $(`#${id}`).prop('checked', false);
            
            // 更新显示文本
            const $text = $(`#${id}Text`);
            $text.text('否');
            $text.removeClass('text-success').addClass('text-muted');
            
            // 更新隐藏字段值
            $(`input[name="${id}Hidden"]`).val('否');
        });
        
        // 清空工艺路线内容
        $('#process_content').val('');
        $('#selected_processes').empty();
        $('#empty_process_message').show();
        $('#selected_processes').hide();
        
        // 清除所有预览
        $('.file-list, #productImagePreview').empty();
        
        // 设置当前日期
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        $('#uploadDate').val(`${yyyy}-${mm}-${dd}`);
        
        // 清除所有验证状态
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        // 重置版本号
        originalVersion = '';
        
        // 如果是编辑模式，切换回新增模式
        if ($('#formMode').val() === 'edit') {
            $('#formMode').val('add');
            $('#edit_id').val('');

            $('#modeIndicator').removeClass('mode-edit').addClass('mode-new');
            $('#modeText').text('新增模式');
            
            // 更新提交按钮文本
            $('button[type="submit"]').html('<i class="fas fa-save mr-2"></i>提交保存');
            
            // 移除新增版本按钮
            $('.new-version-btn').remove();
        }
        
        // 重置填充标志，允许后续手动修改触发编码生成
        isFillingForm = false;
    }
    
    // 客户图号失去焦点时触发查询
    $('#drawingNumber').on('blur', function() {
        // 只有当客户名称也已填写时才查询
        if ($('#customerName').val()) {
            queryMaterial();
        }
    });
    
    // 客户名称改变时触发查询
    $('#customerName').on('change', function() {
        // 只有当客户图号也已填写时才查询
        if ($('#drawingNumber').val().trim()) {
            queryMaterial();
        }
    });
    
    // 版本号失去焦点时检查重复
    $('#version').on('blur', function() {
        // 只有当客户名称和客户图号也已填写时才检查
        if ($('#customerName').val() && $('#drawingNumber').val().trim()) {
            checkDuplicateMaterial();
        }
    });
    
    // 添加回车键触发失去焦点事件
    $('#drawingNumber, #version').on('keypress', function(e) {
        if (e.which === 13) { // 回车键
            $(this).blur();
            e.preventDefault();
        }
    });
    
    // 填充表单数据
    function fillFormWithMaterial(material) {
        // 设置填充标志，防止触发不必要的编码生成
        isFillingForm = true;
        
        // 设置表单模式
        $('#formMode').val('edit');
        
        $('#edit_id').val(material.material_id);
        // 填充基本信息
        $('#materialCode').val(material.material_no);
        $('#materialName').val(material.material_name);
        $('#version').val(material.material_version);
        // 更新原始版本号
        originalVersion = material.material_version;
        $('#material_quality').val(material.material_quality);
        $('#materialSpec').val(material.material_spec);
        $('#lossRate').val(material.material_lossrate);
        $('#quantity').val(material.material_count);
        $('#productionQuantity').val(material.material_producecount);
        $('#partUsage').val(material.material_partcount);
        $('#responsible').val(material.material_supp_prochasemanager);
        
        // 设置下拉选择框
        $('#customerName').val(material.customer_name).trigger('change');
        $('#materialCategory').val(material.material_attr).trigger('change');
        $('#semifinishedStatus').val(material.material_semistate).trigger('change');
        $('#unit').val(material.material_unit).trigger('change');
        $('#workshop').val(material.material_workshop).trigger('change');
        
        // 填充供应商信息
        $('#castingSupplier').val(material.material_supp_casting);
        $('#machiningSupplier').val(material.material_supp_machining);
        $('#sheetMetalSupplier').val(material.material_supp_sheetmetal);
        $('#procurementSupplier').val(material.material_supp_purchase);
        
        // 填充工艺信息 - 注意不直接将工艺路线填入工艺序号，而是保持空白
        // $('#processNumber').val(material.material_processroute);
        $('#processNumber').val('');  // 工艺序号应该为空
        
        // 如果存在工艺路线内容，填充到工艺路线顺序区域
        if (material.material_processroute && material.material_processroute.trim() !== '') {
            $('#process_content').val(material.material_processroute);
            // 触发工艺路线内容更新，显示在工艺路线顺序区域
            if (typeof updateSelectedProcesses === 'function') {
                updateSelectedProcesses();
            } else {
                // 如果函数不存在，确保工艺路线内容的隐藏字段被设置
                $('#process_content').val(material.material_processroute);
                // 隐藏空提示
                $('#empty_process_message').hide();
                $('#selected_processes').show();
                
                // 手动处理工艺步骤显示
                const $container = $('#selected_processes');
                $container.empty();
                
                const content = $('#process_content').val();
                if (content) {
                    const processes = content.split('|');
                    
                    // 添加工艺步骤和箭头
                    processes.forEach((process, index) => {
                        // 添加步骤
                        const $step = $(`
                            <div class="process-step" data-process="${process}">
                                ${process}
                                <i class="fas fa-times remove-process"></i>
                            </div>
                        `);
                        
                        $container.append($step);
                        
                        // 如果不是最后一个，添加箭头
                        if (index < processes.length - 1) {
                            $container.append(`<span class="process-arrow"><i class="fas fa-long-arrow-alt-right"></i></span>`);
                        }
                    });
                }
            }
        }
        
        // 设置存货属性的单选按钮
        if (material.inventory_attrs) {
            setRadioValue('customerSupply', material.inventory_attrs.customer_supply === '是');
            setRadioValue('outsourcing', material.inventory_attrs.outsourcing === '是');
            setRadioValue('sales', material.inventory_attrs.sales === '是');
            setRadioValue('selfMade', material.inventory_attrs.self_made === '是');
            setRadioValue('subcontract', material.inventory_attrs.subcontract === '是');
            setRadioValue('productionConsumption', material.inventory_attrs.production_consumption === '是');
            setRadioValue('batchManagement', material.inventory_attrs.batch_management === '是');
            $('#cuttingSize').val(material.inventory_attrs.cutting_size);
        }
        
        // 显示产品图片（如果有）
        console.log('图片信息:', material.material_img, material.material_img_url);
        if (material.material_img_url) {
            console.log('添加图片到预览区域:', material.material_img_url);
            $('#productImagePreview').empty().append(`<img src="${material.material_img_url}" class="image-preview" alt="产品图片">`);
            
            // 添加隐藏字段保存原始图片路径，用于新增版本时复制
            if (!$('#original_image_path').length) {
                $('<input>').attr({
                    type: 'hidden',
                    id: 'original_image_path',
                    name: 'original_image_path',
                    value: material.material_img
                }).appendTo('#materialBomForm');
            } else {
                $('#original_image_path').val(material.material_img);
            }
            console.log('设置原始图片路径:', material.material_img);
            
            // 延迟一下，确保图片已加载到DOM中
            setTimeout(function() {
                // 初始化图片预览功能
                if (typeof initImagePreview === 'function') {
                    initImagePreview();
                }
            }, 100);
        }
        
        // 处理图纸相关字段

        
        // 客户图纸
        if (material.customer_drawing_url) {
            const fileName = material.material_drawing_customer.split('/').pop();
            $('#customerDrawingPreview').empty().append(`
                <div class="file-item">
                    <div>
                        <i class="fas fa-file-pdf mr-2"></i>
                        <a href="/view-file/customer/${material.material_id}/" target="_blank" class="file-link">${fileName}</a>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-file" data-field="material_drawing_customer">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `);
        }
        
        // 生产图纸
        if (material.production_drawing_url) {
            const fileName = material.material_drawing_finished.split('/').pop();
            $('#productionDrawingPreview').empty().append(`
                <div class="file-item">
                    <div>
                        <i class="fas fa-file-pdf mr-2"></i>
                         <a href="/view-file/finished/${material.material_id}/" target="_blank" class="file-link">${fileName}</a>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-file" data-field="material_drawing_finished">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `);
        }
        
        // 工艺图纸
        if (material.process_drawing_url) {
            const fileName = material.material_drawing_workmanship.split('/').pop();
            $('#processDrawingPreview').empty().append(`
                <div class="file-item">
                    <div>
                        <i class="fas fa-file-pdf mr-2"></i>
                        <a href="/view-file/workmanship/${material.material_id}/" target="_blank" class="file-link">${fileName}</a>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-file" data-field="material_drawing_workmanship">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `);
        }
        
        // 检测单
        if (material.inspection_sheet_files) {
            const fileName = material.material_drawing_testingform.split('/').pop();
            $('#inspectionSheetPreview').empty().append(`
                <div class="file-item">
                    <div>
                        <i class="fas fa-file-excel mr-2"></i>
                        <a href="${material.inspection_sheet_files}" target="_blank" class="file-link">${fileName}</a>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-file" data-field="material_drawing_testingform">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `);
        }
        
        
        // 修改表单提交地址
        const actionUrl = $('#materialBomForm').attr('action').replace(/\/materials\/add\/$/, `/materials/edit/${material.material_id}/`);
        $('#materialBomForm').attr('action', actionUrl);
        
        // 保存原始客户和分类值，用于后续比较
        originalCustomer = material.customer_name;
        originalCategory = material.material_attr;
        
        // 重置填充标志，允许后续手动修改触发编码生成
        isFillingForm = false;
        
        // 如果需要，可以在这里添加其他字段的填充
    }
    
    // 设置单选按钮值
    function setRadioValue(name, isYes) {
        if (isYes) {
            $(`input[name=${name}][value="是"]`).prop('checked', true);
        } else {
            $(`input[name=${name}][value="否"]`).prop('checked', true);
        }
    }
    
    // 显示通知消息
    function showNotification(title, message, type) {
        // 确保通知容器存在
        let container = $('.notification-container');
        if (container.length === 0) {
            container = $('<div class="notification-container"></div>');
            $('body').append(container);
        }
        
        // 创建通知元素
        const notification = $(`
            <div class="notification alert alert-${type} alert-dismissible fade show">
                <strong>${title}</strong> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `);
        
        // 添加到容器
        container.append(notification);
        
        // 自动关闭
        setTimeout(function() {
            notification.addClass('fade-out');
            setTimeout(function() {
                notification.remove();
            }, 300); // 等待动画完成
        }, 3000);
        
        // 点击关闭按钮时移除通知
        notification.find('.close').on('click', function() {
            notification.addClass('fade-out');
            setTimeout(function() {
                notification.remove();
            }, 300);
        });
    }
    
    // 物料编码生成逻辑
    function generateMaterialCode() {
        const customerSelect = $('#customerName');
        const materialCategory = $('#materialCategory');
        
        if (customerSelect.val() && materialCategory.val()) {
            const customerOption = customerSelect.find('option:selected');
            const customerCode = customerOption.data('code') || '';
            const categoryOption = materialCategory.find('option:selected');
            const categoryCode = categoryOption.val() || '';
            const categoryRule = categoryOption.data('rule') || '客户码';
            
            // 中缀规则：如果是"客户码"则使用客户代码，否则使用规则中的值
            const middleCode = (categoryRule === '客户码') ? customerCode : categoryRule;
            
            // 通过AJAX查询最大的自增编号
            $.ajax({
                url: '/get_max_material_code/',
                type: 'GET',
                data: {
                    'category_code': categoryCode,
                    'customer_code': customerCode,
                    'middle_code': middleCode
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // 使用服务器返回的最大编号+1
                        const nextNumber = response.next_number;
                        const code = `${categoryCode}${middleCode}${nextNumber}`;
                        $('#materialCode').val(code);
                    } else {
                        // 如果查询失败，使用默认值0001
                        const addnum = "0001";
                        const code = `${categoryCode}${middleCode}${addnum}`;
                        $('#materialCode').val(code);
                    }
                },
                error: function() {
                    // 如果请求失败，使用默认值0001
                    const addnum = "0001";
                    const code = `${categoryCode}${middleCode}${addnum}`;
                    $('#materialCode').val(code);
                }
            });
        }
    }
    
    // 保存原始值，用于比较变化
    let originalCustomer = '';
    let originalCategory = '';
    
    // 页面加载完成后保存原始值
    $(document).ready(function() {
        // 如果是编辑模式，保存原始值
        if ($('#formMode').val() === 'edit') {
            originalCustomer = $('#customerName').val();
            originalCategory = $('#materialCategory').val();
        }
    });
    
    // 标记是否正在填充表单数据（用于防止填充过程中触发不必要的编码生成）
    let isFillingForm = false;
    
    // 当客户或物料类别改变时，根据条件重新生成编码
    $('#customerName, #materialCategory').on('change', function() {
        // 如果正在填充表单，不执行编码生成
        if (isFillingForm) {
            return;
        }
        
        const isEditMode = $('#formMode').val() === 'edit';
        const currentCustomer = $('#customerName').val();
        const currentCategory = $('#materialCategory').val();


        // 在新增模式下始终重新生成编码
        // 在编辑模式下，只有当客户名称或物料分类发生变化时才重新生成
        if (!isEditMode || 
            (isEditMode && (currentCustomer !== originalCustomer || currentCategory !== originalCategory))) {
            generateMaterialCode();
            
            // 更新原始值
            if (isEditMode && currentCategory !== "") {
                originalCustomer = currentCustomer;
                originalCategory = currentCategory;
            }
        }
    });

    // 单文件上传处理 - 通用函数
    function handleSingleFileUpload(inputId, previewId) {
        $(`#${inputId}`).on('change', function() {
            const file = this.files[0];
            if (file) {
                // 清除之前的预览
                $(`#${previewId}`).empty();
                
                // 检查文件是否为图像类型
                if (file.type.match('image.*')) {
                    // 调用图像预览函数
                    showImagePreview(file, $(`#${previewId}`));
                    return;
                }
                
                // 确定文件图标
                let fileIcon = 'fa-file';
                if (file.name.endsWith('.pdf')) {
                    fileIcon = 'fa-file-pdf';
                } else if (file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
                    fileIcon = 'fa-file-word';
                } else if (file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) {
                    fileIcon = 'fa-file-excel';
                } else if (file.name.endsWith('.dwg')) {
                    fileIcon = 'fa-drafting-compass';
                }
                
                // 创建文件项
                const fileItem = $(`
                    <div class="file-item">
                        <div>
                            <i class="fas ${fileIcon} mr-2"></i>
                            <span>${file.name}</span>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger remove-file">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);
                
                // 添加到预览区域
                $(`#${previewId}`).append(fileItem);
                
                // 绑定删除按钮事件
                fileItem.find('.remove-file').on('click', function() {
                    $(`#${inputId}`).val('');
                    $(`#${previewId}`).empty();
                });
            }
        });
    }
    
    // 初始化各个文件上传组件
    handleSingleFileUpload('customerDrawing', 'customerDrawingPreview');
    handleSingleFileUpload('productionDrawing', 'productionDrawingPreview');
    handleSingleFileUpload('processDrawing', 'processDrawingPreview');
    handleSingleFileUpload('inspectionSheet', 'inspectionSheetPreview');

    // 使用通用文件上传初始化产品图片（这里会处理所有的点击、拖放和change事件）
    setupFileUpload('productImage', 'productImagePreview', showImagePreview, false);
    
    // 处理资料文件夹按钮点击事件
    $('#documentFolderBtn').on('click', function() {
        // 获取所需数据
        const customerSelect = $('#customerName');
        const materialCategory = $('#materialCategory');
        const drawingNumber = $('#drawingNumber').val().trim();
        const materialCode = $('#materialCode').val().trim();
        const materialName = $('#materialName').val().trim();
        const version = $('#version').val().trim();
        
        // 检查必要数据是否存在
        let missingFields = [];
        
        if (!customerSelect.val()) {
            missingFields.push('客户名称');
        }
        
        if (!materialCategory.val()) {
            missingFields.push('物料属性分类');
        }
        
        if (!drawingNumber) {
            missingFields.push('客户图号');
        }
        
        if (!materialCode || materialCode.length < 4) {
            missingFields.push('物料编码（需至少4位）');
        }
        
        if (!materialName) {
            missingFields.push('物料名称');
        }
        
        if (!version) {
            missingFields.push('版本');
        }
        
        // 如果有缺失字段，提示用户
        if (missingFields.length > 0) {
            const missingFieldsText = missingFields.join('、');
            showNotification('缺少必要信息', `缺少${missingFieldsText}，请完善表单后再打开`, 'warning');
            return;
        }
        
        // 获取客户代码和客户名称
        const customerOption = customerSelect.find('option:selected');
        const customerCode = customerOption.data('code') || '';
        const customerName = customerOption.val();
        
        // 获取物料属性代码和名称
        const categoryOption = materialCategory.find('option:selected');
        const categoryCode = categoryOption.val();
        const categoryName = categoryOption.text().split('(')[0].trim(); // 获取括号前的名称部分并去除空格
        
        // 显示加载状态
        showNotification('处理中', '正在生成资料文件夹路径，请稍候...', 'info');
        
        // 通过AJAX请求服务端API生成资料文件夹路径
        $.ajax({
            url: '/generate_datafolder_path/',
            type: 'GET',
            data: {
                material_customer: customerCode,
                material_customername: customerName,
                material_attr: categoryCode,
                material_attrname: categoryName,
                material_no: materialCode,
                material_drawingno: drawingNumber,
                material_name: materialName,
                material_version: version
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.universal_link) {
                    // 打开生成的链接
                    window.location.href = response.universal_link;
                } else {
                    // 显示错误信息
                    showNotification('生成失败', response.message || '生成资料文件夹路径失败，请联系管理员', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('生成资料文件夹路径请求失败:', error);
                showNotification('请求失败', '无法连接到服务器，请稍后重试', 'error');
            }
        });
    });
    
    // 获取SMB基础路径
    function getSmbBasePath() {
        // 缓存SMB路径，避免重复请求
        if (window.smbBasePath) {
            return window.smbBasePath;
        }
        
        // 使用同步AJAX请求获取SMB路径（因为我们需要立即返回值）
        let smbBase = "smb://upfile"; // 默认值
        
        $.ajax({
            url: '/get_smb_base_path/',
            type: 'GET',
            async: false, // 使用同步请求，因为需要立即返回值
            dataType: 'json',
            success: function(response) {
                if (response.success && response.smb_base) {
                    smbBase = response.smb_base;
                    // 缓存结果
                    window.smbBasePath = smbBase;
                } else {
                    console.error('获取SMB路径失败:', response.error || '未知错误');
                }
            },
            error: function(xhr, status, error) {
                console.error('获取SMB路径请求失败:', error);
            }
        });
        
        return smbBase;
    }
    
    // 处理已存在文件的删除
    $('.remove-existing-file').on('click', function() {
        const $fileItem = $(this).closest('.file-item');
        const filePath = $fileItem.data('path');
        const fileType = $(this).data('type');
        
        // 将要删除的文件路径添加到隐藏字段
        const $filesToDelete = $('#filesToDelete');
        let filesToDeleteArray = $filesToDelete.val() ? $filesToDelete.val().split(',') : [];
        filesToDeleteArray.push(filePath);
        $filesToDelete.val(filesToDeleteArray.join(','));
        
        // 从DOM中移除文件项
        $fileItem.remove();
    });
    
    // 编辑模式下的删除按钮处理
    $('#customerDrawingPreview .remove-file, #productionDrawingPreview .remove-file, #processDrawingPreview .remove-file, #inspectionSheetPreview .remove-file').on('click', function() {
        const $fileItem = $(this).closest('.file-item');
        const $previewContainer = $fileItem.parent();
        const previewId = $previewContainer.attr('id');
        let fieldName = '';
        // 根据预览容器ID确定字段名
        if (previewId === 'customerDrawingPreview') {
            fieldName = 'material_drawing_customer';
        } else if (previewId === 'productionDrawingPreview') {
            fieldName = 'material_drawing_finished';
        } else if (previewId === 'processDrawingPreview') {
            fieldName = 'material_drawing_workmanship';
        } else if (previewId === 'inspectionSheetPreview') {
            fieldName = 'material_drawing_testingform';
        }
        if (fieldName) {
            // 将要删除的文件类型添加到隐藏字段
            const $filesToDelete = $('#filesToDelete');
            let filesToDeleteArray = $filesToDelete.val() ? $filesToDelete.val().split(',') : [];
            filesToDeleteArray.push(fieldName);
            $filesToDelete.val(filesToDeleteArray.join(','));
        }
        
        // 从DOM中移除文件项
        $fileItem.remove();
    });
    
    // 处理产品图片删除
    $('#productImagePreview').on('click', '.remove-btn', function() {
        // 将要删除的字段添加到隐藏字段
        const $filesToDelete = $('#filesToDelete');
        let filesToDeleteArray = $filesToDelete.val() ? $filesToDelete.val().split(',') : [];
        filesToDeleteArray.push('material_img');
        $filesToDelete.val(filesToDeleteArray.join(','));
        
        // 清空图片预览
        $('#productImagePreview').empty();
    });
    
    // 如果存在产品图片，添加删除按钮
    if ($('#productImagePreview img').length) {
        // 创建删除按钮
        const removeBtn = $('<button type="button" class="btn btn-sm btn-danger remove-btn" style="position:absolute;top:5px;right:5px;"><i class="fas fa-times"></i></button>');
        
        // 设置预览容器为相对定位，以便正确放置删除按钮
        $('#productImagePreview').css('position', 'relative');
        
        // 添加删除按钮到预览容器
        $('#productImagePreview').append(removeBtn);
    }
    
    // 设置当前日期
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');
    $('#uploadDate').val(`${yyyy}-${mm}-${dd}`);
    
    // 表单提交处理
    $('#materialBomForm').on('submit', function(e) {
        // 在此移除调试输出
        // 防止多次验证，让HTML表单验证独立进行
        // 如果已有表单验证在进行，则跳过此处理器
        if ($(this).data('validating')) {
            return true;
        }
        
        // 标记表单正在验证中
        $(this).data('validating', true);
        
        // 手动检查必填字段
        let hasEmpty = false;
        $('[required], [data-required="true"]').each(function() {
            if (!$(this).val()) {
                hasEmpty = true;
                return false; // 跳出循环
            }
        });
        
        if (hasEmpty) {
            e.preventDefault();
            e.stopPropagation();
            $(this).data('validating', false);
            return false;
        }
        
        // 执行自定义验证
        if (!validateForm()) {
            e.preventDefault();
            e.stopPropagation();
            $(this).data('validating', false);
            return false;
        }
        
        // 显示提交中的提示
        showSubmittingMessage();
        
        // 重置验证状态
        $(this).data('validating', false);
        
        // 允许表单正常提交
        return true;
    });
    
    // 版本输入验证
    $('#version').on('input', function() {
        const input = $(this).val().trim().toUpperCase();
        let validInput = input;
        
        // 如果输入的是字母，只保留第一个字母并转为大写
        if (/[A-Z]/.test(input)) {
            validInput = input.match(/[A-Z]/)[0];
        }
        // 如果输入的是数字，最多保留两位
        else if (/^\d+$/.test(input)) {
            // 限制最大为99
            const num = parseInt(input, 10);
            if (num > 99) {
                validInput = '99';
            } else {
                // 自动补0，确保是两位数
                validInput = num.toString().padStart(2, '0');
            }
        }
        // 如果输入的既不是字母也不是数字，清空输入
        else if (input && !/^[A-Z\d]+$/.test(input)) {
            validInput = '';
        }
        
        // 更新输入框的值
        if (validInput !== input) {
            $(this).val(validInput);
        }
    });
    
    // 版本输入框失去焦点时格式化
    $('#version').on('blur', function() {
        const input = $(this).val().trim();
        
        // 如果是数字，确保格式为两位数
        if (/^\d+$/.test(input)) {
            const num = parseInt(input, 10);
            if (num <= 99) {
                $(this).val(num.toString().padStart(2, '0'));
            }
        }
        // 如果是字母，确保是大写
        else if (/^[a-zA-Z]$/.test(input)) {
            $(this).val(input.toUpperCase());
        }
    });
    
    // 表单验证函数
    function validateForm() {
        // 这里添加表单验证逻辑
        // 如果验证失败，返回false
        
        var isValid = true;
        
        // 验证必填字段（同时处理required和data-required属性）
        $('[required], [data-required="true"]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('is-invalid');
                
                // 获取字段标签
                var label = $(this).prev('label').text().replace(' *', '').trim();
                if (!label) {
                    label = $(this).closest('.form-group').find('label').text().replace(' *', '').trim();
                }
                if (!label) {
                    label = "此字段";
                }
                
                // 显示错误消息
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">请填写' + label + '</div>');
                }
                
                // 找到包含此字段的选项卡
                const tabPane = $(this).closest('.tab-pane');
                if (tabPane.length) {
                    const tabId = tabPane.attr('id');
                    // 高亮显示包含错误字段的选项卡
                    $(`#materialTabs button[data-bs-target="#${tabId}"]`).addClass('tab-error');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        });
        
        // 验证版本格式
        const versionValue = $('#version').val();
        if (versionValue) {
            const isValidVersion = /^[A-Z]$/.test(versionValue) || /^[0-9]{2}$/.test(versionValue);
            if (!isValidVersion) {
                isValid = false;
                $('#version').addClass('is-invalid');
                
                if (!$('#version').next('.invalid-feedback').length) {
                    $('#version').after('<div class="invalid-feedback">版本格式不正确，请输入一位字母A-Z或两位数字01-99</div>');
                }
            }
        }
        
        // 检查是否存在重复记录
        const drawingNumber = $('#drawingNumber').val().trim();
        const customerName = $('#customerName').val();
        const version = $('#version').val();
        const isEditMode = $('#formMode').val() === 'edit';
        
        // 如果是编辑模式，并且版本没有改变，则不需要检查重复
        if (isEditMode && version === originalVersion) {
            // 不执行重复检查
        } else if (drawingNumber && customerName && version) {
            // 使用同步AJAX请求检查是否存在重复记录
            $.ajax({
                url: '/check-duplicate-material/',
                type: 'GET',
                data: {
                    drawing_number: drawingNumber,
                    customer_name: customerName,
                    version: version,
                    material_id: isEditMode ? window.location.pathname.split('/').pop() : null
                },
                async: false,
                success: function(response) {
                    if (response.duplicate) {
                        isValid = false;
                        $('#version').addClass('is-invalid');
                        
                        if (!$('#version').next('.invalid-feedback').length) {
                            $('#version').after(`<div class="invalid-feedback">已存在相同的物料记录，物料编码：${response.material_no}</div>`);
                        }
                        
                        showNotification('重复警告', `已存在相同的物料记录！客户名称：${customerName}，客户图号：${drawingNumber}，版本：${version}，物料编码：${response.material_no}`, 'warning');
                    }
                }
            });
        }
        
        return isValid;
    }
    
    // 显示提交中的提示
    function showSubmittingMessage() {
        // 创建遮罩层元素
        const $overlay = $('<div id="savingOverlay" class="saving-overlay"><div class="saving-content"><i class="fas fa-spinner fa-spin fa-2x"></i><p>正在保存...</p></div></div>');
        
        // 添加到body
        $('body').append($overlay);
        
        // 淡入显示
        $overlay.fadeIn(300);
    }

    // 移除提交中的提示
    function removeSavingMessage() {
        $('#savingOverlay').fadeOut(300, function() {
            $(this).remove();
        });
    }

    // 表单重置
    $('#resetForm').on('click', function() {
        if (confirm('确定要重置表单吗？所有已填写的数据将丢失。')) {
            // 完全重置表单
            
            $('#materialBomForm')[0].reset();
            
            $('#drawingNumber').val('');
            // 明确重置每个文本字段
            $('#materialCode').val('');
            $('#materialName').val('');
            $('#version').val('');
            $('#material_quality').val('');
            $('#materialSpec').val('');
            $('#lossRate').val('');
            $('#quantity').val('');
            $('#productionQuantity').val('');
            $('#partUsage').val('');
            $('#responsible').val('');
            $('#castingSupplier').val('');
            $('#machiningSupplier').val('');
            $('#sheetMetalSupplier').val('');
            $('#procurementSupplier').val('');
            $('#processNumber').val('');
            $('#cuttingSize').val('');
            
            
            // 重置Select2控件
            if ($.fn.select2) {
                $('#customerName').val('').trigger('change');
                $('#materialCategory').val('').trigger('change');
                $('#semifinishedStatus').val('').trigger('change');
                $('#unit').val('').trigger('change');
                $('#workshop').val('').trigger('change');
            }
            
            // 重置单选按钮
            $('input[name=customerSupply][value="否"]').prop('checked', true);
            $('input[name=outsourcing][value="否"]').prop('checked', true);
            $('input[name=sales][value="否"]').prop('checked', true);
            $('input[name=selfMade][value="否"]').prop('checked', true);
            $('input[name=subcontract][value="否"]').prop('checked', true);
            $('input[name=productionConsumption][value="否"]').prop('checked', true);
            $('input[name=batchManagement][value="否"]').prop('checked', true);
            $('input[name=virtualItem][value="否"]').prop('checked', true);
            
            // 重置存货属性滑块开关
            const switchIds = [
                'customerSupply',
                'outsourcing',
                'sales',
                'selfMade',
                'subcontract',
                'productionConsumption',
                'batchManagement',
                'virtualItem'
            ];
            
            // 将所有滑块开关重置为"否"
            switchIds.forEach(function(id) {
                // 重置复选框状态
                $(`#${id}`).prop('checked', false);
                
                // 更新显示文本
                const $text = $(`#${id}Text`);
                $text.text('否');
                $text.removeClass('text-success').addClass('text-muted');
                
                // 更新隐藏字段值
                $(`input[name="${id}Hidden"]`).val('否');
            });
            
            // 清空工艺路线内容
            $('#process_content').val('');
            $('#selected_processes').empty();
            $('#empty_process_message').show();
            $('#selected_processes').hide();
            
            // 清除所有预览
            $('.file-list, #productImagePreview').empty();
            
            // 重置版本号
            $('#version').val('');
            // 客户图号和客户名称查询功能 - 使用失去焦点事件
            lastDrawingNumber = ''; // 记录上一次查询的图号
            lastCustomerName = ''; // 记录上一次查询的客户名称
            lastVersion = ''; // 记录上一次查询的版本号
            // 重置原始版本号
            originalVersion = '';
            
            // 设置当前日期
            const today = new Date();
            const yyyy = today.getFullYear();
            const mm = String(today.getMonth() + 1).padStart(2, '0');
            const dd = String(today.getDate()).padStart(2, '0');
            $('#uploadDate').val(`${yyyy}-${mm}-${dd}`);
            
            // 清除所有验证状态
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').remove();
            
            // 如果是编辑模式，切换回新增模式
            if ($('#formMode').val() === 'edit') {
                $('#formMode').val('add');
                $('#edit_id').val('');

                $('#modeIndicator').removeClass('mode-edit').addClass('mode-new');
                $('#modeText').text('新增模式');
                
                // 更新提交按钮文本
                $('button[type="submit"]').html('<i class="fas fa-save mr-2"></i>提交保存');
                
                // 移除新增版本按钮
                $('.new-version-btn').remove();
                
                // 修改表单提交地址为新增URL（不带编辑参数）
                $('#materialBomForm').attr('action', '/materials/add/');
            }
            
            // 显示重置成功消息
            showNotification('重置成功', '表单已重置', 'info');
        }
    });
    

    // 工艺步骤处理
    let processIndex = 0;
    $('#addProcessStep').on('click', function() {
        processIndex++;
        const processStepHtml = `
            <div class="col-md-12 process-step-row" data-index="${processIndex}">
                <div class="card mb-3">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">工序 ${processIndex}</h5>
                        <button type="button" class="btn btn-sm btn-danger remove-process">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">工序名称</label>
                                    <input type="text" class="form-control" name="process_name_${processIndex}" required>
                </div>
            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">工序描述</label>
                                    <input type="text" class="form-control" name="process_desc_${processIndex}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">工序参数</label>
                                    <input type="text" class="form-control" name="process_params_${processIndex}">
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        $('#processSteps').append(processStepHtml);
        
        // 更新工艺序号
        updateProcessNumber();
        
        // 删除工序按钮事件
        $('.remove-process').off('click').on('click', function() {
            $(this).closest('.process-step-row').remove();
            updateProcessNumber();
        });
    });

    // 更新工艺序号
    function updateProcessNumber() {
        const steps = $('.process-step-row').length;
        if (steps > 0) {
            const numbers = [];
            $('.process-step-row').each(function(index) {
                numbers.push(index + 1);
                $(this).find('h5').text(`工序 ${index + 1}`);
            });
            $('#processNumber').val(numbers.join(','));
        } else {
            $('#processNumber').val('');
        }
    }

    // 创建新版本的函数
    function createNewVersion() {
        // 获取客户名称和图号
        const drawingNumber = $('#drawingNumber').val().trim();
        const customerName = $('#customerName').val();
        
        // 如果客户名称或图号为空，显示错误信息
        if (!drawingNumber || !customerName) {
            showNotification('错误', '客户名称和客户图号不能为空', 'error');
            return;
        }
        
        // 获取当前图片路径（在切换到新增模式前）
        let currentImagePath = '';
        
        // 首先检查隐藏字段中是否已有图片路径
        if ($('#original_image_path').length && $('#original_image_path').val()) {
            currentImagePath = $('#original_image_path').val();
        } 
        // 如果没有，检查图片预览区域是否有图片
        else if ($('#productImagePreview img').length) {
            // 从图片src中提取路径
            const imgSrc = $('#productImagePreview img').attr('src');
            // 如果src是以/media/开头的，提取后面的部分作为路径
            if (imgSrc && imgSrc.startsWith('/media/')) {
                currentImagePath = imgSrc.substring(7); // 去掉"/media/"前缀
            }
        }
        
        // 检查是否为临时模式
        const tempStateValue = $('#temp_state').val();
        const tempMode = tempStateValue !== '0' ? '1' : '0';

        // 发送AJAX请求获取数据库中的最高版本号
        $.ajax({
            url: '/api/get_max_version/',
            type: 'GET',
            data: {
                drawing_number: drawingNumber,
                customer_name: customerName,
                temp_mode: tempMode  // 传递临时模式参数
            },
            success: function(response) {
                if (response.success) {
                    const newVersion = response.max_version;

                    // 更新表单模式
                    $('#formMode').val('add');
                    $('#edit_id').val('');

                    $('#modeIndicator').removeClass('mode-edit').addClass('mode-new');
                    $('#modeText').text('新增模式');
                    
                    // 更新提交按钮文本
                    $('button[type="submit"]').html('<i class="fas fa-save mr-2"></i>提交保存');
                    
                    // 更新版本号
                    $('#version').val(newVersion);
                    // 更新原始版本号
                    originalVersion = newVersion;
                    
                    // 保存原始图片路径
                    if (currentImagePath) {
                        // 确保隐藏字段存在
                        if (!$('#original_image_path').length) {
                            $('<input>').attr({
                                type: 'hidden',
                                id: 'original_image_path',
                                name: 'original_image_path',
                                value: currentImagePath
                            }).appendTo('#materialBomForm');
                        } else {
                            $('#original_image_path').val(currentImagePath);
                        }
                        console.log('新增版本时设置原始图片路径:', currentImagePath);
                    }
                    
                    // 保留产品图片预览，不清空
                    const productImagePreview = $('#productImagePreview').html();
                    
                    // 清空图纸信息标签页的内容，但不包括产品图片
                    clearDrawingTabContentExceptProductImage();
                    
                    // 如果之前有产品图片，恢复预览
                    if (productImagePreview) {
                        $('#productImagePreview').html(productImagePreview);
                        console.log('恢复产品图片预览');
                    }
                    
                    // 移除新增版本按钮
                    $('.new-version-btn').remove();
                    
                    // 修改表单提交地址为新增URL
                    const addUrl = '/materials/add/';
                    $('#materialBomForm').attr('action', addUrl);
                    
                    // 移除可能存在的隐藏material_id字段
                    $('input[name="material_id"]').remove();
                    
                    // 确保浏览器不会发送带有ID的请求
                    history.pushState({}, '', '/materials/add/');
                    
                    // 保留来源页面信息
                    const sourceType = $('#sourceType').val();
                    
                    // 显示提示信息
                    showNotification('新增版本', `已切换到新增模式，版本号已更新为 ${newVersion}`, 'success');
                    
                    // 重置填充标志，允许后续手动修改触发编码生成
                    isFillingForm = false;
                } else {
                    showNotification('错误', response.message || '获取最大版本号失败', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('获取最大版本号失败:', error);
                showNotification('错误', '获取最大版本号失败，请重试', 'error');
            }
        });
    }

    // 版本号递增函数
    function incrementVersion(version) {
        if (!version) {
            return '01'; // 如果没有版本号，默认返回01
        }
        
        // 获取客户名称和图号
        const drawingNumber = $('#drawingNumber').val().trim();
        const customerName = $('#customerName').val();
        
        // 检查是否为临时模式
        const tempStateValue = $('#temp_state').val();
        const tempMode = tempStateValue !== '0' ? '1' : '0';



        // 发送AJAX请求查询数据库中的最大版本号
        return $.ajax({
            url: '/api/get_max_version/',
            type: 'GET',
            async: false, // 使用同步请求，确保在返回前获得结果
            data: {
                drawing_number: drawingNumber,
                customer_name: customerName,
                temp_mode: tempMode  // 传递临时模式参数
            },
            success: function(response) {
                if (response.success && response.max_version) {
                    return response.max_version;
                }
            },
            error: function(xhr, status, error) {
                console.error('获取最大版本号失败:', error);
                // 出错时使用本地递增逻辑
                
                // 检查是否是字母版本 (A-Z)
                if (/^[A-Z]$/.test(version)) {
                    // 字母版本，将字母转换为下一个字母
                    const charCode = version.charCodeAt(0);
                    if (charCode < 90) { // Z的ASCII码是90
                        return String.fromCharCode(charCode + 1);
                    } else {
                        return 'A'; // 如果是Z，循环回A
                    }
                }
                
                // 检查是否是数字版本 (01-99)
                if (/^\d{1,2}$/.test(version)) {
                    // 数字版本，将数字加1并格式化为两位数
                    const num = parseInt(version, 10) + 1;
                    return num <= 99 ? num.toString().padStart(2, '0') : '01'; // 如果超过99，循环回01
                }
                
                // 如果格式不符合要求，返回默认值
                return '01';
            }
        }).responseJSON.max_version || '01'; // 如果没有获取到结果，默认返回'01'
    }

    // 清空图纸信息标签页的内容，但保留产品图片
    function clearDrawingTabContentExceptProductImage() {
        // 清除文件输入框的值，但保留产品图片
        $('#customerDrawing, #productionDrawing, #processDrawing, #inspectionSheet, #documentFolder').val('');
        
        // 清除文件预览区域，但保留产品图片
        $('#customerDrawingPreview, #productionDrawingPreview, #processDrawingPreview, #inspectionSheetPreview, #documentFolderPreview').empty();
    }

    // 表单提交前调试
    $('#materialBomForm').on('submit', function(e) {
        // 检查工艺路线内容
        const processContent = $('#process_content').val();

        // 确保工艺路线内容被正确设置
        if ($('#selected_processes .process-step').length > 0 && !processContent) {
            // 如果有工艺步骤但没有内容，重新生成内容
            updateProcessesFromUI();
        }
        
        // 如果是新增模式，确保表单提交到新增URL
        if ($('#formMode').val() === 'add') {
            const currentAction = $(this).attr('action');
            if (currentAction.includes('/materials/edit/')) {
                e.preventDefault(); // 阻止默认提交
                
                // 修改表单提交地址
                $(this).attr('action', '/materials/add/');
                
                // 手动提交表单
                this.submit();
                return false;
            }
        }
        
        return true; // 允许表单提交
    });

    // 初始化存货属性滑块开关
    function initInventorySwitches() {
        // 存货属性滑块开关列表
        const switchIds = [
            'customerSupply',
            'outsourcing',
            'sales',
            'selfMade',
            'subcontract',
            'productionConsumption',
            'batchManagement',
            'virtualItem'
        ];
        
        // 为每个滑块开关添加事件监听
        switchIds.forEach(function(id) {
            const $switch = $(`#${id}`);
            const $text = $(`#${id}Text`);
            
            // 初始化滑块状态和文本
            updateSwitchText($switch, $text);
            
            // 添加切换事件
            $switch.on('change', function() {
                updateSwitchText($(this), $text);
                
                // 更新隐藏字段的值
                $(`input[name="${id}Hidden"]`).val($(this).prop('checked') ? '是' : '否');
            });
        });
    }
    
    // 更新滑块开关的文本显示
    function updateSwitchText($switch, $text) {
        if ($switch.prop('checked')) {
            $text.text('是');
            $text.removeClass('text-muted').addClass('text-success');
        } else {
            $text.text('否');
            $text.removeClass('text-success').addClass('text-muted');
        }
    }
});

// 初始化时检查工艺路线内容并显示
$(document).ready(function() {
    // 如果process_content已有内容，初始化工艺路线显示
    const initialProcessContent = $('#process_content').val();
    
    if (initialProcessContent && initialProcessContent.trim() !== '') {
        // 确保updateSelectedProcesses函数被调用
        if (typeof updateSelectedProcesses === 'function') {
            updateSelectedProcesses();
        }
    }
});

/**
 * 设置文件上传区域
 */
function setupFileUpload(inputId, previewId, previewCallback, isMultiple = false) {
    const $area = $(`#${inputId}Area`);
    const $input = $(`#${inputId}`);
    const $preview = $(`#${previewId}`);
    
    // 点击区域触发文件选择
    $area.on('click', function(e) {
        // 阻止事件冒泡，防止点击事件递归触发
        e.stopPropagation();
        
        // 确保点击的是上传区域而不是其中的文件输入框
        if (e.target !== $input[0] && !$.contains(e.target, $input[0])) {
            $input.trigger('click');
        }
    });
    
    // 拖放文件
    $area.on('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).addClass('dragover');
    });
    
    $area.on('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');
    });
    
    $area.on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');
        
        if (e.originalEvent.dataTransfer && e.originalEvent.dataTransfer.files.length) {
            $input[0].files = e.originalEvent.dataTransfer.files;
            handleFiles($input[0], $preview, previewCallback, isMultiple);
        }
    });
    
    // 文件选择事件
    $input.on('change', function() {
        handleFiles(this, $preview, previewCallback, isMultiple);
    });
}

/**
 * 处理已选择的文件
 */
function handleFiles(input, $preview, previewCallback, isMultiple) {
    if (input.files && input.files.length) {
        $preview.empty();
        
        for (let i = 0; i < input.files.length; i++) {
            const file = input.files[i];
            
            // 创建文件预览项
            const $fileItem = $('<div class="file-item"></div>');
            
            // 文件名和大小
            const fileSize = (file.size / 1024).toFixed(2) + ' KB';
            $fileItem.append(`
                <div>
                    <i class="fas fa-file mr-2"></i>
                    <span class="file-name">${file.name}</span>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                    <i class="fas fa-times"></i>
                </button>
            `);
            
            $preview.append($fileItem);
            
            // 如果提供了自定义预览回调，则调用它
            if (previewCallback) {
                previewCallback(file, $fileItem);
            }
            
            // 如果不是多文件上传，只处理第一个文件
            if (!isMultiple) break;
        }
        
        // 删除文件事件
        $preview.find('.remove-file').on('click', function(e) {
            // 阻止事件冒泡，防止触发上传区域的点击事件
            e.preventDefault();
            e.stopPropagation();
            
            $(this).closest('.file-item').remove();
            // 重置文件输入框
            input.value = '';
        });
    }
}

/**
 * 图像文件预览
 */
function showImagePreview(file, $target) {
    // 检查文件是否为图像
    if (file.type.match('image.*')) {
        const reader = new FileReader();

        reader.onload = function(e) {
            // 判断$target是文件项还是预览容器
            let $container;
            let isFileItem = false;

            if ($target.hasClass('file-item')) {
                // $target是文件项
                $container = $target.parent();
                isFileItem = true;
            } else {
                // $target是预览容器
                $container = $target;
                isFileItem = false;
            }

            // 如果是产品图片预览，特殊处理
            if ($container.attr('id') === 'productImagePreview') {
                // 清空容器中的所有内容（包括之前的图片和文件项）
                $container.empty();

                // 创建图片预览元素
                const $img = $('<img class="image-preview" />');
                $img.attr('src', e.target.result);

                // 添加到容器
                $container.append($img);

                // 为图片添加点击事件
                $img.on('click', function() {
                    const imgSrc = $(this).attr('src');
                    $('#previewImage').attr('src', imgSrc);
                    $('#imagePreviewOverlay').addClass('active');
                });
            } else {
                // 对于其他类型的预览
                if (isFileItem) {
                    // 如果是文件项，替换图标
                    $target.find('.fas.fa-file').removeClass('fa-file').addClass('fa-image');
                } else {
                    // 如果是预览容器，直接添加图片
                    $container.empty();
                    const $img = $('<img class="image-preview" />');
                    $img.attr('src', e.target.result);
                    $container.append($img);
                }
            }

            // 延迟一下，确保图片已加载到DOM中
            setTimeout(function() {
                // 初始化图片预览功能
                if (typeof initImagePreview === 'function') {
                    initImagePreview();
                }
            }, 100);
        };

        reader.readAsDataURL(file);
    }
}

/**
 * 工艺路线相关功能
 */
// 更新已选工艺显示
function updateSelectedProcesses() {
    const $container = $('#selected_processes');
    if (!$container.length) return; // 如果容器不存在则退出
    
    $container.empty();
    
    const content = $('#process_content').val();
    if (!content) {
        updateEmptyMessage();
        return;
    }
    
    updateEmptyMessage();
    
    const processes = content.split('|');
    
    // 添加工艺步骤和箭头
    processes.forEach((process, index) => {
        // 添加步骤
        const $step = $(`
            <div class="process-step" data-process="${process}">
                ${process}
                <i class="fas fa-times remove-process"></i>
                        </div>
        `);
        
        $container.append($step);
        
        // 如果不是最后一个，添加箭头
        if (index < processes.length - 1) {
            $container.append(`<span class="process-arrow"><i class="fas fa-long-arrow-alt-right"></i></span>`);
        }
    });
    
    // 绑定删除事件
    $('.remove-process').on('click', function(e) {
        e.stopPropagation(); // 防止触发排序
        const process = $(this).parent().data('process');
        removeProcess(process);
    });
    
    // 使工艺步骤可排序，如果jQuery UI可用
    if ($.fn.sortable) {
        $container.sortable({
            placeholder: "process-placeholder",
            axis: "x",
            items: ".process-step",
            forcePlaceholderSize: true,
            tolerance: "pointer",
            update: function(event, ui) {
                // 重新生成工序排序后的内容
                updateProcessesFromUI();
            }
        });
    }
}

// 根据UI中的顺序更新工艺内容
function updateProcessesFromUI() {
    const processes = [];
    $('#selected_processes .process-step').each(function() {
        processes.push($(this).data('process'));
    });
    $('#process_content').val(processes.join('|'));
    updateSelectedProcesses();
}

// 从已选列表中移除工艺
function removeProcess(process) {
    let currentContent = $('#process_content').val();
    let processes = currentContent ? currentContent.split('|') : [];
    
    // 找到要移除的工艺索引
    const index = processes.indexOf(process);
    if (index > -1) {
        processes.splice(index, 1);
        $('#process_content').val(processes.join('|'));
        updateSelectedProcesses();
    }
}

// 检查是否有工艺内容，更新空提示信息
function updateEmptyMessage() {
    const content = $('#process_content').val();
    if (!content) {
        $('#empty_process_message').show();
        $('#selected_processes').hide();
    } else {
        $('#empty_process_message').hide();
        $('#selected_processes').show();
    }
}

