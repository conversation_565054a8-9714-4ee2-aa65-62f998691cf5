{% extends 'base.html' %}
{% load static %}

{% block title %}编辑数据字典 - 物料清单管理系统{% endblock %}

{% block body_class %}{% if is_iframe %}{% else %}hold-transition sidebar-mini layout-fixed{% endif %}{% endblock %}

{% block content %}
{% if is_iframe %}
    <!-- 主体内容 -->
    <section class="content">
        <div class="container-fluid">
            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">编辑数据字典</h3>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'data_dictionary_edit' dict_item.datadictionary_id %}{% if is_iframe %}?iframe=1{% endif %}">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="dict_name">字典名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="dict_name" name="dict_name" required value="{{ dict_item.datadictionary_name }}">
                        </div>
                        <div class="form-group">
                            <label for="dict_code">字典代码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="dict_code" name="dict_code" required value="{{ dict_item.datadictionary_code }}">
                        </div>
                        <div class="form-group">
                            <label for="dict_tag">标签</label>
                            <input type="text" class="form-control" id="dict_tag" name="dict_tag" value="{{ dict_item.datadictionary_tag }}">
                        </div>
                        <div class="form-group">
                            <label for="dict_pid">父级字典</label>
                            <select class="form-control" id="dict_pid" name="dict_pid">
                                <option value="0" {% if dict_item.datadictionary_pid == 0 %}selected{% endif %}>无 (作为顶级字典)</option>
                                {% for parent in parent_dicts %}
                                <option value="{{ parent.datadictionary_id }}" {% if dict_item.datadictionary_pid == parent.datadictionary_id %}selected{% endif %}>
                                    {{ parent.datadictionary_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="dict_order">排序</label>
                            <input type="number" class="form-control" id="dict_order" name="dict_order" value="{{ dict_item.datadictionary_order }}">
                        </div>
                        <button type="submit" class="btn btn-primary">保存</button>
                        <a href="{% url 'data_dictionary_list' %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-secondary">取消</a>
                    </form>
                </div>
            </div>
        </div>
    </section>
{% else %}
    <div class="wrapper">
        <!-- 导航栏 -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- 左侧导航栏切换按钮 -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
            </ul>

            <!-- 右侧导航栏 -->
            <ul class="navbar-nav ml-auto">
                <!-- 用户信息下拉菜单 -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline">{{ user_nick|default:username }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> 个人资料
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{% url 'logout' %}" class="dropdown-item">
                            <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                        </a>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- 左侧边栏 -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- 品牌Logo -->
            <a href="#" class="brand-link">
                <span class="brand-text font-weight-light">物料清单管理系统</span>
            </a>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 用户面板 -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-light"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block">{{ user_nick|default:username }}</a>
                    </div>
                </div>

                <!-- 侧边栏菜单 -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'dashboard' %}" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>
                                    仪表盘
                                </p>
                            </a>
                        </li>
                        
                        <li class="nav-item menu-open">
                            <a href="#" class="nav-link active">
                                <i class="nav-icon fas fa-database"></i>
                                <p>
                                    基础数据管理
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{% url 'data_dictionary_list' %}" class="nav-link active">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>数据字典</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'customer_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>客户信息</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 主体内容 -->
            <section class="content">
                <div class="container-fluid">
                    <!-- 消息提示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">编辑数据字典</h3>
                        </div>
                        <div class="card-body">
                            <form method="post" action="{% url 'data_dictionary_edit' dict_item.datadictionary_id %}">
                                {% csrf_token %}
                                <div class="form-group">
                                    <label for="dict_name">字典名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="dict_name" name="dict_name" required value="{{ dict_item.datadictionary_name }}">
                                </div>
                                <div class="form-group">
                                    <label for="dict_code">字典代码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="dict_code" name="dict_code" required value="{{ dict_item.datadictionary_code }}">
                                </div>
                                <div class="form-group">
                                    <label for="dict_tag">标签</label>
                                    <input type="text" class="form-control" id="dict_tag" name="dict_tag" value="{{ dict_item.datadictionary_tag }}">
                                </div>
                                <div class="form-group">
                                    <label for="dict_pid">父级字典</label>
                                    <select class="form-control" id="dict_pid" name="dict_pid">
                                        <option value="0" {% if dict_item.datadictionary_pid == 0 %}selected{% endif %}>无 (作为顶级字典)</option>
                                        {% for parent in parent_dicts %}
                                        <option value="{{ parent.datadictionary_id }}" {% if dict_item.datadictionary_pid == parent.datadictionary_id %}selected{% endif %}>
                                            {{ parent.datadictionary_name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="dict_order">排序</label>
                                    <input type="number" class="form-control" id="dict_order" name="dict_order" value="{{ dict_item.datadictionary_order }}">
                                </div>
                                <button type="submit" class="btn btn-primary">保存</button>
                                <a href="{% url 'data_dictionary_list' %}" class="btn btn-secondary">取消</a>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </div>

    </div>
{% endif %}
{% endblock %} 