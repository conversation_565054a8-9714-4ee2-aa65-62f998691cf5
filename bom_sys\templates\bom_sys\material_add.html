{% extends 'base.html' %}
{% load custom_filters %}
{% load none_filter %}

{% block title %}{% if is_preview %}查看物料信息{% elif material %}编辑物料信息{% else %}单物料录入{% endif %}{% endblock %}

{% block body_class %}{% if is_iframe %}iframe-mode{% else %}hold-transition sidebar-mini layout-fixed{% endif %}{% endblock %}

{% block content %}
<div class="{% if not is_iframe %}content-wrapper{% endif %}">
            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <!-- 模式指示器 -->
            <div id="modeIndicator" class="mode-indicator {% if is_preview %}mode-preview{% elif material %}mode-edit{% else %}mode-new{% endif %}" style="display: block;">
                        <i class="fas fa-info-circle mr-2"></i>
                <span id="modeText">{% if is_preview %}预览模式{% elif material %}编辑模式{% else %}新增模式{% endif %}</span>
                    </div>

            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}

            <form id="materialBomForm" action="{% if material %}{% url 'material_edit' material.material_id %}{% else %}{% url 'material_add' %}{% endif %}{% if bom_pid %}?bom_pid={{ bom_pid }}{% endif %}" method="post" enctype="multipart/form-data" {% if is_preview %}class="preview-mode"{% endif %}>
                {% csrf_token %}
                <!-- 隐藏字段 -->
                <input type="hidden" id="formMode" name="formMode" value="{% if is_preview %}preview{% elif material %}edit{% else %}add{% endif %}">
                <input type="hidden" id="process_content" name="process_content" value="{{ material.material_processroute|default:'' }}">
                <!-- 工艺详情隐藏字段 -->
                <input type="hidden" id="material_processroute" name="material_processroute" value="{{ material.material_processroute|none_to_empty }}">
                <input type="hidden" id="material_ProcessrouteTimes" name="material_ProcessrouteTimes" value="{{ material.material_ProcessrouteTimes|none_to_empty }}">
                <input type="hidden" id="material_ProcessrouteDevice" name="material_ProcessrouteDevice" value="{{ material.material_ProcessrouteDevice|none_to_empty }}">
                <input type="hidden" id="material_ProcessroutePrice" name="material_ProcessroutePrice" value="{{ material.material_ProcessroutePrice|none_to_empty }}">
                <input type="hidden" id="material_ProcessrouteType" name="material_ProcessrouteType" value="{{ material.material_ProcessrouteType|none_to_empty }}">
                <input type="hidden" id="material_ProcessrouteOutSupplier" name="material_ProcessrouteOutSupplier" value="{{ material.material_ProcessrouteOutSupplier|none_to_empty }}">
                <input type="hidden" id="material_ProcessrouteOutPrice" name="material_ProcessrouteOutPrice" value="{{ material.material_ProcessrouteOutPrice|none_to_empty }}">
                <input type="hidden" id="material_ProcessrouteImportant" name="material_ProcessrouteImportant" value="{{ material.material_ProcessrouteImportant|none_to_empty }}">
                <input type="hidden" id="material_ProcessrouteCode" name="material_ProcessrouteCode" value="{{ material.material_ProcessrouteCode|none_to_empty }}">
                <input type="hidden" id="sourceType" name="sourceType" value="{{ source_type|default:'direct' }}">
                <!-- 添加隐藏的edit_id字段 -->
                <input type="hidden" id="edit_id" name="edit_id" value="{{ material.material_id|default:'' }}">
                <!-- BOM树相关参数 -->
                <input type="hidden" id="bom_pid" name="bom_pid" value="{{ bom_pid|default:'' }}">
                <input type="hidden" id="customer_code_param" name="customer_code_param" value="{{ customer_code|default:'' }}">
                <!-- 用于保存原始图片路径，在新增版本时使用 -->
                <input type="hidden" id="original_image_path" name="original_image_path" value="{{ material.material_img|default:'' }}">
                <!-- 用于记录要删除的文件 -->
                <input type="hidden" id="filesToDelete" name="filesToDelete" value="">
                
                <!-- 存货属性隐藏字段 -->
                <input type="hidden" id="customerSupplyHidden" name="customerSupplyHidden" value="否">
                <input type="hidden" id="outsourcingHidden" name="outsourcingHidden" value="否">
                <input type="hidden" id="salesHidden" name="salesHidden" value="否">
                <input type="hidden" id="selfMadeHidden" name="selfMadeHidden" value="否">
                <input type="hidden" id="subcontractHidden" name="subcontractHidden" value="否">
                <input type="hidden" id="productionConsumptionHidden" name="productionConsumptionHidden" value="否">
                <input type="hidden" id="batchManagementHidden" name="batchManagementHidden" value="否">
                <input type="hidden" id="virtualItemHidden" name="virtualItemHidden" value="否">
                <!-- 临时状态隐藏字段 -->
                <input type="hidden" id="temp_state" name="temp_state" value="{{ temp_state|default:'' }}">
                <input type="hidden" id="bom_state" name="bom_state" value="{{ bom_state|default:'' }}">
                        <!-- 标签页导航 -->
                        <ul class="nav nav-tabs" id="materialTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="material-tab" data-bs-toggle="tab" data-bs-target="#material" type="button" role="tab" aria-controls="material" aria-selected="true">
                                    <i class="fas fa-box mr-2"></i>物料属性
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                        <button class="nav-link" id="drawing-tab" data-bs-toggle="tab" data-bs-target="#drawing" type="button" role="tab" aria-controls="drawing" aria-selected="false">
                                    <i class="fas fa-file-image mr-2"></i>图纸信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                        <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory" type="button" role="tab" aria-controls="inventory" aria-selected="false">
                                    <i class="fas fa-warehouse mr-2"></i>存货属性
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                        <button class="nav-link" id="supplier-tab" data-bs-toggle="tab" data-bs-target="#supplier" type="button" role="tab" aria-controls="supplier" aria-selected="false">
                                    <i class="fas fa-truck mr-2"></i>供应商
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                        <button class="nav-link" id="process-tab" data-bs-toggle="tab" data-bs-target="#process" type="button" role="tab" aria-controls="process" aria-selected="false">
                                    <i class="fas fa-cogs mr-2"></i>工艺
                                </button>
                            </li>
                        </ul>

                        <!-- 标签页内容 -->
                        <div class="tab-content" id="materialTabContent">
                            <!-- 物料属性 -->
                    <div class="tab-pane fade show active" id="material" role="tabpanel" aria-labelledby="material-tab">
                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="customerName" class="form-label">客户名称 <span class="required">*</span></label>
                                            <select class="form-control select2" id="customerName" name="customerName" required>
                                                <option value="">请选择客户</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.customer_name }}" data-code="{{ customer.customer_code }}" {% if customer_name == customer.customer_name %}selected{% endif %}>{{ customer.customer_name }}({{ customer.customer_code }})</option>
                                        {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                            <div class="col-sm-6 col-md-3">
                                <div class="form-group">
                                    <label for="drawingNumber" class="form-label">客户图号 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="drawingNumber" name="drawingNumber" value="{{ material.material_drawingno|default:'' }}" required>
                                    <div class="form-text">输入图号后自动查询已有物料</div>
                                </div>
                            </div>

                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="materialCategory" class="form-label">物料属性分类 <span class="required">*</span></label>
                                            <select class="form-control" id="materialCategory" name="materialCategory" required>
                                                <option value="">请选择物料属性分类</option>
                                        {% for category in material_categories %}
                                        <option value="{{ category.code }}" data-rule="{{ category.rule }}" {% if material.material_attr == category.code %}selected{% endif %}>{{ category.name }}({{ category.code }})</option>
                                        {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="materialCode" class="form-label">物料编码 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="materialCode" name="materialCode" value="{{ material.material_no|default:'' }}" required>
                                            <div class="form-text">系统将根据分类和客户自动生成编码</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="materialName" class="form-label">物料名称 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="materialName" name="materialName" value="{{ material.material_name|default:'' }}" required>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="version" class="form-label">版本 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="version" name="version" value="{{ material.material_version|default:'' }}" required>
                                    <div class="form-text">请输入一位字母(A-Z)或两位数字(01-99)</div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                    <label for="material_quality" class="form-label">材质</label>
                                    <input type="text" class="form-control" id="material_quality" name="material_quality" value="{{ material.material_quality|default:'' }}">
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="materialSpec" class="form-label">材料规格</label>
                                    <input type="text" class="form-control" id="materialSpec" name="materialSpec" value="{{ material.material_spec|default:'' }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="unit" class="form-label">计量单位</label>
                                            <select class="form-control" id="unit" name="unit">
                                                <option value="">请选择计量单位</option>
                                        {% for unit in units %}
                                        <option value="{{ unit.code }}" {% if material.material_unit == unit.code %}selected{% endif %}>{{ unit.name }}</option>
                                        {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="workshop" class="form-label">生产车间</label>
                                            <select class="form-control" id="workshop" name="workshop">
                                                <option value="">请选择生产车间</option>
                                        {% for workshop in workshops %}
                                        <option value="{{ workshop.code }}" {% if material.material_workshop == workshop.code %}selected{% endif %}>{{ workshop.name }}</option>
                                        {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="techManager" class="form-label">技术负责人</label>
                                    <input type="text" class="form-control" id="techManager" name="techManager" value="{{ material.material_techmanager|default:user_nick|default:username }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="uploadDate" class="form-label">上传日期</label>
                                    <input type="date" class="form-control" id="uploadDate" name="uploadDate" value="{{ material.material_uploaddate|default:'' }}">
                                        </div>
                                    </div>
                            <div class="col-sm-6 col-md-4">
                                        <div class="form-group">
                                            <label for="productImage" class="form-label">产品图片</label>
                                    <input type="file" id="productImage" name="productImage" accept="image/jpg,image/jpeg,image/png,image/gif" style="display: none;">
                                            <div class="upload-area" id="productImageArea" style="height: 120px;">
                                                <i class="fas fa-image fa-2x mb-2"></i>
                                                <p>点击上传产品图片 (JPG/PNG/GIF)</p>
                                            </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-5">
                                <div class="form-group">
                                    <label class="form-label">图片预览</label>
                                    <div id="productImagePreview" class="image-preview-container">
                                        {% if product_image_url %}
                                        <img src="{{ product_image_url }}" class="image-preview" alt="产品图片">
                                        {% endif %}
                                    </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 图纸信息 -->
                    <div class="tab-pane fade" id="drawing" role="tabpanel" aria-labelledby="drawing-tab">
                                <div class="row">
                            <div class="col-sm-4 col-md-4">
                                        <div class="form-group">
                                            <label for="customerDrawing" class="form-label">客户图纸</label>
                                    <input type="file" id="customerDrawing" name="customerDrawing" accept=".pdf" style="display: none;">
                                            <div class="upload-area" id="customerDrawingArea" style="height: 120px;">
                                                <i class="fas fa-file-pdf fa-2x mb-2"></i>
                                                <p>点击上传客户图纸 (PDF格式)</p>
                                            </div>
                                            <div id="customerDrawingPreview" class="file-list">
                                                {% if customer_drawing_url %}
                                                <div class="file-item">
                                                    <div>
                                                        <i class="fas fa-file-pdf mr-2"></i>
                                                        <a href="{% url 'view_file' 'customer' material.material_id %}" target="_blank" class="file-link">{{ customer_drawing_url|filename_from_path }}</a>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                            <div class="col-sm-4 col-md-4">
                                        <div class="form-group">
                                            <label for="productionDrawing" class="form-label">生产图纸</label>
                                    <input type="file" id="productionDrawing" name="productionDrawing" accept=".pdf" style="display: none;">
                                            <div class="upload-area" id="productionDrawingArea" style="height: 120px;">
                                                <i class="fas fa-file-pdf fa-2x mb-2"></i>
                                                <p>点击上传生产图纸 (PDF格式)</p>
                                            </div>
                                            <div id="productionDrawingPreview" class="file-list">
                                                {% if production_drawing_url %}
                                                <div class="file-item">
                                                    <div>
                                                        <i class="fas fa-file-pdf mr-2"></i>
                                                        <a href="{% url 'view_file' 'finished' material.material_id %}" target="_blank" class="file-link">{{ production_drawing_url|filename_from_path }}</a>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                            <div class="col-sm-4 col-md-4">
                                        <div class="form-group">
                                            <label for="processDrawing" class="form-label">工艺图纸</label>
                                    <input type="file" id="processDrawing" name="processDrawing" accept=".pdf" style="display: none;">
                                            <div class="upload-area" id="processDrawingArea" style="height: 120px;">
                                                <i class="fas fa-file-pdf fa-2x mb-2"></i>
                                                <p>点击上传工艺图纸</p>
                                            </div>
                                            <div id="processDrawingPreview" class="file-list">
                                                {% if process_drawing_url %}
                                                <div class="file-item">
                                                    <div>
                                                        <i class="fas fa-file-pdf mr-2"></i>
                                                        <a href="{% url 'view_file' 'workmanship' material.material_id %}" target="_blank" class="file-link">{{ process_drawing_url|filename_from_path }}</a>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                        </div>

                        <div class="row">

                            <div class="col-sm-6 col-md-6">
                                        <div class="form-group">
                                            <label for="inspectionSheet" class="form-label">检测单</label>
                                    <input type="file" id="inspectionSheet" name="inspectionSheet" accept=".xls,.xlsx" style="display: none;">
                                            <div class="upload-area" id="inspectionSheetArea" style="height: 120px;">
                                                <i class="fas fa-file-excel fa-2x mb-2"></i>
                                                <p>上传检测单 (仅支持Excel格式)</p>
                                            </div>
                                            <div id="inspectionSheetPreview" class="file-list">
                                                {% if inspection_sheet_files %}
                                                <div class="file-item">
                                                    <div>
                                                        <i class="fas fa-file-excel mr-2"></i>
                                                        <a href="{% url 'view_file' 'testingform' material.material_id %}" target="_blank" class="file-link">{{ inspection_sheet_files|filename_from_path }}</a>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                            <div class="col-sm-6 col-md-6">
                                        <div class="form-group">
                                            <label for="documentFolder" class="form-label">资料文件夹</label>
                                            <button type="button" id="documentFolderBtn" class="btn btn-primary btn-block">
                                                <i class="fas fa-folder-open mr-2"></i>打开资料文件夹
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 存货属性 -->
                    <div class="tab-pane fade" id="inventory" role="tabpanel" aria-labelledby="inventory-tab">
                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">客供 <span class="required">*</span></label>
                                    <div class="switch-container">
                                        <label class="switch">
                                            <input type="checkbox" id="customerSupply" name="customerSupply" value="是" {% if inventory_attrs.customer_supply == "是" %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="slider-text" id="customerSupplyText">{% if inventory_attrs.customer_supply == "是" %}是{% else %}否{% endif %}</span>
                                        <input type="hidden" name="customerSupplyHidden" value="{% if inventory_attrs.customer_supply == '是' %}是{% else %}否{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">外购 <span class="required">*</span></label>
                                    <div class="switch-container">
                                        <label class="switch">
                                            <input type="checkbox" id="outsourcing" name="outsourcing" value="是" {% if inventory_attrs.outsourcing == "是" %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="slider-text" id="outsourcingText">{% if inventory_attrs.outsourcing == "是" %}是{% else %}否{% endif %}</span>
                                        <input type="hidden" name="outsourcingHidden" value="{% if inventory_attrs.outsourcing == '是' %}是{% else %}否{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">销售 <span class="required">*</span></label>
                                    <div class="switch-container">
                                        <label class="switch">
                                            <input type="checkbox" id="sales" name="sales" value="是" {% if inventory_attrs.sales == "是" %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="slider-text" id="salesText">{% if inventory_attrs.sales == "是" %}是{% else %}否{% endif %}</span>
                                        <input type="hidden" name="salesHidden" value="{% if inventory_attrs.sales == '是' %}是{% else %}否{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">自制 <span class="required">*</span></label>
                                    <div class="switch-container">
                                        <label class="switch">
                                            <input type="checkbox" id="selfMade" name="selfMade" value="是" {% if inventory_attrs.self_made == "是" %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="slider-text" id="selfMadeText">{% if inventory_attrs.self_made == "是" %}是{% else %}否{% endif %}</span>
                                        <input type="hidden" name="selfMadeHidden" value="{% if inventory_attrs.self_made == '是' %}是{% else %}否{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">委外 <span class="required">*</span></label>
                                    <div class="switch-container">
                                        <label class="switch">
                                            <input type="checkbox" id="subcontract" name="subcontract" value="是" {% if inventory_attrs.subcontract == "是" %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="slider-text" id="subcontractText">{% if inventory_attrs.subcontract == "是" %}是{% else %}否{% endif %}</span>
                                        <input type="hidden" name="subcontractHidden" value="{% if inventory_attrs.subcontract == '是' %}是{% else %}否{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">生产耗用 <span class="required">*</span></label>
                                    <div class="switch-container">
                                        <label class="switch">
                                            <input type="checkbox" id="productionConsumption" name="productionConsumption" value="是" {% if inventory_attrs.production_consumption == "是" %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="slider-text" id="productionConsumptionText">{% if inventory_attrs.production_consumption == "是" %}是{% else %}否{% endif %}</span>
                                        <input type="hidden" name="productionConsumptionHidden" value="{% if inventory_attrs.production_consumption == '是' %}是{% else %}否{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">批号管理 <span class="required">*</span></label>
                                    <div class="switch-container">
                                        <label class="switch">
                                            <input type="checkbox" id="batchManagement" name="batchManagement" value="是" {% if inventory_attrs.batch_management == "是" %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="slider-text" id="batchManagementText">{% if inventory_attrs.batch_management == "是" %}是{% else %}否{% endif %}</span>
                                        <input type="hidden" name="batchManagementHidden" value="{% if inventory_attrs.batch_management == '是' %}是{% else %}否{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">虚拟件 <span class="required">*</span></label>
                                    <div class="switch-container">
                                        <label class="switch">
                                            <input type="checkbox" id="virtualItem" name="virtualItem" value="是" {% if inventory_attrs.virtual_item == "是" %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="slider-text" id="virtualItemText">{% if inventory_attrs.virtual_item == "是" %}是{% else %}否{% endif %}</span>
                                        <input type="hidden" name="virtualItemHidden" value="{% if inventory_attrs.virtual_item == '是' %}是{% else %}否{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="cuttingSize" class="form-label">下料尺寸</label>
                                    <input type="text" class="form-control" id="cuttingSize" name="cuttingSize" value="{{ inventory_attrs.cutting_size|default:'' }}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 供应商 -->
                    <div class="tab-pane fade" id="supplier" role="tabpanel" aria-labelledby="supplier-tab">
                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="castingSupplier" class="form-label">铸造</label>
                                    <input type="text" class="form-control" id="castingSupplier" name="castingSupplier" value="{{ material.material_supp_casting|default:'' }}">
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="machiningSupplier" class="form-label">机加</label>
                                    <input type="text" class="form-control" id="machiningSupplier" name="machiningSupplier" value="{{ material.material_supp_machining|default:'' }}">
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="sheetMetalSupplier" class="form-label">钣金</label>
                                    <input type="text" class="form-control" id="sheetMetalSupplier" name="sheetMetalSupplier" value="{{ material.material_supp_sheetmetal|default:'' }}">
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="procurementSupplier" class="form-label">采购</label>
                                    <input type="text" class="form-control" id="procurementSupplier" name="procurementSupplier" value="{{ material.material_supp_purchase|default:'' }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-6 col-md-3">
                                        <div class="form-group">
                                            <label for="procurementManager" class="form-label">采购负责人</label>
                                    <input type="text" class="form-control" id="procurementManager" name="procurementManager" value="{{ material.material_supp_prochasemanager|default:'' }}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 工艺 -->
                    <div class="tab-pane fade" id="process" role="tabpanel" aria-labelledby="process-tab">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="processRouteName" class="form-label">工艺路线名称</label>
                                            <select class="form-control select2" id="processRouteName" name="processRouteName">
                                                <option value="">请选择工艺路线</option>
                                                {% if current_process_route_name %}
                                                <option value="{{ current_process_route_name }}" selected>{{ current_process_route_name }}</option>
                                                {% endif %}
                                            </select>
                                            <div class="form-text">选择工艺路线后自动加载对应工艺信息</div>
                                        </div>
                                    </div>
                                </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-primary btn-sm" id="add-process-row">
                                    <i class="fas fa-plus"></i> 添加工序
                                </button>
                            </div>
                        </div>
                        <div>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="process-table">
                                    <thead class="thead-light">
                                        <tr>
                                            <th width="5%">序号</th>
                                            <th width="15%">工序名称 <span class="text-danger">*</span></th>
                                            <th width="10%">工时</th>
                                            <th width="15%">设备</th>
                                            <th width="10%">工价</th>
                                            <th width="10%">加工方式</th>
                                            <th width="15%">委外供应商</th>
                                            <th width="10%">委外单价</th>
                                            <th width="10%">关键工序</th>
                                            <th width="5%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="process-tbody">
                                        <!-- 动态添加的行将在这里 -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="empty-process-message" class="text-center text-muted py-3">
                                <i class="fas fa-info-circle"></i> 请点击"添加工序"按钮添加工艺步骤
                            </div>
                        </div>
                    </div>

                    
                </div>
            </div>
        </div>

                        <!-- 提交按钮 -->
                        <!-- 调试信息：source_type={{ request.GET.source_type }}, iframe={{ request.GET.iframe }}, is_preview={{ is_preview }} -->
                        <!-- iframe模式下隐藏所有内部按钮，因为操作都通过外部按钮触发 -->
                        <div class="row mt-4" {% if request.GET.iframe == '1' %}style="display:none;"{% endif %}>
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <div>
                                        {% if not is_preview %}
                                        <button type="button" class="btn btn-secondary" id="resetForm">
                                            <i class="fas fa-undo mr-2"></i>重置
                                        </button>
                                        <!-- 根据来源控制保存按钮的显示 -->
                                        <button type="submit" class="btn btn-primary ml-2" id="saveButton">
                                            <i class="fas fa-save mr-2"></i>{% if material %}保存物料{% else %}新增物料{% endif %}
                                        </button>
                                        {% else %}
                                        <!-- 调试：预览模式，按钮被隐藏 -->
                                        <span class="text-muted">预览模式 - 按钮已隐藏</span>
                                        {% endif %}

                                <a href="javascript:history.back()" class="btn btn-outline-secondary ml-2">
                                    <i class="fas fa-arrow-left mr-2"></i>返回列表
                                </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 隐藏字段，记录来源页面类型 -->
                        <input type="hidden" id="sourceType" name="sourceType" value="{{ request.GET.source_type|default:'material_list' }}">
                    </form>
                </div>
            </section>
        </div>
{% endblock %}

{% block extra_css %}
    <style>
        .content-wrapper {
            margin-left: 0 !important;
        }
        
        .form-section {
            background: #fff;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        /* 预览模式样式 */
        .mode-preview {
            background-color: #cff4fc;
            color: #055160;
            border: 1px solid #9eeaf9;
        }
        
        .preview-mode input[type="text"],
        .preview-mode input[type="date"],
        .preview-mode input[type="number"],
        .preview-mode textarea,
        .preview-mode select {
            background-color: #f8f9fa;
            pointer-events: none;
            border-color: #dee2e6;
            color: #495057;
        }
        
        .preview-mode .select2-container {
            pointer-events: none;
        }
        
        .preview-mode .select2-selection {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
        }
        
        .preview-mode .upload-area {
            display: none;
        }
        
        .preview-mode .switch-container .switch {
            pointer-events: none;
            opacity: 0.8;
        }
        
        .preview-mode .remove-file,
        .preview-mode .remove-process {
            display: none;
        }
        
        .preview-mode #process_search,
        .preview-mode .search-results {
            display: none;
        }
        
        .preview-mode .process-step {
            cursor: default;
        }
        
        .required {
            color: #dc3545;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-control {
            font-size: 0.875rem;
            padding: 0.5rem 0.75rem;
        }

        .form-text {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .upload-area p {
            margin: 0;
            font-size: 0.875rem;
            color: #6c757d;
        }

        .upload-area i {
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-check-label {
            font-size: 0.875rem;
        }

        .tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            padding: 1.5rem;
            background: white;
            min-height: 500px;
        max-height: 620px;
        }

        .row {
            margin-bottom: 0.5rem;
        }

        .row:last-child {
            margin-bottom: 0;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .form-label {
                font-size: 0.8rem;
            }
            
            .form-control {
                font-size: 0.8rem;
                padding: 0.4rem 0.6rem;
            }
            
            .upload-area {
                min-height: 80px;
                padding: 0.75rem;
            }
            
            .upload-area p {
                font-size: 0.8rem;
            }
        }

        /* 确保所有表单元素高度一致 */
        .form-control, .form-select {
            height: calc(2.25rem + 2px);
        }

        /* 单选框组对齐 */
        .form-check-inline {
            margin-right: 1rem;
        }

        .form-check-inline:last-child {
            margin-right: 0;
        }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .file-list {
            margin-top: 1rem;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 0.5rem;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 120px;
            border-radius: 0.375rem;
            border: 1px solid #dee2e6;
        }
        
        .image-preview-container {
            min-height: 120px;
            border: 1px dashed #ccc;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            background-color: #f8f9fa;
        }
        
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
        }
        
        .nav-tabs .nav-link.active {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }
        
        .tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            padding: 1.5rem;
            background: white;
        }
        
        .mode-indicator {
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }
        
        .mode-new {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .mode-edit {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .auto-generated {
            background-color: #e9ecef;
            font-style: italic;
        }
        
        .pdf-preview {
            width: 100%;
            height: 400px;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }

    .mode-loading {
        background-color: #e9f5fe;
        color: #0c63e4;
        border: 1px solid #b6dffd;
        animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
    }

    /* 工艺路线动态表格样式 */
    #process-table {
        margin-bottom: 0;
        width: 100%;
        table-layout: fixed;
    }

    #process-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        border: 1px solid #dee2e6;
    }

    #process-table td {
        vertical-align: middle;
        border: 1px solid #dee2e6;
    }

    .process-input {
        width: 100%;
    }

    .process-input:focus {
        outline: none;
        background-color: #fff3cd;
        border: 1px solid #ffc107;
        border-radius: 3px;
    }

    .process-select {
        width: 100%;
    }

    .process-select:focus {
        outline: none;
        background-color: #fff3cd;
        border: 1px solid #ffc107;
        border-radius: 3px;
    }

    .process-checkbox {
        transform: scale(1.2);
    }

    .btn-remove-row {
        padding: 2px 6px;
        font-size: 12px;
    }

    #empty-process-message {
        display: none;
    }

    .table-responsive {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }

    .drag-handle {
        cursor: move;
        color: #6c757d;
    }

    .drag-handle:hover {
        color: #495057;
    }

    .ui-sortable-helper {
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .ui-sortable-placeholder {
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        visibility: visible !important;
        height: 50px;
    }
        
        /* 标签页错误状态样式 */
        .nav-tabs .nav-link.tab-error {
            color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
            border-color: #dc3545;
            position: relative;
        }
        
        .nav-tabs .nav-link.tab-error::after {
            content: "!";
            position: absolute;
            top: 2px;
            right: 5px;
            color: #dc3545;
            font-weight: bold;
        }

        /* 只读字段样式 */
        .readonly-field {
            background-color: #f8f9fa !important;
            cursor: not-allowed;
        }

        .readonly-field:focus {
            box-shadow: none;
            border-color: #ced4da;
        }
    </style>
<!-- Select2 CSS -->
<link href="/static/css/select2.min.css" rel="stylesheet" />
<link href="/static/css/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<link rel="stylesheet" href="/static/css/jquery-ui.css">
{% endblock %}

{% block extra_js %}
    <!-- Select2 JS -->
    <script src="/static/js/select2.min.js"></script>
    <script src="/static/js/form-logic.js"></script>
<script src="/static/js/jquery-ui.min.js"></script>
<script>
    // 初始化后台预览模式
    {% if is_preview %}
    document.addEventListener("DOMContentLoaded", function() {
        // 添加预览模式样式
        document.querySelector('form').classList.add('preview-mode');
        // 禁用所有表单元素（但不包括标签页按钮）
        document.querySelectorAll('input, select, textarea').forEach(function(el) {
            el.setAttribute('readonly', true);
            el.setAttribute('disabled', true);
        });
        // 隐藏上传区域
        document.querySelectorAll('.upload-area').forEach(function(el) {
            el.style.display = 'none';
        });
        // 不再禁用标签页，允许自由切换
        // 隐藏所有编辑按钮
        document.querySelectorAll('.remove-file, .remove-process').forEach(function(el) {
            el.style.display = 'none';
        });
        
        // 确保标签页点击事件正常工作
        document.querySelectorAll('#materialTabs button').forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                var target = this.getAttribute('data-bs-target');
                
                // 移除所有标签页的active类
                document.querySelectorAll('#materialTabs button').forEach(function(btn) {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.tab-pane').forEach(function(pane) {
                    pane.classList.remove('show', 'active');
                });
                
                // 添加当前标签页的active类
                this.classList.add('active');
                document.querySelector(target).classList.add('show', 'active');
            });
        });
    });
    {% endif %}
    
    // 其他脚本
    // 确保标签页正确初始化
    $(document).ready(function() {
        // 存储原始required字段状态
        const originalRequired = {};
        
        // 保存所有required字段的原始状态
        function saveRequiredFields() {
            $('form [required]').each(function() {
                originalRequired[$(this).attr('name')] = true;
            });
        }
        
        // 仅对可见tab中的字段设置required
        function updateRequiredAttributes() {
            // 移除所有required属性
            $('form [required]').removeAttr('required');
            
            // 仅为当前可见tab中的字段添加required属性
            $('.tab-pane.active [data-required="true"]').attr('required', 'required');
        }
        
        // 初始化时保存所有required字段
        saveRequiredFields();
        
        // 将所有required标记为data-required属性
        $('form [required]').attr('data-required', 'true');
        
        // 清除标签页错误状态
        function clearTabErrors() {
            $('#materialTabs button').removeClass('tab-error');
        }
        
        // 处理标签页切换
        $('#materialTabs button').on('click', function() {
            // 在切换前更新required状态
            updateRequiredAttributes();
            clearTabErrors();
        });
        
        // 输入框值改变时清除错误状态
        $('input, select, textarea').on('change input', function() {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
            
            // 检查当前tab是否还有错误，如果没有则移除错误状态
            const tabPane = $(this).closest('.tab-pane');
            if (tabPane.length && tabPane.find('.is-invalid').length === 0) {
                const tabId = tabPane.attr('id');
                $(`#materialTabs button[data-bs-target="#${tabId}"]`).removeClass('tab-error');
            }
        });
        
        // 表单提交前验证所有必填字段
        $('#materialBomForm').on('submit', function(e) {
            // 临时恢复所有required属性用于验证
            $('[data-required="true"]').attr('required', 'required');
            
            // 手动验证必填字段
            let hasEmptyRequiredFields = false;
            $('[data-required="true"]').each(function() {
                if (!$(this).val()) {
                    hasEmptyRequiredFields = true;
                    $(this).addClass('is-invalid');
                    return false; // 找到一个就停止循环
                }
            });
            
            if (hasEmptyRequiredFields) {
                e.preventDefault();
                e.stopImmediatePropagation();
                
                // 找到第一个无效字段
                const firstInvalidField = $('[data-required="true"]:invalid').first();
                if (firstInvalidField.length) {
                    const tabPane = firstInvalidField.closest('.tab-pane');
                    const tabId = tabPane.attr('id');
                    
                    // 切换到包含无效字段的标签页
                    $(`#materialTabs button[data-bs-target="#${tabId}"]`).trigger('click');
                    
                    // 聚焦到第一个无效字段
                    setTimeout(() => {
                        firstInvalidField.focus();
                    }, 100);
                }
                
                // 显示验证提示
                showNotification('表单验证', '请填写所有必填字段', 'warning');
                
                // 恢复required状态优化
                updateRequiredAttributes();
                return false;
            }
            
            // 如果通过了验证，恢复所有required属性，以便服务器端验证
            // 什么都不做，保留所有required属性
        });
        
        // 初始化时更新一次
        updateRequiredAttributes();

        // 初始化工艺路线名称的Select2，支持AJAX搜索
        $('#processRouteName').select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: '请选择或搜索工艺路线',
            allowClear: true,
            ajax: {
                url: '{% url "material_add" %}',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        search_process_route: 1,
                        q: params.term, // 搜索词
                        page: params.page || 1
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    if (data.success && data.process_routes) {
                        return {
                            results: data.process_routes.map(function(route) {
                                return {
                                    id: route.name,
                                    text: route.name + ' (' + route.no + ')',
                                    data: route
                                };
                            }),
                            pagination: {
                                more: data.has_more || false
                            }
                        };
                    } else {
                        return {
                            results: []
                        };
                    }
                },
                cache: true
            },
            minimumInputLength: 0, // 允许空搜索显示所有选项
            escapeMarkup: function (markup) {
                return markup; // 让我们的自定义格式通过
            },
            templateResult: function (repo) {
                if (repo.loading) {
                    return repo.text;
                }

                if (repo.data) {
                    return $('<div>' + repo.data.name + ' <small class="text-muted">(' + repo.data.no + ')</small></div>');
                }

                return repo.text;
            },
            templateSelection: function (repo) {
                return repo.data ? repo.data.name : repo.text;
            }
        });

        // 检查是否处于预览模式
        if ($('#formMode').val() === 'preview') {
            // 禁用所有输入字段
            $('input, select, textarea').attr('readonly', true).attr('disabled', true);
            // 隐藏上传区域
            $('.upload-area').hide();
            // 禁用标签页导航
           
            // 隐藏所有编辑按钮
            $('.remove-file, .remove-process').hide();
        }
        // 确保第一个标签页被激活
        var firstTabButton = $('#materialTabs button.active');
        if (firstTabButton.length) {
            var target = firstTabButton.data('bs-target');
            $(target).addClass('show active');
        }
        
        // 修复Bootstrap 4/5的兼容性问题
        if (!$._data) {
            // 如果jQuery的_data方法不存在（jQuery 3.0+），使用自定义方法
            $('#materialTabs button').each(function() {
                var $this = $(this);
                var events = $._data ? $._data($this[0], 'events') : null;
                
                // 如果没有绑定点击事件，手动绑定
                    $this.on('click', function(e) {
                        e.preventDefault();
                        var target = $(this).data('bs-target');
                        
                        // 移除所有标签页的active类
                        $('#materialTabs button').removeClass('active');
                        $('.tab-pane').removeClass('show active');
                        
                        // 添加当前标签页的active类
                        $(this).addClass('active');
                        $(target).addClass('show active');
                    
                    // 处理表单验证
                    updateRequiredAttributes();
                    });
            });
        }
        

        
        // 工艺相关功能
        // 设备数据
        let equipmentData = [];

        // 获取设备数据
        $.ajax({
            url: '{% url "material_add" %}?get_equipment=1',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if (data.success) {
                    equipmentData = data.equipment;
                   

                    // 加载现有数据
                    loadFromHiddenFields();
                } else {
                    console.error('加载设备数据失败:', data.error);
                    // 即使设备数据加载失败，也要加载现有数据
                    loadFromHiddenFields();
                }
            },
            error: function(xhr, status, error) {
                console.error('加载设备数据请求失败:', error);
                // 即使设备数据加载失败，也要加载现有数据
                loadFromHiddenFields();
            }
        });

        // 检查是否有工艺内容，更新空提示信息
        function updateEmptyMessage() {
            const rowCount = $('#process-tbody tr').length;
            if (rowCount === 0) {
                $('#empty-process-message').show();
                $('#process-table').hide();
            } else {
                $('#empty-process-message').hide();
                $('#process-table').show();
            }
        }

        // 添加工序行
        $('#add-process-row').on('click', function() {
            addProcessRow();
        });

        // 添加工序行函数
        function addProcessRow(data = {}) {
            const rowIndex = $('#process-tbody tr').length;
            const sequenceNumber = rowIndex + 1;

            // 构建设备选择框选项
            let equipmentOptions = '<option value="">选择设备</option>';
            equipmentData.forEach(equipment => {
                const selected = data.device === equipment.id ? 'selected' : '';
                equipmentOptions += `<option value="${equipment.id}" ${selected}>${equipment.name} (${equipment.no})</option>`;
            });

            // 格式化价格（保留两位小数）
            const formatPrice = (price) => {
                if (!price || price === '') return '';
                const num = parseFloat(price);
                return isNaN(num) ? price : num.toFixed(2);
            };

            const row = `
                <tr data-index="${rowIndex}">
                    <td class="text-center">
                        <span class="sequence-number">${sequenceNumber}</span>
                        <i class="fas fa-grip-vertical drag-handle ml-2" style="cursor: move;"></i>
                    </td>
                    <td>
                        <select class="form-control select2 process-select" name="process_content_${rowIndex}"
                                id="process_content_${rowIndex}" required>
                            <option value="">请选择工序</option>
                        </select>
                    </td>
                    <td>
                        <input type="text" class="process-input" name="process_time_${rowIndex}"
                               placeholder="工时" value="${data.time || ''}">
                    </td>
                    <td>
                        <select class="process-select" name="process_device_${rowIndex}">
                            ${equipmentOptions}
                        </select>
                    </td>
                    <td>
                        <input type="text" class="process-input price-input" name="process_price_${rowIndex}"
                               placeholder="工价" value="${formatPrice(data.price)}">
                    </td>
                    <td>
                        <select class="process-select" name="process_type_${rowIndex}">
                            <option value="">请选择</option>
                            <option value="自制" ${data.type === '自制' ? 'selected' : ''}>自制</option>
                            <option value="委外" ${data.type === '委外' ? 'selected' : ''}>委外</option>
                        </select>
                    </td>
                    <td>
                        <input type="text" class="process-input" name="process_outsupplier_${rowIndex}"
                               placeholder="委外供应商" value="${data.outsupplier || ''}">
                    </td>
                    <td>
                        <input type="text" class="process-input price-input" name="process_outprice_${rowIndex}"
                               placeholder="委外单价" value="${formatPrice(data.outprice)}">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" class="process-checkbox" name="process_important_${rowIndex}"
                               value="1" ${data.important === '1' ? 'checked' : ''}>
                    </td>
                    <td class="text-center">
                        <button type="button" class="btn btn-danger btn-sm btn-remove-row">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            $('#process-tbody').append(row);
            updateEmptyMessage();

            // 只在不跳过时更新序号和属性
            if (!window.skipSequenceUpdate) {
                updateSequenceNumbersOnly();
            }

            // 绑定删除事件
            bindRemoveEvents();

            // 绑定输入事件
            bindInputEvents();

            // 绑定价格输入格式化事件
            bindPriceFormatEvents();

            // 初始化新添加行的工序名称Select2
            initProcessSelect2(`#process_content_${rowIndex}`, data.content);
        }

        // 绑定删除事件
        function bindRemoveEvents() {
            $('.btn-remove-row').off('click').on('click', function() {
                $(this).closest('tr').remove();
                updateRowIndices();
                updateEmptyMessage();
                updateHiddenFields();
            });
        }

        // 绑定输入事件
        function bindInputEvents() {
            $('.process-input, .process-select, .process-checkbox').off('input change').on('input change', function() {
                updateHiddenFields();
            });
        }

        // 绑定价格格式化事件
        function bindPriceFormatEvents() {
            $('.price-input').off('blur').on('blur', function() {
                const value = $(this).val().trim();
                if (value && value !== '') {
                    const num = parseFloat(value);
                    if (!isNaN(num)) {
                        $(this).val(num.toFixed(2));
                        updateHiddenFields();
                    }
                }
            });
        }

        // 初始化工序名称Select2
        function initProcessSelect2(selector, selectedValue = '') {
            console.log('初始化Select2:', selector, '选中值:', selectedValue);

            // 检查元素是否存在
            if (!$(selector).length) {
                console.error('Select2初始化失败：元素不存在', selector);
                return;
            }

            // 检查是否已经初始化过Select2
            if ($(selector).hasClass('select2-hidden-accessible')) {
                console.log('Select2已经初始化过，跳过重复初始化', selector);
                return;
            }

            $(selector).select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: '请选择工序',
                allowClear: false,
                ajax: {
                    url: '{% url "material_add" %}',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            search_processes: 1,
                            q: params.term || ''
                        };
                    },
                    processResults: function (data) {
                        if (data.success && data.processes) {
                            return {
                                results: data.processes.map(function(process) {
                                    return {
                                        id: process.name,
                                        text: process.name,
                                        code: process.code
                                    };
                                })
                            };
                        } else {
                            return { results: [] };
                        }
                    },
                    cache: true
                },
                minimumInputLength: 0
            });

            // 如果有选中值，设置选中项
            if (selectedValue) {
                console.log('设置Select2选中值:', selectedValue);

                // 先创建一个临时选项并设置为选中
                const tempOption = new Option(selectedValue, selectedValue, true, true);
                $(selector).append(tempOption);

                // 触发Select2更新显示
                $(selector).trigger('change');

                // 异步获取完整的工序数据，包括code，但不改变当前选中状态
                $.ajax({
                    url: '{% url "material_add" %}',
                    data: { search_processes: 1, q: selectedValue },
                    dataType: 'json',
                    success: function(data) {
                        if (data.success && data.processes) {
                            const process = data.processes.find(p => p.name === selectedValue);
                            if (process) {
                                // 更新选项的data属性，但保持选中状态
                                const $option = $(selector).find('option[value="' + selectedValue + '"]');
                                if ($option.length > 0) {
                                    $option.data('code', process.code);
                                    console.log('已设置工序代码:', process.code, '对应工序:', selectedValue);
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.warn('获取工序数据失败，但保持当前选中值:', error);
                    }
                });
            }

            // 绑定change事件
            $(selector).on('change', function() {
                updateHiddenFields();
            });
        }

        // 只更新序号和属性，不重新初始化Select2
        function updateSequenceNumbersOnly() {
            $('#process-tbody tr').each(function(index) {
                $(this).find('.sequence-number').text(index + 1);
                $(this).attr('data-index', index);

                // 更新所有input和select的name属性
                $(this).find('input, select').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        const baseName = name.replace(/_\d+$/, '');
                        $(this).attr('name', baseName + '_' + index);

                        // 如果是工序选择框，也要更新ID
                        if (baseName === 'process_content') {
                            $(this).attr('id', baseName + '_' + index);
                        }
                    }
                });
            });
        }

        // 更新序号并重新初始化Select2（仅在必要时使用）
        function updateSequenceNumbers() {
            $('#process-tbody tr').each(function(index) {
                $(this).find('.sequence-number').text(index + 1);
                $(this).attr('data-index', index);

                // 更新所有input和select的name属性
                $(this).find('input, select').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        const baseName = name.replace(/_\d+$/, '');
                        $(this).attr('name', baseName + '_' + index);

                        // 如果是工序选择框，也要更新ID
                        if (baseName === 'process_content') {
                            $(this).attr('id', baseName + '_' + index);
                        }
                    }
                });

                // 重新初始化工序Select2
                const processSelect = $(this).find('select[name^="process_content_"]');
                if (processSelect.length > 0) {
                    let currentValue = '';

                    // 如果已经初始化了Select2，先获取当前选中的值
                    if (processSelect.hasClass('select2-hidden-accessible')) {
                        try {
                            const selectedData = processSelect.select2('data');
                            if (selectedData && selectedData.length > 0) {
                                currentValue = selectedData[0].id || selectedData[0].text || '';
                            }
                        } catch (e) {
                            currentValue = processSelect.val() || '';
                        }

                        // 销毁Select2
                        processSelect.select2('destroy');
                    } else {
                        currentValue = processSelect.val() || '';
                    }

                    // 只有当有选中值时才重新初始化
                    if (currentValue) {
                        console.log('重新初始化Select2:', processSelect.attr('id'), '选中值:', currentValue);
                        initProcessSelect2('#' + processSelect.attr('id'), currentValue);
                    } else {
                        // 如果没有选中值，初始化为空的Select2
                        initProcessSelect2('#' + processSelect.attr('id'), '');
                    }
                }
            });
        }

        // 更新行索引
        function updateRowIndices() {
            updateSequenceNumbers();
        }

        // 更新隐藏字段
        function updateHiddenFields() {
            const contents = [];
            const times = [];
            const devices = [];
            const prices = [];
            const types = [];
            const outsuppliers = [];
            const outprices = [];
            const importants = [];
            const codes = [];

            $('#process-tbody tr').each(function() {
                const $row = $(this);
                const processSelect = $row.find('select[name*="process_content"]');
                const processName = processSelect.val() || '';

                // 从Select2的数据中获取code
                let processCode = '';
                try {
                    if (processSelect.hasClass('select2-hidden-accessible')) {
                        const selectedData = processSelect.select2('data');
                        if (selectedData && selectedData.length > 0 && selectedData[0].code) {
                            processCode = selectedData[0].code;
                        }
                    }
                } catch (error) {
                    console.warn('获取Select2数据时出错:', error);
                }

                contents.push(processName);
                codes.push(processCode);
                times.push($row.find('input[name*="process_time"]').val() || '');
                devices.push($row.find('select[name*="process_device"]').val() || '');
                prices.push($row.find('input[name*="process_price"]').val() || '');
                types.push($row.find('select[name*="process_type"]').val() || '');
                outsuppliers.push($row.find('input[name*="process_outsupplier"]').val() || '');
                outprices.push($row.find('input[name*="process_outprice"]').val() || '');
                importants.push($row.find('input[name*="process_important"]').is(':checked') ? '1' : '0');
            });

            $('#material_processroute').val(contents.join('|'));
            $('#material_ProcessrouteCode').val(codes.join('|'));
            $('#material_ProcessrouteTimes').val(times.join('|'));
            $('#material_ProcessrouteDevice').val(devices.join('|'));
            $('#material_ProcessroutePrice').val(prices.join('|'));
            $('#material_ProcessrouteType').val(types.join('|'));
            $('#material_ProcessrouteOutSupplier').val(outsuppliers.join('|'));
            $('#material_ProcessrouteOutPrice').val(outprices.join('|'));
            $('#material_ProcessrouteImportant').val(importants.join('|'));
        }

        // 初始化表格排序
        function initSortable() {
            $('#process-tbody').sortable({
                handle: '.drag-handle',
                placeholder: 'ui-sortable-placeholder',
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    return $helper;
                },
                update: function(event, ui) {
                    updateSequenceNumbers();
                    updateHiddenFields();
                }
            });
        }

        // 从隐藏字段加载数据
        function loadFromHiddenFields() {
            const contentValue = $('#material_processroute').val() || '';
            const codeValue = $('#material_ProcessrouteCode').val() || '';
            const timesValue = $('#material_ProcessrouteTimes').val() || '';
            const devicesValue = $('#material_ProcessrouteDevice').val() || '';
            const pricesValue = $('#material_ProcessroutePrice').val() || '';
            const typesValue = $('#material_ProcessrouteType').val() || '';
            const outsuppliersValue = $('#material_ProcessrouteOutSupplier').val() || '';
            const outpricesValue = $('#material_ProcessrouteOutPrice').val() || '';
            const importantsValue = $('#material_ProcessrouteImportant').val() || '';

            console.log('加载隐藏字段数据:', {
                content: contentValue,
                code: codeValue,
                times: timesValue
            });

            // 处理None值和0值
            const cleanValue = (val) => {
                if (!val || val === 'None' || val === 'null' || val === '0') return '';
                return val;
            };

            const contents = cleanValue(contentValue).split('|').filter(x => x.trim());
            const codes = cleanValue(codeValue).split('|');
            const times = cleanValue(timesValue).split('|');
            const devices = cleanValue(devicesValue).split('|');
            const prices = cleanValue(pricesValue).split('|');
            const types = cleanValue(typesValue).split('|');
            const outsuppliers = cleanValue(outsuppliersValue).split('|');
            const outprices = cleanValue(outpricesValue).split('|');
            const importants = cleanValue(importantsValue).split('|');

            // 临时禁用序号更新，避免重复调用
            window.skipSequenceUpdate = true;

            contents.forEach((content, index) => {
                if (content.trim()) {
                    addProcessRow({
                        content: content,
                        code: cleanValue(codes[index]) || '',
                        time: cleanValue(times[index]) || '',
                        device: cleanValue(devices[index]) || '',
                        price: cleanValue(prices[index]) || '',
                        type: cleanValue(types[index]) || '',
                        outsupplier: cleanValue(outsuppliers[index]) || '',
                        outprice: cleanValue(outprices[index]) || '',
                        important: cleanValue(importants[index]) || '0'
                    });
                }
            });

            // 恢复序号更新并统一更新一次
            window.skipSequenceUpdate = false;
            updateSequenceNumbersOnly();
        }

        // 工艺路线名称选择时查询工艺路线
        $('#processRouteName').on('change', function() {
            const routeName = $(this).val().trim();
            if (!routeName) {
                // 清空工艺信息
                $('#process-tbody').empty();
                $('#material_processroute').val('');
                $('#material_ProcessrouteTimes').val('');
                $('#material_ProcessrouteDevice').val('');
                $('#material_ProcessroutePrice').val('');
                $('#material_ProcessrouteType').val('');
                $('#material_ProcessrouteOutSupplier').val('');
                $('#material_ProcessrouteOutPrice').val('');
                $('#material_ProcessrouteImportant').val('');
                return;
            }

            // 查询工艺路线
            $.ajax({
                url: '{% url "material_add" %}?get_process_route=1&route_name=' + encodeURIComponent(routeName),
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.success && data.process_route) {
                        // 清空现有表格
                        $('#process-tbody').empty();

                        // 更新隐藏字段
                        $('#material_processroute').val(data.process_route.content || '');
                        $('#material_ProcessrouteTimes').val(data.process_route.times || '');
                        $('#material_ProcessrouteDevice').val(data.process_route.device || '');
                        $('#material_ProcessroutePrice').val(data.process_route.price || '');
                        $('#material_ProcessrouteType').val(data.process_route.type || '');
                        $('#material_ProcessrouteOutSupplier').val(data.process_route.outsupplier || '');
                        $('#material_ProcessrouteOutPrice').val(data.process_route.outprice || '');
                        $('#material_ProcessrouteImportant').val(data.process_route.important || '');

                        // 重新加载表格数据
                        loadFromHiddenFields();

                        // 通知用户成功
                        showNotification('查询成功', '已加载工艺路线信息', 'success');
                    } else {
                        console.error('查询工艺路线失败:', data.error || '未找到对应工艺路线');
                        // 通知用户失败
                        showNotification('查询失败', data.error || '未找到对应工艺路线', 'warning');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('查询工艺路线请求失败:', error);
                    // 通知用户错误
                    showNotification('查询错误', '服务器错误，请稍后重试', 'error');
                }
            });
        });

        // 初始化
        updateEmptyMessage();
        initSortable();


        
        // 设置显示通知消息的函数
        function showNotification(title, message, type) {
            // 确保通知容器存在
            let container = $('.notification-container');
            if (container.length === 0) {
                container = $('<div class="notification-container"></div>');
                $('body').append(container);
            }
            
            // 创建通知元素
            const notification = $(`
                <div class="notification alert alert-${type} alert-dismissible fade show">
                    <strong>${title}</strong> ${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `);
            
            // 添加到容器
            container.append(notification);
            
            // 自动关闭
            setTimeout(function() {
                notification.addClass('fade-out');
                setTimeout(function() {
                    notification.remove();
                }, 300); // 等待动画完成
            }, 3000);
            
            // 点击关闭按钮时移除通知
            notification.find('.close').on('click', function() {
                notification.addClass('fade-out');
                setTimeout(function() {
                    notification.remove();
                }, 300);
            });
        }
        
        // 自动隐藏提示信息
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);

        // 确保页面加载时正确初始化工艺路线显示
        // 如果存在工艺路线内容，但工艺路线名称不为空，清除工艺路线名称
        if ($('#material_processroute').val() && $('#processRouteName').val()) {
            $('#processRouteName').val('').trigger('change');
        }

        // 文件上传功能
     

        // 客户图纸上传
        $('#customerDrawingArea').on('click', function() {
            $('#customerDrawing').click();
        });

        $('#customerDrawing').on('change', function() {
            const file = this.files[0];
            if (file) {
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

                // 更新预览区域
                $('#customerDrawingPreview').html(`
                    <div class="file-item">
                        <div>
                            <i class="fas fa-file-pdf mr-2"></i>
                            <span class="file-name">${fileName}</span>
                            <small class="text-muted">(${fileSize} MB)</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger remove-file">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);

                // 绑定删除按钮事件
                $('#customerDrawingPreview .remove-file').on('click', function() {
                    $('#customerDrawing').val('');
                    $('#customerDrawingPreview').empty();
                });
            }
        });

        // 生产图纸上传
        $('#productionDrawingArea').on('click', function() {
            $('#productionDrawing').click();
        });

        $('#productionDrawing').on('change', function() {
            const file = this.files[0];
            if (file) {
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

                // 更新预览区域
                $('#productionDrawingPreview').html(`
                    <div class="file-item">
                        <div>
                            <i class="fas fa-file-pdf mr-2"></i>
                            <span class="file-name">${fileName}</span>
                            <small class="text-muted">(${fileSize} MB)</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger remove-file">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);

                // 绑定删除按钮事件
                $('#productionDrawingPreview .remove-file').on('click', function() {
                    $('#productionDrawing').val('');
                    $('#productionDrawingPreview').empty();
                });
            }
        });

        // 工艺图纸上传
        $('#processDrawingArea').on('click', function() {
            $('#processDrawing').click();
        });

        $('#processDrawing').on('change', function() {
            const file = this.files[0];
            if (file) {
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

                // 更新预览区域
                $('#processDrawingPreview').html(`
                    <div class="file-item">
                        <div>
                            <i class="fas fa-file-pdf mr-2"></i>
                            <span class="file-name">${fileName}</span>
                            <small class="text-muted">(${fileSize} MB)</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger remove-file">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);

                // 绑定删除按钮事件
                $('#processDrawingPreview .remove-file').on('click', function() {
                    $('#processDrawing').val('');
                    $('#processDrawingPreview').empty();
                });
            }
        });

        // 检测单上传
        $('#inspectionSheetArea').on('click', function() {
            $('#inspectionSheet').click();
        });

        $('#inspectionSheet').on('change', function() {
            const file = this.files[0];
            if (file) {
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

                // 更新预览区域
                $('#inspectionSheetPreview').html(`
                    <div class="file-item">
                        <div>
                            <i class="fas fa-file-excel mr-2"></i>
                            <span class="file-name">${fileName}</span>
                            <small class="text-muted">(${fileSize} MB)</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger remove-file">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);

                // 绑定删除按钮事件
                $('#inspectionSheetPreview .remove-file').on('click', function() {
                    $('#inspectionSheet').val('');
                    $('#inspectionSheetPreview').empty();
                });
            }
        });

        // 为现有文件的删除按钮绑定事件
        $(document).on('click', '.remove-file', function() {
            const $fileItem = $(this).closest('.file-item');
            const $preview = $(this).closest('.file-list');

            // 确认删除
            if (confirm('确定要删除这个文件吗？')) {
                // 清空对应的文件输入框
                if ($preview.attr('id') === 'customerDrawingPreview') {
                    $('#customerDrawing').val('');
                } else if ($preview.attr('id') === 'productionDrawingPreview') {
                    $('#productionDrawing').val('');
                } else if ($preview.attr('id') === 'processDrawingPreview') {
                    $('#processDrawing').val('');
                } else if ($preview.attr('id') === 'inspectionSheetPreview') {
                    $('#inspectionSheet').val('');
                }

                // 移除文件项
                $fileItem.remove();
            }
        });

        // 为产品图片的删除按钮绑定事件（单独处理，因为结构不同）
        $(document).on('click', '.remove-image', function() {
            if (confirm('确定要删除这个图片吗？')) {
                $('#productImage').val('');
                $('#productImagePreview').empty();
            }
        });

        // 添加客户图号查询物料并更新edit_id的处理
        $('#drawingNumber').on('blur', function() {
            const customerCode = $('#customerName').find('option:selected').data('code');
            const drawingNumber = $(this).val().trim();
            
            if (customerCode && drawingNumber) {
                // 查询物料信息
                $.ajax({
                    url: '{% url "material_add" %}?query_material=1&customer_code=' + customerCode + '&drawing_no=' + drawingNumber,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        if (data.success && data.material) {
                            // 更新edit_id字段
                            $('#edit_id').val(data.material.material_id);
                            
                            // 更新表单模式
                            $('#formMode').val('edit');
                            // 通知用户成功
                            showNotification('查询成功', '已加载物料信息，可以进行编辑', 'success');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('查询物料信息请求失败:', error);
                    }
                });
            }
        });
        
        // 检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const bomTreeMode = urlParams.get('bom_tree_mode');
        const isSuccess = urlParams.get('success');
        const isIframe = urlParams.get('iframe');
        const sourceType = urlParams.get('source_type');

        // 检查是否在iframe中（即使URL参数中没有iframe=1）
        const inIframe = window.parent !== window;

        // 添加调试信息
        console.log('页面加载完成，检查参数:');
        console.log('- URL:', window.location.href);
        console.log('- search:', window.location.search);
        console.log('- bomTreeMode:', bomTreeMode);
        console.log('- isSuccess:', isSuccess);
        console.log('- isIframe:', isIframe);
        console.log('- sourceType:', sourceType);
        console.log('- inIframe:', inIframe);

        // 备用方法：直接检查URL字符串（支持多种成功标识）
        const urlContainsSuccess = window.location.href.includes('success=true') ||
                                  window.location.href.includes('success=1');
        console.log('- URL包含success标识:', urlContainsSuccess);

        // 检查是否有成功提示消息（Django messages）
        const hasSuccessMessage = $('.alert-success').length > 0 || $('.alert.alert-success').length > 0;
        console.log('- 页面有成功消息:', hasSuccessMessage);

        // 详细检查各种成功条件
        console.log('成功条件检查:');
        console.log('- isSuccess === "true":', isSuccess === 'true');
        console.log('- isSuccess === "1":', isSuccess === '1');
        console.log('- urlContainsSuccess:', urlContainsSuccess);
        console.log('- hasSuccessMessage:', hasSuccessMessage);

        // 修改条件：只有在URL中明确包含success参数时才发送刷新消息
        // 不依赖Django messages，因为那会导致每次进入页面都发送消息
        const shouldSendMessage = inIframe && (isSuccess === 'true' || isSuccess === '1' || urlContainsSuccess);

        if (shouldSendMessage) {
            console.log('准备发送刷新消息到父页面');
            // 延迟发送消息，确保页面完全加载
            setTimeout(function() {
                console.log('发送刷新消息到父页面');
                window.parent.postMessage({
                    action: 'refreshBomData',
                    success: true
                }, '*');
            }, 500);
        } else {
            console.log('不满足发送刷新消息的条件:', {
                inIframe: inIframe,
                isSuccess: isSuccess,
                urlContainsSuccess: urlContainsSuccess,
                hasSuccessMessage: hasSuccessMessage
            });
        }

        // 检查是否来自BOM树视图，如果是则禁用客户名称字段
        if (bomTreeMode === '1') {
            // 保存原始客户名称值
            const originalCustomerName = $('#customerName').val();
            
            // 对于Select2控件，使用disabled方法禁用
            $('#customerName').prop('disabled', true);
            
            // 添加隐藏字段保存客户名称值，确保表单提交时包含该值
            if (originalCustomerName) {
                $('#customerName').after(`<input type="hidden" name="customerName" value="${originalCustomerName}">`);
            }
            
            $('#customerName').closest('.form-group').append('<div class="form-text text-muted">在BOM树视图中客户名称不可修改</div>');
        }

        // 监听来自父窗口的消息（用于BOM树视图中的按钮操作）
        window.addEventListener('message', function(event) {
            // 验证消息来源（可选，增加安全性）
            if (event.data && event.data.action) {
                switch (event.data.action) {
                    case 'save':
                        // 更新临时状态参数
                        if (event.data.tempState !== undefined) {
                            const tempStateInput = document.getElementById('temp_state');
                            if (tempStateInput) {
                                tempStateInput.value = event.data.tempState;
                            }
                        }

                        if (event.data.bomState !== undefined) {
                            const bomStateInput = document.getElementById('bom_state');
                            if (bomStateInput) {
                                bomStateInput.value = event.data.bomState;
                            }
                        }

                        // 设置sourceType参数
                        if (event.data.sourceType) {
                            let sourceTypeInput = document.getElementById('sourceType');
                            if (!sourceTypeInput) {
                                sourceTypeInput = document.createElement('input');
                                sourceTypeInput.type = 'hidden';
                                sourceTypeInput.id = 'sourceType';
                                sourceTypeInput.name = 'sourceType';
                                document.getElementById('materialBomForm').appendChild(sourceTypeInput);
                            }
                            sourceTypeInput.value = event.data.sourceType;
                            console.log('设置sourceType:', event.data.sourceType);
                        }

                        // 设置BOM基本信息参数
                        const bomFields = ['bomNum', 'bomLossRate', 'bomPartCount', 'bomProduceCount'];
                        bomFields.forEach(field => {
                            if (event.data[field] !== undefined) {
                                let input = document.getElementById(field);
                                if (!input) {
                                    input = document.createElement('input');
                                    input.type = 'hidden';
                                    input.id = field;
                                    input.name = field;
                                    document.getElementById('materialBomForm').appendChild(input);
                                }
                                input.value = event.data[field];
                                console.log(`设置${field}:`, event.data[field]);
                            }
                        });

                        // 检查是否需要添加BOM更新参数
                        // 注意：添加子件时不应该更新父节点的物料信息，只有在编辑现有BOM节点时才需要更新
                        if (event.data.bomId && event.data.isAddMode && event.data.sourceType !== 'bom_tree') {
                            console.log('新增模式，添加BOM更新参数:', event.data.bomId);
                            addBomUpdateParameter(event.data.bomId);
                        } else if (event.data.sourceType === 'bom_tree') {
                            console.log('BOM树添加子件模式，不更新父节点物料信息');
                        } else {
                            console.log('编辑模式或无BOM ID，不添加更新参数');
                        }

                        // 延迟一点时间确保隐藏字段已添加，然后触发表单提交
                        setTimeout(function() {
                            const form = document.getElementById('materialBomForm');
                            if (form) {
                                console.log('提交表单');

                                // 在表单提交前设置一个监听器来检测提交完成
                                const originalAction = form.action;

                                // 创建一个隐藏的iframe来监听表单提交结果
                                const submitFrame = document.createElement('iframe');
                                submitFrame.style.display = 'none';
                                submitFrame.name = 'submitFrame_' + Date.now();
                                document.body.appendChild(submitFrame);

                                // 设置表单target为这个iframe
                                form.target = submitFrame.name;

                                // 监听iframe加载完成
                                submitFrame.onload = function() {
                                    console.log('表单提交完成，直接发送成功消息');

                                    // 直接发送成功消息，不依赖URL检测
                                    // 因为我们知道如果到达这里，表单提交就是成功的
                                    if (window.parent !== window) {
                                        window.parent.postMessage({
                                            action: 'refreshBomData',
                                            success: true
                                        }, '*');
                                        console.log('已发送刷新消息到父页面');
                                    }

                                    // 清理iframe
                                    setTimeout(function() {
                                        document.body.removeChild(submitFrame);
                                    }, 2000);
                                };

                                form.submit();
                            } else {
                                console.error('未找到表单');
                            }
                        }, 100);
                        break;
                    case 'newVersion':
                        // 触发新增版本操作
                        const newVersionBtn = document.querySelector('.new-version-btn');
                        if (newVersionBtn) {
                            newVersionBtn.click();
                        }
                        break;
                }
            }
        });

        // 添加BOM更新参数的函数
        function addBomUpdateParameter(bomId) {
            const form = document.getElementById('materialBomForm');
            console.log('addBomUpdateParameter 调用，bomId:', bomId, 'form:', form);

            if (form && bomId) {
                // 检查是否已经存在该字段
                let updateBomInput = form.querySelector('input[name="update_bom_material"]');
                if (!updateBomInput) {
                    updateBomInput = document.createElement('input');
                    updateBomInput.type = 'hidden';
                    updateBomInput.name = 'update_bom_material';
                    form.appendChild(updateBomInput);
                    console.log('创建新的隐藏字段');
                } else {
                    console.log('使用现有的隐藏字段');
                }
                updateBomInput.value = bomId;
                console.log('设置BOM更新参数值:', bomId);

                // 验证字段是否正确添加
                const verification = form.querySelector('input[name="update_bom_material"]');
                console.log('验证隐藏字段:', verification ? verification.value : '未找到');
            } else {
                console.error('无法添加BOM更新参数，form:', form, 'bomId:', bomId);
            }
        }
    });
</script>
{% endblock %}
