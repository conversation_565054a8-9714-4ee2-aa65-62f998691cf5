{% extends 'base.html' %}
{% load static %}

{% block title %}数据字典树形视图 - 物料清单管理系统{% endblock %}

{% block body_class %}{% if is_iframe %}{% else %}hold-transition sidebar-mini layout-fixed{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .tree-container {
        margin: 20px 0;
    }
    .tree-node {
        margin-bottom: 10px;
    }
    .tree-node-content {
        display: flex;
        align-items: center;
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        border-left: 3px solid #007bff;
    }
    .tree-node-content:hover {
        background-color: #e9ecef;
    }
    .tree-node-toggle {
        margin-right: 10px;
        cursor: pointer;
        width: 20px;
        text-align: center;
    }
    .tree-node-info {
        flex-grow: 1;
    }
    .tree-node-actions {
        margin-left: 10px;
    }
    .tree-node-children {
        margin-left: 30px;
        padding-left: 10px;
        border-left: 1px dashed #ccc;
    }
    .tree-node-leaf {
        margin-bottom: 5px;
    }
    .tree-node-leaf .tree-node-content {
        border-left-color: #28a745;
    }
    .tree-node-badge {
        margin-left: 10px;
        font-size: 0.8em;
    }
</style>
{% endblock %}

{% block content %}
{% if is_iframe %}
    <!-- 主体内容 -->
    <section class="content">
        <div class="container-fluid">
            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">数据字典树形结构</h3>
                    <div class="card-tools">
                        <a href="{% url 'data_dictionary_batch_edit' %}?parent_id=0&iframe=1" class="btn btn-success btn-sm mr-2">
                            <i class="fas fa-edit"></i> 批量编辑根级别
                        </a>
                        <a href="{% url 'data_dictionary_add' %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 添加数据字典
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tree-container">
                        {% for dict_item in root_dicts %}
                            {% include 'bom_sys/tree_node.html' with node=dict_item is_iframe=is_iframe %}
                        {% empty %}
                            <div class="alert alert-info">
                                暂无数据字典项，请先添加。
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </section>
{% else %}
    <div class="wrapper">
        <!-- 导航栏 -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- 左侧导航栏切换按钮 -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
            </ul>

            <!-- 右侧导航栏 -->
            <ul class="navbar-nav ml-auto">
                <!-- 用户信息下拉菜单 -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline">{{ user_nick|default:username }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> 个人资料
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{% url 'logout' %}" class="dropdown-item">
                            <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                        </a>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- 左侧边栏 -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- 品牌Logo -->
            <a href="#" class="brand-link">
                <span class="brand-text font-weight-light">物料清单管理系统</span>
            </a>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 用户面板 -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-light"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block">{{ user_nick|default:username }}</a>
                    </div>
                </div>

                <!-- 侧边栏菜单 -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'dashboard' %}" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>
                                    仪表盘
                                </p>
                            </a>
                        </li>
                        
                        <li class="nav-item menu-open">
                            <a href="#" class="nav-link active">
                                <i class="nav-icon fas fa-database"></i>
                                <p>
                                    基础数据管理
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{% url 'data_dictionary_tree' %}" class="nav-link active">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>数据字典</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'customer_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>客户信息</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 内容头部 -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0">数据字典树形视图</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item"><a href="#">首页</a></li>
                                <li class="breadcrumb-item active">数据字典树形视图</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主体内容 -->
            <section class="content">
                <div class="container-fluid">
                    <!-- 消息提示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">数据字典树形结构</h3>
                            <div class="card-tools">
                                <a href="{% url 'data_dictionary_batch_edit' %}?parent_id=0" class="btn btn-success btn-sm mr-2">
                                    <i class="fas fa-edit"></i> 批量编辑根级别
                                </a>
                                <a href="{% url 'data_dictionary_add' %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加数据字典
                                </a>
                                <a href="{% url 'data_dictionary_list' %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-list"></i> 列表视图
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="tree-container">
                                {% for dict_item in root_dicts %}
                                    {% include 'bom_sys/tree_node.html' with node=dict_item is_iframe=is_iframe %}
                                {% empty %}
                                    <div class="alert alert-info">
                                        暂无数据字典项，请先添加。
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 树节点展开/折叠功能
        $('.tree-node-toggle').on('click', function() {
            const $this = $(this);
            const $children = $this.closest('.tree-node').find('> .tree-node-children');
            
            if ($children.is(':visible')) {
                $children.slideUp(200);
                $this.html('<i class="fas fa-plus"></i>');
            } else {
                $children.slideDown(200);
                $this.html('<i class="fas fa-minus"></i>');
            }
        });
        
        // 默认折叠所有节点
        $('.tree-node-children').hide();
        $('.tree-node-toggle').html('<i class="fas fa-plus"></i>');
       
    });
</script>
{% endblock %} 