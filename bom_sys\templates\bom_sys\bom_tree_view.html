{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}BOM树形视图{% endblock %}

{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}


{% block content %}
<!-- 保存遮罩层 -->
<div id="savingOverlay" class="saving-overlay" style="display: none;">
    <div class="saving-content">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">保存中...</span>
        </div>
        <div class="mt-3">
            <h5>保存中，请稍候...</h5>
            <p class="text-muted">正在保存物料信息</p>
        </div>
    </div>
</div>

<div>
    {% csrf_token %}

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4>
                            <i class="fas fa-sitemap mr-2"></i>
                            BOM树形视图: {{ material.material_name }}
                            <small class="text-muted ml-2">{{ material.material_drawingno }} - {{ material.material_version }}</small>
                        </h4>
                        <div>
                            <a href="{% url 'bom_list_new' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left mr-1"></i>返回BOM清单
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row bom-container">
                <!-- 左侧面板：BOM列表 -->
                <div class="left-panel" id="leftPanel">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">BOM结构</h3>
                            <div class="card-tools d-flex align-items-center">
                                <button class="btn btn-secondary btn-sm mr-2" id="refreshBomData">
                                    <i class="fas fa-sync-alt mr-1"></i>刷新
                                </button>

                                <!-- 物料属性筛选复选框 -->
                                <div class="mr-3">
                                    <div class="d-flex flex-wrap align-items-center">
                                        <small class="text-muted mr-2">属性筛选：</small>
                                        <div class="form-check form-check-inline mr-2">
                                            <input class="form-check-input material-attr-filter" type="checkbox" id="filter_customer_supply" value="customer_supply">
                                            <label class="form-check-label" for="filter_customer_supply">客供</label>
                                        </div>
                                        <div class="form-check form-check-inline mr-2">
                                            <input class="form-check-input material-attr-filter" type="checkbox" id="filter_outsourcing" value="outsourcing">
                                            <label class="form-check-label" for="filter_outsourcing">外购</label>
                                        </div>
                                        <div class="form-check form-check-inline mr-2">
                                            <input class="form-check-input material-attr-filter" type="checkbox" id="filter_sales" value="sales">
                                            <label class="form-check-label" for="filter_sales">销售</label>
                                        </div>
                                        <div class="form-check form-check-inline mr-2">
                                            <input class="form-check-input material-attr-filter" type="checkbox" id="filter_self_made" value="self_made">
                                            <label class="form-check-label" for="filter_self_made">自制</label>
                                        </div>
                                        <div class="form-check form-check-inline mr-2">
                                            <input class="form-check-input material-attr-filter" type="checkbox" id="filter_subcontract" value="subcontract">
                                            <label class="form-check-label" for="filter_subcontract">委外</label>
                                        </div>
                                        <div class="form-check form-check-inline mr-2">
                                            <input class="form-check-input material-attr-filter" type="checkbox" id="filter_production_consumption" value="production_consumption">
                                            <label class="form-check-label" for="filter_production_consumption">生产耗用</label>
                                        </div>
                                        <div class="form-check form-check-inline mr-2">
                                            <input class="form-check-input material-attr-filter" type="checkbox" id="filter_batch_management" value="batch_management">
                                            <label class="form-check-label" for="filter_batch_management">批号管理</label>
                                        </div>
                                        <div class="form-check form-check-inline mr-2">
                                            <input class="form-check-input material-attr-filter" type="checkbox" id="filter_virtual_item" value="virtual_item">
                                            <label class="form-check-label" for="filter_virtual_item">虚拟件</label>
                                        </div>
                                        <button class="btn btn-outline-secondary btn-sm ml-2" id="clearFilters" title="清除所有筛选">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="input-group input-group-sm" style="width: 200px;">
                                    <input type="text" id="searchBomInput" class="form-control" placeholder="搜索...">
                                    <div class="input-group-append">
                                        <button type="button" id="searchBomBtn" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body table-responsive p-0" style="max-height: calc(100vh - 220px); overflow: auto;">
                            <table class="table table-hover table-head-fixed text-nowrap" id="bomTable">
                                <thead>
                                    <tr>
                                        <th style="width: 60px; min-width: 60px; max-width: 60px; text-align: left;">&nbsp;</th>
                                        <th style="width: 60px; min-width: 60px; max-width: 60px; text-align: left;">层级</th>
                                        <th style="width: 142px; min-width: 142px; max-width: 142px;">物料编码</th>
                                        <th style="width: 120px; min-width: 120px; max-width: 120px;">客户图号</th>
                                        <th style="width: 180px; min-width: 180px; max-width: 180px;">物料名称</th>
                                        <th style="width: 40px; min-width: 40px; max-width: 40px; text-align: center;">数量</th>
                                        <th style="width: 60px">材质</th>
                                        <th style="width: 80px">规格</th>
                                        <th style="width: 40px">版本</th>
                                        <th style="width: 60px">生产数量</th>
                                        <th style="width: 60px">零件用量</th>
                                        <th style="width: 60px">计量单位</th>
                                        <th style="width: 60px">损耗率</th>
                                        <th style="width: 40px">客供</th>
                                        <th style="width: 40px">外购</th>
                                        <th style="width: 40px">销售</th>
                                        <th style="width: 40px">自制</th>
                                        <th style="width: 40px">委外</th>
                                        <th style="width: 60px">生产耗用</th>
                                        <th style="width: 60px">批号管理</th>
                                        <th style="width: 60px">虚拟件</th>
                                        <th style="width: 80px">下料尺寸</th>
                                        <th style="width: 120px">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="bomTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                    <tr>
                                        <td colspan="22" class="text-center">
                                            <i class="fas fa-spinner fa-spin mr-2"></i>正在加载BOM数据...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧面板：物料详情 -->
                <div class="right-panel" id="rightPanel">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">物料详情</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" id="pinPanel" title="固定面板">
                                    <i class="fas fa-thumbtack"></i>
                                </button>
                                <button type="button" class="btn btn-tool" id="closePanel" title="关闭面板">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0" style="height: calc(100vh - 100px);">
                            <!-- iframe容器 -->
                            <div id="materialFrame" style="height: 100%; width: 100%;">
                                <!-- 操作按钮区 -->
                                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light">
                                    <h5 class="mb-0">物料详细信息</h5>
                                    <div>
                                        <button class="btn btn-primary btn-sm" id="saveMaterial">
                                            <i class="fas fa-save mr-1"></i>保存
                                        </button>
                                        <button class="btn btn-info btn-sm ml-2" id="newVersionMaterial">
                                            <i class="fas fa-code-branch mr-1"></i>新增版本
                                        </button>
                                        <button class="btn btn-warning btn-sm ml-2" id="bindMaterial">
                                            <i class="fas fa-link mr-1"></i>绑定物料
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- iframe 嵌入 -->
                                <iframe id="materialIframe" src="" style="width: 100%; height: calc(100% - 50px); border: none;" frameborder="0"></iframe>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- 绑定物料模态框 -->
<div class="modal fade" id="bindMaterialModal" tabindex="-1" role="dialog" aria-labelledby="bindMaterialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bindMaterialModalLabel">
                    <i class="fas fa-link mr-2"></i>绑定物料
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                {% csrf_token %}
                <!-- 搜索区域 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="materialSearchInput" placeholder="输入物料名称或编号搜索">
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="materialStateFilter">
                            <option value="1">在用</option>
                            <option value="1,2">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-primary" id="searchMaterialBtn">
                            <i class="fas fa-search mr-1"></i>搜索
                        </button>
                    </div>
                    <div class="col-md-5">
                        <small class="text-muted">当前客户：<span id="currentCustomerName"></span></small>
                    </div>
                </div>

                <!-- 物料列表表格 -->
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="materialBindTable">
                        <thead class="thead-light">
                            <tr>
                                <th width="15%">物料编号</th>
                                <th width="25%">物料名称</th>
                                <th width="15%">客户图号</th>
                                <th width="10%">版本</th>
                                <th width="15%">物料分类</th>
                                <th width="10%">状态</th>
                                <th width="10%">绑定</th>
                            </tr>
                        </thead>
                        <tbody id="materialBindTableBody">
                            <tr>
                                <td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div id="materialPaginationInfo" class="text-muted"></div>
                    </div>
                    <div class="col-md-6">
                        <nav aria-label="物料分页">
                            <ul class="pagination pagination-sm justify-content-end" id="materialPagination">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 包含公共BOM节点操作模态框 -->
{% include 'bom_sys/includes/bom_node_modal.html' %}

<!-- 移动节点模态框 -->
<div class="modal fade" id="moveNodeModal" tabindex="-1" role="dialog" aria-labelledby="moveNodeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="moveNodeModalLabel">
                    <i class="fas fa-arrows-alt mr-2"></i>移动节点
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="targetParentSelect">
                        <i class="fas fa-search mr-1"></i>选择目标父节点：
                    </label>
                    <small class="text-muted d-block mb-2">
                        支持按物料编码、物料名称、客户图号进行搜索
                    </small>
                    <select class="form-control" id="targetParentSelect" style="width: 100%;">
                        <option value="">-- 请选择目标父节点 --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="insertPosition">插入位置：</label>
                    <select class="form-control" id="insertPosition">
                        <option value="last">最后一个子节点</option>
                        <option value="first">第一个子节点</option>
                    </select>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i>
                    <strong>提示：</strong>选择目标父节点后，当前节点将移动到该节点下成为其子节点。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" id="confirmMoveNode">
                    <i class="fas fa-check mr-1"></i>确定移动
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 右键菜单 -->
<div id="contextMenu" class="context-menu" style="display: none;">
    <ul class="list-unstyled mb-0">
        <li><a href="#" class="context-menu-item" data-action="addChild">
            <i class="fas fa-plus mr-2"></i>添加子件
        </a></li>
        <li><a href="#" class="context-menu-item" data-action="bind">
            <i class="fas fa-link mr-2"></i>绑定物料
        </a></li>
        <li class="dropdown-divider"></li>
        <li><a href="#" class="context-menu-item" data-action="move">
            <i class="fas fa-arrows-alt mr-2"></i>移动节点
        </a></li>
        <li class="dropdown-divider"></li>
        <li><a href="#" class="context-menu-item text-danger" data-action="delete">
            <i class="fas fa-trash mr-2"></i>删除节点
        </a></li>
    </ul>
</div>

{% endblock %}

{% block extra_css %}
<style>
/* 布局样式 */
.bom-container {
    margin-bottom: 20px;
}

.left-panel {
    border-right: 1px solid #dee2e6;
}

/* 确保右侧面板默认隐藏，不受Bootstrap类影响 */
#rightPanel {
    display: none !important;
    width: 0;
    float: right;
    overflow: hidden;
    transition: width 0.3s ease;
}

/* 当面板需要显示时，使用这个类 */
#rightPanel.show-panel {
    display: block !important;
    width: 58.333333% !important; /* 相当于col-md-7 */
    overflow: visible !important;
}

/* 左侧面板宽度控制 */
#leftPanel {
    width: 100% !important;
    float: left;
    transition: width 0.3s ease;
}

#leftPanel.panel-narrow {
    width: 41.666667% !important; /* 相当于col-md-5 */
}

/* 确保容器能容纳浮动元素 */
.bom-container {
    overflow: hidden;
}

/* 表格样式 */
#bomTable {
    table-layout: auto; /* 改为自动布局 */
    width: 100%;
    min-width: 1400px;
}

/* 表格容器样式优化 */
.table-responsive {
    position: relative;
}

/* 表头固定样式 */
#bomTable thead th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 20;
    border-bottom: 2px solid #dee2e6;
}

#bomTable .level-indent {
    display: inline-block;
    width: 20px;
    height: 1px;
}

#bomTable .selected-row {
    background-color: #e3f2fd !important;
}

/* 确保选中行的所有单元格都有背景色 */
#bomTable .selected-row td {
    background-color: #e3f2fd !important;
}

#bomTable td {
    white-space: nowrap;
}

/* 固定左侧列样式 */
#bomTable th:nth-child(1), /* 序号 */
#bomTable th:nth-child(2), /* 层级 */
#bomTable th:nth-child(3), /* 物料编码 */
#bomTable th:nth-child(4), /* 客户图号 */
#bomTable th:nth-child(5), /* 物料名称 */
#bomTable th:nth-child(6) { /* 数量 - 表头 */
    position: sticky;
    background-color: #f8f9fa;
    z-index: 30;
    border-right: 1px solid #dee2e6;
}

#bomTable td:nth-child(1), /* 序号 */
#bomTable td:nth-child(2), /* 层级 */
#bomTable td:nth-child(3), /* 物料编码 */
#bomTable td:nth-child(4), /* 客户图号 */
#bomTable td:nth-child(5), /* 物料名称 */
#bomTable td:nth-child(6) { /* 数量 - 数据行 */
    position: sticky;
    background-color: #fff;
    z-index: 15;
    border-right: 1px solid #dee2e6;
}

/* 设置每列的left位置 */
#bomTable th:nth-child(1),
#bomTable td:nth-child(1) { /* 序号 */
    left: 0;
    width: 40px;
    min-width: 40px;
    max-width: 40px;
}

#bomTable th:nth-child(2),
#bomTable td:nth-child(2) { /* 层级 */
    left: 60px;
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    text-align: left; /* 层级列左对齐 */
}

#bomTable th:nth-child(3),
#bomTable td:nth-child(3) { /* 物料编码 */
    left: 120px;
    width: 142px;
    min-width: 142px;
    max-width: 142px;
}

#bomTable th:nth-child(4),
#bomTable td:nth-child(4) { /* 客户图号 */
    left: 262px;
    width: 120px;
    min-width: 120px;
    max-width: 120px;
}

#bomTable th:nth-child(5),
#bomTable td:nth-child(5) { /* 物料名称 */
    left: 382px;
    width: 180px;
    min-width: 180px;
    max-width: 180px;
}

#bomTable th:nth-child(6),
#bomTable td:nth-child(6) { /* 数量 */
    left: 562px;
    width: 50px;
    min-width: 50px;
    max-width: 50px;
}

/* 确保固定列的内容不会溢出 */
#bomTable th:nth-child(1),
#bomTable th:nth-child(2),
#bomTable th:nth-child(3),
#bomTable th:nth-child(4),
#bomTable th:nth-child(5),

#bomTable td:nth-child(1),
#bomTable td:nth-child(2),
#bomTable td:nth-child(3),
#bomTable td:nth-child(4),
#bomTable td:nth-child(5){
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
}

/* 选中行的所有固定列背景色调整 - 确保覆盖所有其他样式 */
#bomTable .selected-row td:nth-child(1),
#bomTable .selected-row td:nth-child(2),
#bomTable .selected-row td:nth-child(3),
#bomTable .selected-row td:nth-child(4),
#bomTable .selected-row td:nth-child(5),
#bomTable .selected-row td:nth-child(6) {
    background-color: #e3f2fd !important;
}

/* 特别确保所有固定列在选中时都有背景色 */
#bomTable tbody tr.selected-row td:nth-child(1),
#bomTable tbody tr.selected-row td:nth-child(2),
#bomTable tbody tr.selected-row td:nth-child(3),
#bomTable tbody tr.selected-row td:nth-child(4),
#bomTable tbody tr.selected-row td:nth-child(5),
#bomTable tbody tr.selected-row td:nth-child(6) {
    background-color: #e3f2fd !important;
}

/* 鼠标悬停时的固定列样式 */
#bomTable tbody tr:hover td:nth-child(1),
#bomTable tbody tr:hover td:nth-child(2),
#bomTable tbody tr:hover td:nth-child(3),
#bomTable tbody tr:hover td:nth-child(4),
#bomTable tbody tr:hover td:nth-child(5),
#bomTable tbody tr:hover td:nth-child(6) {
    background-color: #f5f5f5 !important;
}

/* 特定列的样式 */
#bomTable td:nth-child(2), /* 层级 */
#bomTable td:nth-child(3), /* 物料编码 */
#bomTable td:nth-child(4), /* 客户图号 */
#bomTable td:nth-child(5), /* 物料名称 */
#bomTable td:nth-child(6) { /* 数量 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 其他列保持原有的省略号样式 */
#bomTable td:not(:nth-child(1)):not(:nth-child(2)):not(:nth-child(3)):not(:nth-child(4)):not(:nth-child(5)):not(:nth-child(6)) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 右侧面板动画 */
.right-panel {
    transition: all 0.3s ease;
}

/* 固定按钮样式 */
#pinPanel .text-primary {
    color: #007bff !important;
}

/* 层级样式，通过文本样式区分层级 */
.level-0 {
    font-weight: bold;
    color: #000;
    font-size: 14px;
}

.level-1 {
    color: #007bff;
    font-weight: 600;
    font-size: 13px;
}

.level-2 {
    color: #28a745;
    font-weight: 500;
    font-size: 13px;
}

.level-3 {
    color: #fd7e14;
    font-weight: 500;
    font-size: 12px;
}

.level-4 {
    color: #dc3545;
    font-weight: 400;
    font-size: 12px;
}

.level-5 {
    color: #6f42c1;
    font-weight: 400;
    font-size: 11px;
}

.level-6 {
    color: #20c997;
    font-weight: 400;
    font-size: 11px;
}

/* 层级列表头强制sticky定位 */
#bomTable th:nth-child(2) {
    position: sticky !important;
    left: 60px !important;
    background-color: #f8f9fa !important;
    z-index: 30 !important;
}

/* 层级列可点击样式 - 确保sticky定位优先级 */
#bomTable td:nth-child(2) {
    cursor: pointer;
    user-select: none;
    position: sticky !important; /* 强制sticky定位 */
    left: 60px !important; /* 确保left位置 */
    background-color: #fff !important; /* 确保背景色 */
    z-index: 15 !important; /* 确保层级 */
}

/* 选中行的层级列背景色 - 优先级最高 */
#bomTable .selected-row td:nth-child(2) {
    background-color: #e3f2fd !important;
}

#bomTable td:nth-child(2):hover {
    background-color: #f5f5f5 !important;
}

/* 展开/收缩图标 */
.level-toggle {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 4px;
    text-align: center;
    line-height: 16px;
    font-size: 10px;
    border: 1px solid #ccc;
    border-radius: 2px;
    background-color: #fff;
    cursor: pointer;
}

.level-toggle:hover {
    background-color: #e9ecef;
}

.level-toggle.expanded::before {
    content: "−";
    font-weight: bold;
}

.level-toggle.collapsed::before {
    content: "+";
    font-weight: bold;
}

/* 隐藏的子行样式 */
.child-row.hidden {
    display: none;
}

/* 物料属性筛选复选框样式 */
.material-attr-filter {
    transform: scale(0.9);
}

.form-check-inline {
    margin-right: 0.5rem !important;
}

.form-check-label {
    font-size: 0.875rem;
    margin-bottom: 0;
    cursor: pointer;
}

/* 筛选后的行高亮样式 */
.filtered-row {
    background-color: #fff3cd !important;
}

.filtered-row td {
    background-color: #fff3cd !important;
}

/* 响应式样式 */
@media (max-width: 991.98px) {
    .left-panel,
    .right-panel {
        margin-bottom: 20px;
    }
}

/* ==================== BOM操作功能样式 ==================== */

/* 操作按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    margin: 0 1px;
}

/* 表格行选中状态 - 统一使用不透明背景色 */
.table tbody tr.selected-row {
    background-color: #e3f2fd !important;
}

.table tbody tr.selected-row td {
    background-color: #e3f2fd !important;

}

/* 右键菜单样式 */
.context-menu {
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 150px;
}

.context-menu ul {
    padding: 5px 0;
    margin: 0;
}

.context-menu li {
    list-style: none;
}

.context-menu li a {
    display: block;
    padding: 8px 15px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s;
}

.context-menu li a:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #333;
}

.context-menu .dropdown-divider {
    margin: 5px 0;
    border-top: 1px solid #e9ecef;
}

/* 移动节点Select2样式优化 */
#moveNodeModal .select2-container {
    width: 100% !important;
    z-index: 9999;
}

#moveNodeModal .select2-dropdown {
    z-index: 9999 !important;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#moveNodeModal .select2-container--default .select2-selection--single {
    height: 38px;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background-color: #fff;
}

#moveNodeModal .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
    padding-right: 20px;
    color: #495057;
}

#moveNodeModal .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
    right: 10px;
}

#moveNodeModal .select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #999 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0;
}

/* Select2结果选项样式 */
#moveNodeModal .select2-results__option {
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.4;
    border-bottom: 1px solid #f8f9fa;
}

#moveNodeModal .select2-results__option:last-child {
    border-bottom: none;
}

#moveNodeModal .select2-results__option--highlighted {
    background-color: #007bff !important;
    color: white !important;
}

#moveNodeModal .select2-results__option--selected {
    background-color: #e9ecef;
    color: #495057;
}

/* 层级缩进样式 */
.node-level-0 { padding-left: 12px; }
.node-level-1 { padding-left: 24px; }
.node-level-2 { padding-left: 36px; }
.node-level-3 { padding-left: 48px; }
.node-level-4 { padding-left: 60px; }
.node-level-5 { padding-left: 72px; }

/* 物料信息显示样式 */
.material-info-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 6px;
    padding: 2px 0;
    line-height: 1.4;
}

.material-code-badge {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
    display: inline-block;
    min-width: 0;
}

.material-name-text {
    font-weight: 500;
    color: #333;
    font-size: 13px;
    flex: 1;
    min-width: 0;
    word-break: break-word;
}

.customer-drawing-text {
    color: #6c757d;
    font-size: 11px;
    font-style: italic;
    white-space: nowrap;
}

/* 层级指示器样式 */
.level-indicator {
    color: #6c757d;
    font-size: 12px;
    font-family: monospace;
    white-space: pre;
    margin-right: 4px;
}

/* 高亮状态下的样式调整 */
#moveNodeModal .select2-results__option--highlighted .material-code-badge {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

#moveNodeModal .select2-results__option--highlighted .material-name-text {
    color: white;
}

#moveNodeModal .select2-results__option--highlighted .customer-drawing-text {
    color: rgba(255, 255, 255, 0.8);
}

/* 层级指示器 */
.level-indicator {
    display: inline-block;
    width: 12px;
    height: 1px;
    background-color: #dee2e6;
    margin-right: 4px;
    vertical-align: middle;
}

.level-indicator.level-1::before { content: "└─"; color: #6c757d; font-size: 12px; }
.level-indicator.level-2::before { content: "　└─"; color: #6c757d; font-size: 12px; }
.level-indicator.level-3::before { content: "　　└─"; color: #6c757d; font-size: 12px; }
.level-indicator.level-4::before { content: "　　　└─"; color: #6c757d; font-size: 12px; }
.level-indicator.level-5::before { content: "　　　　└─"; color: #6c757d; font-size: 12px; }

/* 模态框优化 */
.modal-lg {
    max-width: 800px;
}

/* 操作类型选择按钮组 */
.btn-group-toggle .btn {
    border-radius: 0.25rem;
}

.btn-group-toggle .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group-toggle .btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* 表格操作列固定宽度 */
#bomTable th:last-child,
#bomTable td:last-child {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
}

/* 操作按钮悬停效果 */
.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 搜索结果表格样式 */
#materialSearchResults tr:hover {
    background-color: #f8f9fa;
}

/* 选中的物料行 */
#materialSearchResults tr.selected {
    background-color: #e3f2fd;
}

/* 工具提示样式 */
[title] {
    cursor: help;
}

/* 保存遮罩层样式 */
.saving-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99999 !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.saving-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    min-width: 250px;
}

.saving-content .spinner-border {
    width: 3rem;
    height: 3rem;
}
</style>
<!-- Select2 CSS -->
<link href="/static/css/select2.min.css" rel="stylesheet" />
<link href="/static/css/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<link rel="stylesheet" href="/static/css/jquery-ui.css">
{% endblock %}

{% block extra_js %}
    <!-- Select2 JS -->
    <script src="/static/js/select2.min.js"></script>
    <script src="/static/js/form-logic.js"></script>
<script src="/static/js/jquery-ui.min.js"></script>

<script>
    $(document).ready(function() {
        // 确保右侧面板默认隐藏
        $('#rightPanel').removeClass('show-panel');
        $('#leftPanel').removeClass('panel-narrow');

        // 当前选中的BOM节点（默认不选择任何节点，让右侧面板保持隐藏）
        let selectedBomId = null; // 改为null，不自动选择
        let allBomNodes = [];

        // 当前客户信息
        let currentCustomer = {
            code: '{{ customer.customer_code|default:"" }}',
            name: '{{ customer.customer_name|default:"未知客户" }}'
        };

        // 根节点BOM ID（用于刷新整个树）
        let rootBomId = {{ bom_id }};
        
        // 初始化：加载BOM树形数据
        loadBomTreeData();

        // 调试：检查遮罩层是否存在
        let overlayCheck = document.getElementById('savingOverlay');
        console.log('页面加载时遮罩层检查:', overlayCheck ? '存在' : '不存在');

        // 如果遮罩层不存在，动态创建一个
        if (!overlayCheck) {
            console.log('遮罩层不存在，动态创建...');
            const overlay = document.createElement('div');
            overlay.id = 'savingOverlay';
            overlay.className = 'saving-overlay';
            overlay.style.display = 'none';
            overlay.innerHTML = `
                <div class="saving-content">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">保存中...</span>
                    </div>
                    <div class="mt-3">
                        <h5>保存中，请稍候...</h5>
                        <p class="text-muted">正在保存物料信息</p>
                    </div>
                </div>
            `;
            document.body.appendChild(overlay);
            console.log('遮罩层已动态创建');
            overlayCheck = overlay;
        }

        if (overlayCheck) {
            console.log('遮罩层元素:', overlayCheck);
            console.log('遮罩层样式:', window.getComputedStyle(overlayCheck));
        }
        
        // 加载BOM树形数据
        function loadBomTreeData() {

            $.ajax({
                url: '/get_bom_tree_data/',
                type: 'GET',
                data: {
                    bom_id: rootBomId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.bom_tree) {
                        // 存储完整的树形数据
                        allBomNodes = response.bom_tree;

                        // 存储客户信息到全局变量
                        if (response.customer) {
                            window.currentCustomerCode = response.customer.code;
                            window.currentCustomerName = response.customer.name;
                        }

                        // 调试：打印节点数据结构
                        debugNodeStructure();

                        // 渲染表格
                        renderBomTable(response.bom_tree);

                        // 如果有之前选中的节点，保持选中状态；否则不自动选择任何节点
                        if (selectedBomId && findNodeById(selectedBomId, response.bom_tree)) {
                            selectBomNode(selectedBomId);
                        }
                        // 注释掉自动选择第一个节点的逻辑，让右侧面板默认保持关闭状态
                        // else if (response.bom_tree.length > 0) {
                        //     selectBomNode(response.bom_tree[0].id);
                        // }

                    } else {
                        $('#bomTableBody').html('<tr><td colspan="5" class="text-center">没有找到BOM数据</td></tr>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载BOM数据失败:', error);
                    $('#bomTableBody').html('<tr><td colspan="5" class="text-center text-danger">加载BOM数据失败：' + error + '</td></tr>');
                }
            });
        }
        
        // 渲染BOM表格
        function renderBomTable(nodes) {
            let tableRows = [];
            let rowIndex = 1; // 序号计数器
            
            // 递归处理节点
            function processNodes(nodes, level) {
                nodes.forEach(node => {
                    // 创建表格行
                    const row = createTableRow(node, level, rowIndex);
                    tableRows.push(row);
                    rowIndex++; // 递增序号
                    
                    // 如果有子节点，递归处理
                    if (node.children && node.children.length > 0) {
                        processNodes(node.children, level + 1);
                    }
                });
            }
            
            // 处理所有节点
            processNodes(nodes, 0);
            
            // 更新表格内容
            $('#bomTableBody').html(tableRows.join(''));
            
            // 绑定行双击事件（统一交互模式）
            $('#bomTable tbody tr').dblclick(function(e) {
                console.log('双击事件触发');

                // 如果双击的是层级列的展开/收缩按钮，不触发行选择
                if ($(e.target).hasClass('level-toggle')) {
                    return;
                }

                // 如果双击的是操作按钮，不触发行选择
                if ($(e.target).closest('.btn, .dropdown-toggle, .dropdown-menu').length > 0) {
                    return;
                }

                const bomId = $(this).data('id');
                if (bomId) {
                    console.log('双击选中节点:', bomId);
                    selectBomNode(bomId);

                    // 双击时加载物料详情到右侧面板
                    const node = findNodeById(bomId, allBomNodes);
                    if (node) {
                        console.log('双击显示物料详情');
                        displayMaterialDetail(node);
                    }
                }
            });

            // 绑定行单击事件（仅用于选中，不打开面板）
            $('#bomTable tbody tr').click(function(e) {
                console.log('单击事件触发');

                // 如果点击的是层级列的展开/收缩按钮，不触发行选择
                if ($(e.target).hasClass('level-toggle')) {
                    return;
                }

                // 如果点击的是操作按钮，不触发行选择
                if ($(e.target).closest('.btn, .dropdown-toggle, .dropdown-menu').length > 0) {
                    return;
                }

                const bomId = $(this).data('id');
                console.log('单击选中节点:', bomId, '（不显示面板）');
                selectBomNode(bomId);
            });

            // 防止双击时选中文本
            $('#bomTable tbody tr').on('selectstart', function() {
                return false;
            });

            // 绑定展开/收缩事件
            bindToggleEvents();
        }
        
        // 创建表格行
        function createTableRow(node, level, rowIndex) {
            // 限制最大层级为6
            const safeLevel = Math.min(level, 6);

            // 判断是否有子节点
            const hasChildren = node.children && node.children.length > 0;

            // 创建层级显示内容
            let levelContent = '';
            if (hasChildren) {
                levelContent = `<span class="level-toggle expanded" data-node-id="${node.id}"></span>${level}`;
            } else {
                levelContent = `<span style="display: inline-block; width: 20px;"></span>${level}`;
            }

            return `
                <tr data-id="${node.id}" data-level="${level}" data-parent-id="${node.parent_id || ''}" class="bom-row ${level > 0 ? 'child-row' : ''}">
                    <td>${rowIndex}</td>
                    <td class="level-${safeLevel}" data-node-id="${node.id}">${levelContent}</td>
                    <td title="${node.material_no || ''}">${node.material_no || ''}</td>
                    <td title="${node.material_drawingno || ''}">${node.material_drawingno || ''}</td>
                    <td title="${node.material_name || ''}">${node.material_name || ''}</td>
                    <td class="text-center" style="width: 40px">${node.quantity || '1'}</td>
                    <td style="width: 60px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${node.material_quality || ''}">${node.material_quality || ''}</td>
                    <td style="width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${node.material_spec || ''}">${node.material_spec || ''}</td>
                    <td style="width: 40px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${node.material_version || ''}">${node.material_version || ''}</td>
                    <td class="text-center" style="width: 60px">${node.produce_count || ''}</td>
                    <td class="text-center" style="width: 60px">${node.part_count || ''}</td>
                    <td style="width: 60px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${node.material_unit || ''}">${node.material_unit || ''}</td>
                    <td class="text-center" style="width: 60px">${node.loss_rate || ''}</td>
                    <td class="text-center" style="width: 40px">${node.inventory_attrs && node.inventory_attrs.customer_supply === '是' ? '是' : '否'}</td>
                    <td class="text-center" style="width: 40px">${node.inventory_attrs && node.inventory_attrs.outsourcing === '是' ? '是' : '否'}</td>
                    <td class="text-center" style="width: 40px">${node.inventory_attrs && node.inventory_attrs.sales === '是' ? '是' : '否'}</td>
                    <td class="text-center" style="width: 40px">${node.inventory_attrs && node.inventory_attrs.self_made === '是' ? '是' : '否'}</td>
                    <td class="text-center" style="width: 40px">${node.inventory_attrs && node.inventory_attrs.subcontract === '是' ? '是' : '否'}</td>
                    <td class="text-center" style="width: 60px">${node.inventory_attrs && node.inventory_attrs.production_consumption === '是' ? '是' : '否'}</td>
                    <td class="text-center" style="width: 60px">${node.inventory_attrs && node.inventory_attrs.batch_management === '是' ? '是' : '否'}</td>
                    <td class="text-center" style="width: 60px">${node.inventory_attrs && node.inventory_attrs.virtual_item === '是' ? '是' : '否'}</td>
                    <td style="width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${node.inventory_attrs && node.inventory_attrs.cutting_size || ''}">${node.inventory_attrs && node.inventory_attrs.cutting_size || ''}</td>
                    <td style="width: 120px">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-primary add-child-btn" data-bom-id="${node.id}" title="添加子件">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="btn btn-info bind-material-btn" data-bom-id="${node.id}" title="绑定物料">
                                <i class="fas fa-link"></i>
                            </button>
                            <button class="btn btn-warning move-node-btn" data-bom-id="${node.id}" title="移动节点">
                                <i class="fas fa-arrows-alt"></i>
                            </button>
                            <button class="btn btn-danger delete-node-btn" data-bom-id="${node.id}" title="删除节点">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }

        // 绑定展开/收缩事件
        function bindToggleEvents() {
            // 绑定层级列点击事件
            $('#bomTable tbody td:nth-child(2)').off('click').on('click', function(e) {
                e.stopPropagation();
                const nodeId = $(this).data('node-id');
                const toggleBtn = $(this).find('.level-toggle');

                if (toggleBtn.length > 0) {
                    toggleNodeChildren(nodeId, toggleBtn);
                }
            });

            // 绑定展开/收缩按钮点击事件
            $('.level-toggle').off('click').on('click', function(e) {
                e.stopPropagation();
                const nodeId = $(this).data('node-id');
                toggleNodeChildren(nodeId, $(this));
            });
        }

        // 展开/收缩节点子项
        function toggleNodeChildren(nodeId, toggleBtn) {
            const isExpanded = toggleBtn.hasClass('expanded');
            const currentRow = $(`tr[data-id="${nodeId}"]`);
            const currentLevel = parseInt(currentRow.data('level'));

            if (isExpanded) {
                // 收缩：隐藏所有子节点
                hideChildrenRecursive(nodeId, currentLevel);
                toggleBtn.removeClass('expanded').addClass('collapsed');
            } else {
                // 展开：显示直接子节点
                showDirectChildren(nodeId, currentLevel);
                toggleBtn.removeClass('collapsed').addClass('expanded');
            }
        }

        // 递归隐藏所有子节点
        function hideChildrenRecursive(parentNodeId, parentLevel) {
            const parentRow = $(`tr[data-id="${parentNodeId}"]`);
            let nextRow = parentRow.next();

            while (nextRow.length > 0) {
                const nextLevel = parseInt(nextRow.data('level'));

                // 如果是同级或更高级别的节点，停止处理
                if (nextLevel <= parentLevel) {
                    break;
                }

                // 隐藏子节点
                nextRow.addClass('hidden');

                // 如果子节点有展开按钮，将其设置为收缩状态
                const childToggle = nextRow.find('.level-toggle');
                if (childToggle.length > 0) {
                    childToggle.removeClass('expanded').addClass('collapsed');
                }

                nextRow = nextRow.next();
            }
        }

        // 显示子节点树（递归显示所有应该可见的子节点）
        function showChildrenTree(parentNodeId, parentLevel) {
            const parentRow = $(`tr[data-id="${parentNodeId}"]`);
            let nextRow = parentRow.next();

            while (nextRow.length > 0) {
                const nextLevel = parseInt(nextRow.data('level'));
                const nextNodeId = nextRow.data('id');

                // 如果是同级或更高级别的节点，停止处理
                if (nextLevel <= parentLevel) {
                    break;
                }

                // 显示所有子节点（不管层级）
                if (nextLevel > parentLevel) {
                    // 检查这个节点是否应该显示
                    let shouldShow = true;

                    // 检查从父节点到当前节点的路径上是否有折叠的节点
                    let checkRow = parentRow.next();
                    while (checkRow.length > 0 && checkRow.data('id') !== nextNodeId) {
                        const checkLevel = parseInt(checkRow.data('level'));
                        const checkToggle = checkRow.find('.level-toggle');

                        // 如果在路径上发现折叠的节点，且它是当前节点的祖先
                        if (checkLevel < nextLevel && checkLevel > parentLevel &&
                            checkToggle.length > 0 && checkToggle.hasClass('collapsed')) {
                            shouldShow = false;
                            break;
                        }
                        checkRow = checkRow.next();
                    }

                    if (shouldShow) {
                        nextRow.removeClass('hidden');
                    }
                }

                nextRow = nextRow.next();
            }
        }

        // 显示直接子节点（简化版本）
        function showDirectChildren(parentNodeId, parentLevel) {
            showChildrenTree(parentNodeId, parentLevel);
        }

        // 选择BOM节点（只负责选中，不自动显示面板）
        function selectBomNode(bomId) {
            console.log('selectBomNode 被调用，bomId:', bomId);

            // 移除之前的选中状态
            $('#bomTable tbody tr').removeClass('selected-row');

            // 添加新的选中状态
            $(`#bomTable tbody tr[data-id="${bomId}"]`).addClass('selected-row');

            // 更新当前选中的BOM ID
            selectedBomId = bomId;

            console.log('节点选中完成，不显示面板');
        }
        
        // 根据ID查找节点
        function findNodeById(id, nodes) {
            let result = null;
            
            function traverse(nodes) {
                for (let i = 0; i < nodes.length; i++) {
                    if (nodes[i].id == id) {
                        result = nodes[i];
                        return true;
                    }
                    
                    if (nodes[i].children && nodes[i].children.length > 0) {
                        if (traverse(nodes[i].children)) {
                            return true;
                        }
                    }
                }
                
                return false;
            }
            
            traverse(nodes);
            return result;
        }
        
        // 显示物料详情
        function displayMaterialDetail(node) {
            console.log('displayMaterialDetail 被调用，准备显示面板');

            // 获取materialId，这个ID用于加载物料详情
            const materialId = node.material_id || null;

            if (!materialId) {
                console.error("无法获取物料ID:", node);
                return;
            }

            console.log('显示右侧面板，materialId:', materialId);
            // 显示右侧面板
            showRightPanel();
            
            // 构建iframe URL，直接进入编辑模式，添加bom_tree_mode参数
            let iframeUrl = `/materials/add/?edit_id=${materialId}&iframe=1&source_type=bom_tree&bom_tree_mode=1`;
            
            // 检查当前页面URL，判断是否为导入预览页面
            const isImportPreview = window.location.pathname.includes('bom_import_preview');
            
            // 如果是导入预览页面，添加临时状态参数
            if (isImportPreview) {
                const tempState = node.materialTempState || 1;
                const bomState = node.bomState || 1;
                iframeUrl += `&temp_state=${tempState}&bom_state=${bomState}`;
            }
            
            // 设置iframe的src
            $("#materialIframe").attr("src", iframeUrl);
            
            console.log("通过iframe加载物料详情，URL:", iframeUrl);
        }
        
        // 显示右侧面板
        function showRightPanel() {
            $('#rightPanel').addClass('show-panel');
            $('#leftPanel').addClass('panel-narrow');
        }

        // 隐藏右侧面板
        function hideRightPanel() {
            $('#rightPanel').removeClass('show-panel');
            $('#leftPanel').removeClass('panel-narrow');
        }
        
        // 面板固定状态
        let isPanelPinned = false;
        
        // 固定面板按钮事件
        $('#pinPanel').click(function() {
            isPanelPinned = !isPanelPinned;
            if (isPanelPinned) {
                $(this).find('i').removeClass('fas fa-thumbtack').addClass('fas fa-thumbtack text-primary');
                $(this).attr('title', '取消固定');
            } else {
                $(this).find('i').removeClass('fas fa-thumbtack text-primary').addClass('fas fa-thumbtack');
                $(this).attr('title', '固定面板');
            }
        });
        
        // 关闭面板按钮事件
        $('#closePanel').click(function() {
            hideRightPanel();
            // 移除选中状态
            $('#bomTable tbody tr').removeClass('selected-row');
        });
        
        // 点击其他地方时隐藏面板（如果未固定）
        $(document).click(function(e) {
            if (!isPanelPinned && !$(e.target).closest('#rightPanel, #bomTable').length) {
                if ($('#rightPanel').hasClass('show-panel')) {
                    hideRightPanel();
                    $('#bomTable tbody tr').removeClass('selected-row');
                }
            }
        });

        // 保存物料按钮事件
        $("#saveMaterial").on("click", function() {
            const iframe = document.getElementById('materialIframe');
            if (iframe && iframe.contentWindow) {
                try {
                    // 显示保存遮罩层
                    console.log("显示保存遮罩层");
                    const overlay = document.getElementById('savingOverlay');
                    if (overlay) {
                        overlay.style.display = 'flex';
                        console.log("遮罩层已显示，display:", overlay.style.display);
                    } else {
                        console.error("未找到遮罩层元素");
                    }

                    // 设置保存超时
                    if (window.saveTimeout) {
                        clearTimeout(window.saveTimeout);
                    }
                    window.saveTimeout = setTimeout(function() {
                        const overlay = document.getElementById('savingOverlay');
                        if (overlay) {
                            overlay.style.display = 'none';
                        }
                        showErrorMessage('保存超时，请检查网络连接后重试');
                    }, 30000);

                    // 检查是否为新增模式
                    let isAddMode = false;
                    let currentBomId = selectedBomId;

                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const formModeInput = iframeDoc.getElementById('formMode');
                        isAddMode = formModeInput && formModeInput.value === 'add';
                    } catch (e) {
                        // 如果无法访问iframe内容，从URL判断
                        const iframeSrc = iframe.src;
                        isAddMode = !iframeSrc.includes('edit_id=');
                    }

                    // 使用postMessage发送保存命令，包含必要参数
                    const messageData = {
                        action: 'save',
                        bomId: currentBomId,
                        isAddMode: isAddMode
                    };

                    iframe.contentWindow.postMessage(messageData, '*');
                    console.log("发送保存命令到iframe:", messageData);

                } catch (error) {
                    console.error("发送消息失败:", error);
                    const overlay = document.getElementById('savingOverlay');
                    if (overlay) {
                        overlay.style.display = 'none';
                    }
                    if (window.saveTimeout) {
                        clearTimeout(window.saveTimeout);
                    }
                    showErrorMessage("保存失败：无法与iframe通信");
                }
            } else {
                showErrorMessage("请先选择一个物料进行编辑");
            }
        });

        // 新增版本按钮事件
        $("#newVersionMaterial").on("click", function() {
            const iframe = document.getElementById('materialIframe');
            if (iframe && iframe.contentWindow) {
                try {
                    // 使用postMessage发送新增版本命令
                    iframe.contentWindow.postMessage({action: 'newVersion'}, '*');
                    console.log("发送新增版本命令到iframe");
                } catch (error) {
                    console.error("发送消息失败:", error);
                    // 降级处理：直接尝试访问iframe内容
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const newVersionBtn = iframeDoc.querySelector('.new-version-btn');
                        if (newVersionBtn) {
                            newVersionBtn.click();
                        } else {
                            alert("新增版本失败：未找到相应按钮");
                        }
                    } catch (e) {
                        alert("新增版本失败：无法访问iframe内容");
                    }
                }
            } else {
                alert("请先选择一个物料进行编辑");
            }
        });

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            console.log('收到iframe消息:', event.data);
            if (event.data && event.data.action === 'refreshBomData') {
                console.log('处理刷新消息');

                // 清除保存超时
                if (window.saveTimeout) {
                    clearTimeout(window.saveTimeout);
                    window.saveTimeout = null;
                }

                // 隐藏保存遮罩层
                const overlay = document.getElementById('savingOverlay');
                if (overlay) {
                    overlay.style.display = 'none';
                }

                if (event.data.success) {
                    // 重新加载BOM树形数据
                    loadBomTreeData();

                    // 静默保存，不显示提示
                    console.log('物料保存成功，BOM数据已更新');
                } else {
                    // 显示错误消息
                    showErrorMessage('保存失败：' + (event.data.error || event.data.message || '未知错误'));
                }
            }
        });

        // 显示成功消息的函数
        function showSuccessMessage(message) {
            // 创建成功提示
            const alertHtml = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle mr-2"></i>${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `;

            // 在页面顶部显示提示
            $('.content-wrapper').prepend(alertHtml);

            // 3秒后自动隐藏
            setTimeout(function() {
                $('.alert-success').alert('close');
            }, 3000);
        }

        // 显示警告消息的函数
        function showWarningMessage(message) {
            // 创建警告提示
            const alertHtml = `
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle mr-2"></i>${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `;

            // 在页面顶部显示提示
            $('.content-wrapper').prepend(alertHtml);

            // 3秒后自动隐藏
            setTimeout(function() {
                $('.alert-warning').alert('close');
            }, 3000);
        }

        // 手动刷新BOM数据按钮事件
        $("#refreshBomData").on("click", function() {
            loadBomTreeData();
            showSuccessMessage('BOM数据已刷新');
        });


        // 绑定物料按钮事件
        $("#bindMaterial").on("click", function() {
            if (!selectedBomId) {
                alert("请先选择一个BOM节点");
                return;
            }

            // 检查客户信息
            if (!currentCustomer.code) {
                alert("无法获取客户信息，请重新进入页面");
                return;
            }

            // 设置当前客户名称
            $('#currentCustomerName').text(currentCustomer.name);

            // 重置搜索条件
            $('#materialSearchInput').val('');
            $('#materialStateFilter').val('1');

            // 显示模态框
            $('#bindMaterialModal').modal('show');

            // 默认加载数据
            currentMaterialPage = 1;
            searchMaterials();
        });

        // 物料搜索相关变量
        let currentMaterialPage = 1;
        let materialPageSize = 10;
        let selectedMaterialId = null;

        // 搜索物料按钮事件
        $("#searchMaterialBtn").on("click", function() {
            currentMaterialPage = 1;
            searchMaterials();
        });

        // 搜索框回车事件
        $("#materialSearchInput").on("keypress", function(e) {
            if (e.which === 13) {
                currentMaterialPage = 1;
                searchMaterials();
            }
        });

        // 状态筛选变化事件
        $("#materialStateFilter").on("change", function() {
            currentMaterialPage = 1;
            searchMaterials();
        });

        // 搜索物料函数
        function searchMaterials() {
            if (!currentCustomer.code) {
                alert("无法获取客户信息，请重新进入页面");
                return;
            }

            const searchData = {
                customer: currentCustomer.code,
                keyword: $('#materialSearchInput').val().trim(),
                state: $('#materialStateFilter').val(),
                page: currentMaterialPage,
                page_size: materialPageSize
            };

            console.log('搜索物料参数:', searchData);

            $.ajax({
                url: '/search_materials_for_bind/',
                type: 'GET',
                data: searchData,
                dataType: 'json',
                beforeSend: function() {
                    $('#materialBindTableBody').html('<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin"></i> 搜索中...</td></tr>');
                },
                success: function(response) {
                    console.log('物料搜索结果:', response);
                    if (response.success) {
                        renderMaterialTable(response.materials);
                        renderMaterialPagination(response.pagination);
                    } else {
                        $('#materialBindTableBody').html('<tr><td colspan="8" class="text-center text-danger">搜索失败：' + (response.error || '未知错误') + '</td></tr>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('搜索物料失败:', error);
                    $('#materialBindTableBody').html('<tr><td colspan="8" class="text-center text-danger">搜索失败：' + error + '</td></tr>');
                }
            });
        }

        // 渲染物料表格
        function renderMaterialTable(materials) {
            let tableRows = [];

            if (materials.length === 0) {
                tableRows.push('<tr><td colspan="7" class="text-center">没有找到符合条件的物料</td></tr>');
            } else {
                materials.forEach(material => {
                    const statusText = material.state == 1 ? '<span class="badge badge-success">在用</span>' : '<span class="badge badge-secondary">停用</span>';

                    tableRows.push(`
                        <tr data-material-id="${material.id}">
                            <td>${material.no || ''}</td>
                            <td>${material.name || ''}</td>
                            <td>${material.drawing_no || ''}</td>
                            <td>${material.version || ''}</td>
                            <td>${material.attr_name || ''}</td>
                            <td>${statusText}</td>
                            <td class="text-center">
                                <button type="button" class="btn btn-primary btn-sm bind-material-btn" data-material-id="${material.id}" data-material-name="${material.name}">
                                    <i class="fas fa-link mr-1"></i>绑定
                                </button>
                            </td>
                        </tr>
                    `);
                });
            }

            $('#materialBindTableBody').html(tableRows.join(''));

            // 绑定每行的绑定按钮事件
            $('.bind-material-btn').on('click', function() {
                const materialId = $(this).data('material-id');
                const materialName = $(this).data('material-name');

                if (!confirm(`确定要绑定物料"${materialName}"吗？`)) {
                    return;
                }

                bindMaterialToBom(materialId, materialName, $(this));
            });
        }

        // 渲染分页
        function renderMaterialPagination(pagination) {
            // 更新分页信息
            $('#materialPaginationInfo').text(`显示第 ${pagination.start} - ${pagination.end} 条，共 ${pagination.total} 条记录`);

            // 生成分页按钮
            let paginationHtml = [];

            // 上一页
            if (pagination.has_previous) {
                paginationHtml.push(`<li class="page-item"><a class="page-link" href="#" data-page="${pagination.current_page - 1}">上一页</a></li>`);
            } else {
                paginationHtml.push(`<li class="page-item disabled"><span class="page-link">上一页</span></li>`);
            }

            // 页码
            for (let i = 1; i <= pagination.total_pages; i++) {
                if (i === pagination.current_page) {
                    paginationHtml.push(`<li class="page-item active"><span class="page-link">${i}</span></li>`);
                } else {
                    paginationHtml.push(`<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`);
                }
            }

            // 下一页
            if (pagination.has_next) {
                paginationHtml.push(`<li class="page-item"><a class="page-link" href="#" data-page="${pagination.current_page + 1}">下一页</a></li>`);
            } else {
                paginationHtml.push(`<li class="page-item disabled"><span class="page-link">下一页</span></li>`);
            }

            $('#materialPagination').html(paginationHtml.join(''));

            // 绑定分页点击事件
            $('#materialPagination a').on('click', function(e) {
                e.preventDefault();
                const page = $(this).data('page');
                if (page) {
                    currentMaterialPage = page;
                    searchMaterials();
                }
            });
        }

        // 绑定物料到BOM的函数
        function bindMaterialToBom(materialId, materialName, buttonElement) {
            if (!selectedBomId) {
                alert("请先选择一个BOM节点");
                return;
            }

            // 执行绑定
            $.ajax({
                url: '/bind_material_to_bom/',
                type: 'POST',
                data: {
                    bom_id: selectedBomId,
                    material_id: materialId,
                    csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
                },
                dataType: 'json',
                beforeSend: function() {
                    buttonElement.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>绑定中...');
                },
                success: function(response) {
                    console.log('绑定结果:', response);
                    if (response.success) {
                        // 关闭模态框
                        $('#bindMaterialModal').modal('hide');

                        // 刷新BOM数据
                        loadBomTreeData();

                        // 显示成功消息
                        showSuccessMessage(`物料"${materialName}"绑定成功`);
                    } else {
                        alert('绑定失败：' + (response.error || '未知错误'));
                        buttonElement.prop('disabled', false).html('<i class="fas fa-link mr-1"></i>绑定');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('绑定失败:', error);
                    alert('绑定失败：' + error);
                    buttonElement.prop('disabled', false).html('<i class="fas fa-link mr-1"></i>绑定');
                }
            });
        }

        // 绑定搜索BOM按钮点击事件
        $('#searchBomBtn').click(function() {
            const keyword = $('#searchBomInput').val().toLowerCase();
            
            if (!keyword || keyword.trim() === '') {
                // 如果搜索词为空，显示所有行
                $('#bomTable tbody tr').show();
                return;
            }
            
            // 隐藏所有行
            $('#bomTable tbody tr').hide();
            
            // 显示匹配的行
            $('#bomTable tbody tr').each(function() {
                const text = $(this).text().toLowerCase();
                if (text.indexOf(keyword) > -1) {
                    $(this).show();
                }
            });
        });
        
        // 绑定搜索输入框回车事件
        $('#searchBomInput').keypress(function(e) {
            if (e.which === 13) {
                $('#searchBomBtn').click();
                e.preventDefault();
            }
        });

        // 绑定物料属性筛选功能
        $('.material-attr-filter').change(function() {
            applyMaterialAttributeFilter();
        });

        // 绑定清除筛选按钮
        $('#clearFilters').click(function() {
            $('.material-attr-filter').prop('checked', false);
            applyMaterialAttributeFilter();
        });

        // ==================== 物料属性筛选功能 ====================

        // 应用物料属性筛选
        function applyMaterialAttributeFilter() {
            const checkedFilters = [];
            $('.material-attr-filter:checked').each(function() {
                checkedFilters.push($(this).val());
            });

            // 移除之前的筛选高亮
            $('#bomTable tbody tr').removeClass('filtered-row');

            if (checkedFilters.length === 0) {
                // 如果没有选中任何筛选条件，显示所有行
                $('#bomTable tbody tr').show();
                return;
            }

            // 遍历所有表格行进行筛选
            $('#bomTable tbody tr').each(function() {
                const row = $(this);
                const rowData = getRowData(row);
                let shouldShow = false;

                // 检查是否满足任一筛选条件
                for (let filter of checkedFilters) {
                    if (checkMaterialAttribute(rowData, filter)) {
                        shouldShow = true;
                        break;
                    }
                }

                if (shouldShow) {
                    row.show().addClass('filtered-row');
                } else {
                    row.hide();
                }
            });

            // 更新筛选状态显示
            updateFilterStatus(checkedFilters);
        }

        // 从表格行中提取数据
        function getRowData(row) {
            const cells = row.find('td');
            return {
                customer_supply: cells.eq(13).text().trim() === '是',
                outsourcing: cells.eq(14).text().trim() === '是',
                sales: cells.eq(15).text().trim() === '是',
                self_made: cells.eq(16).text().trim() === '是',
                subcontract: cells.eq(17).text().trim() === '是',
                production_consumption: cells.eq(18).text().trim() === '是',
                batch_management: cells.eq(19).text().trim() === '是',
                virtual_item: cells.eq(20).text().trim() === '是'
            };
        }

        // 检查物料属性是否匹配筛选条件
        function checkMaterialAttribute(rowData, filter) {
            return rowData[filter] === true;
        }

        // 更新筛选状态显示
        function updateFilterStatus(checkedFilters) {
            const totalRows = $('#bomTable tbody tr').length;
            const visibleRows = $('#bomTable tbody tr:visible').length;

            if (checkedFilters.length > 0) {
                console.log(`筛选结果: ${visibleRows}/${totalRows} 条记录匹配筛选条件`);

                // 可以在这里添加状态提示
                if (visibleRows === 0) {
                    showWarningMessage('没有找到匹配的记录');
                }
            }
        }

        // ==================== BOM结构操作功能 ====================

        // 全局变量
        let contextMenuTarget = null;

        // 绑定操作按钮事件
        function bindBomOperationEvents() {
            // 添加子件
            $(document).on('click', '.add-child-btn', function(e) {
                e.stopPropagation();
                const bomId = $(this).data('bom-id');
                showBomNodeModal('addChild', bomId);
            });

            // 绑定物料（使用现有功能）
            $(document).on('click', '.bind-material-btn', function(e) {
                e.stopPropagation();
                const bomId = $(this).data('bom-id');
                // 先选中该行
                selectBomNode(bomId);
                // 触发现有的绑定物料功能
                $('#bindMaterial').click();
            });

            // 移动节点
            $(document).on('click', '.move-node-btn', function(e) {
                e.stopPropagation();
                const bomId = $(this).data('bom-id');
                showMoveNodeModal(bomId);
            });



            // 删除节点
            $(document).on('click', '.delete-node-btn', function(e) {
                e.stopPropagation();
                const bomId = $(this).data('bom-id');
                confirmDeleteNode(bomId);
            });

            // 右键菜单
            $(document).on('contextmenu', '#bomTable tbody tr', function(e) {
                e.preventDefault();
                const bomId = $(this).data('id');
                contextMenuTarget = bomId;
                showContextMenu(e.pageX, e.pageY);
            });

            // 点击其他地方隐藏右键菜单
            $(document).click(function() {
                $('#contextMenu').hide();
            });

            // 右键菜单项点击
            $(document).on('click', '.context-menu-item', function(e) {
                e.preventDefault();
                const action = $(this).data('action');
                const bomId = contextMenuTarget;

                switch(action) {
                    case 'addChild':
                        showBomNodeModal('addChild', bomId);
                        break;
                    case 'bind':
                        selectBomNode(bomId);
                        $('#bindMaterial').click();
                        break;
                    case 'move':
                        showMoveNodeModal(bomId);
                        break;
                    case 'delete':
                        confirmDeleteNode(bomId);
                        break;
                }

                $('#contextMenu').hide();
            });
        }

        // 显示BOM节点操作模态框
        function showBomNodeModal(action, parentBomId) {
            $('#bomNodeModal').data('action', action);
            $('#bomNodeModal').data('parent-bom-id', parentBomId);

            // 重置表单
            resetBomNodeModal();

            // 根据操作类型设置不同的界面
            if (action === 'addChild') {
                // 添加子件模式
                const parentNode = findNodeById(parentBomId, allBomNodes);
                const parentName = parentNode ? parentNode.material_name || '未命名' : '未知';
                $('#bomNodeModal .modal-title').html('<i class="fas fa-plus mr-2"></i>为 "' + parentName + '" 添加子件');

            } else if (action === 'addParent') {
                // 添加父件模式
                $('#bomNodeModal .modal-title').html('<i class="fas fa-plus mr-2"></i>添加父件');


                // 加载客户列表（如果有的话）
                if (typeof loadCustomerOptions === 'function') {
                    loadCustomerOptions();
                }
            }

            // 设置iframe URL（创建新物料时）
            if (action === 'addChild') {
                // 获取当前BOM的客户信息
                const parentNode = findNodeById(parentBomId, allBomNodes);
                let customerCode = '';
                let customerName = '';

                // 首先尝试从父节点获取客户信息
                if (parentNode) {
                    customerCode = parentNode.material_customer || parentNode.customer_code || '';
                    customerName = parentNode.material_customername || parentNode.customer_name || '';
                    console.log('从父节点获取客户信息:', { customerCode, customerName });
                }

                // 如果父节点没有客户信息，从全局变量获取
                if (!customerCode && window.currentCustomerCode) {
                    customerCode = window.currentCustomerCode;
                    customerName = window.currentCustomerName || '';
                    console.log('从全局变量获取客户信息:', { customerCode, customerName });
                }

                // 如果还是没有，尝试从页面元素获取
                if (!customerCode) {
                    const customerCodeElement = document.getElementById('currentCustomerCode');
                    const customerNameElement = document.getElementById('currentCustomerName');

                    if (customerCodeElement) {
                        customerCode = customerCodeElement.textContent || customerCodeElement.innerText || '';
                    }
                    if (customerNameElement) {
                        customerName = customerNameElement.textContent || customerNameElement.innerText || '';
                    }
                    console.log('从页面元素获取客户信息:', { customerCode, customerName });
                }

                console.log('最终设置iframe客户信息:', { customerCode, customerName });

                // 构建URL参数，确保客户名称被正确传递
                const params = new URLSearchParams({
                    iframe: '1',
                    source_type: 'bom_tree',
                    bom_pid: parentBomId,
                    customer_code: customerCode,
                    bom_tree_mode: '1'
                });

                // 如果有客户名称，添加到参数中
                if (customerName) {
                    params.append('customerName', customerName);
                }

                const iframeUrl = `/materials/add/?${params.toString()}`;
                console.log('iframe URL:', iframeUrl);
                $('#materialAddIframe').attr('src', iframeUrl);

                // 启动iframe监听
                setTimeout(function() {
                    monitorIframeForSuccess();
                }, 1000);
            }

            $('#bomNodeModal').modal('show');
        }

        // 重置BOM节点模态框
        function resetBomNodeModal() {
            // 重置单选按钮到默认状态（创建新物料）
            $('input[name="nodeAction"][value="create"]').prop('checked', true);

            // 重置按钮组的视觉状态
            $('.btn-group-toggle label').removeClass('active');
            $('.btn-group-toggle label:first').addClass('active');

            // 清空搜索表单
            $('#addChildMaterialSearchInput').val('');
            $('#materialSearchResults').html('<tr><td colspan="5" class="text-center text-muted">请输入搜索条件</td></tr>');

            // 重置BOM基本信息字段（统一字段）
            $('#bomNum').val(1);
            $('#bomLossRate').val(0);
            $('#bomPartCount').val(1);
            $('#bomProduceCount').val(1);

            // 显示创建物料区域，隐藏绑定区域
            $('#createMaterialSection').show();
            $('#bindMaterialSection').hide();
            $('#saveMaterialBtn').show();
            $('#confirmNodeAction').hide();

            // 重置客户选择区域
            if ($('#parentCustomerSelect').length > 0) {
                $('#parentCustomerSelect').val('').trigger('change');
            }

        }

        // 显示移动节点模态框
        function showMoveNodeModal(bomId) {
            const node = findNodeById(bomId, allBomNodes);
            if (!node) {
                console.error(`未找到ID为 ${bomId} 的节点`);
                return;
            }

            console.log(`[DEBUG] 显示移动节点模态框，节点数据:`, node);

            $('#moveNodeModal').data('bom-id', bomId);

            // 构建节点显示信息，使用正确的字段映射
            const materialNo = node.materialNo || node.material_no || '';
            const materialName = node.materialName || node.material_name || node.name || '未命名';
            const customerDrawing = node.drawingNumber || node.customer_drawing || node.material_drawingno || '';

            console.log(`[DEBUG] 节点信息 - materialNo: ${materialNo}, materialName: ${materialName}, customerDrawing: ${customerDrawing}`);

            let nodeDisplayText = '';
            if (materialNo) {
                nodeDisplayText += `[${materialNo}] `;
            }
            nodeDisplayText += materialName;
            if (customerDrawing) {
                nodeDisplayText += ` (${customerDrawing})`;
            }

            $('#moveNodeModal .modal-title').html(`
                <i class="fas fa-arrows-alt mr-2"></i>移动节点:
                <span class="text-primary">${nodeDisplayText}</span>
            `);

            // 填充目标父节点选项
            populateParentNodeOptions(bomId);

            $('#moveNodeModal').modal('show');
        }

        // 填充父节点选项（优化版本，支持完整信息显示和搜索）
        function populateParentNodeOptions(excludeBomId) {
            const select = $('#targetParentSelect');

            // 销毁现有的Select2实例（如果存在）
            if (typeof $.fn.select2 !== 'undefined' && select.hasClass('select2-hidden-accessible')) {
                try {
                    select.select2('destroy');
                } catch (error) {
                    console.warn('销毁Select2实例失败:', error);
                }
            }

            // 清空并添加默认选项
            select.empty();
            select.append('<option value="">-- 请选择目标父节点 --</option>');

            // 收集所有可选节点的数据
            const nodeOptions = [];

            function collectNodeOptions(nodes, level = 0) {
                nodes.forEach(node => {
                    if (node.id != excludeBomId) {
                        // 正确映射字段名称
                        const materialNo = node.materialNo || node.material_no || '';
                        const materialName = node.materialName || node.material_name || node.name || '未命名';
                        const customerDrawing = node.drawingNumber || node.customer_drawing || node.material_drawingno || '';

                        console.log(`[DEBUG] 节点数据: ID=${node.id}, materialNo=${materialNo}, materialName=${materialName}, customerDrawing=${customerDrawing}`);

                        // 构建层级指示器
                        let levelIndicator = '';
                        if (level > 0) {
                            levelIndicator = '　'.repeat(level - 1) + '└─ ';
                        }

                        // 构建显示文本：层级指示器 + [物料编码] 物料名称 (客户图号)
                        let displayText = levelIndicator;
                        if (materialNo) {
                            displayText += `[${materialNo}] `;
                        }
                        displayText += materialName;
                        if (customerDrawing) {
                            displayText += ` (${customerDrawing})`;
                        }

                        // 构建搜索关键词（用于Select2搜索）
                        const searchKeywords = [
                            materialNo,
                            materialName,
                            customerDrawing
                        ].filter(keyword => keyword).join(' ');

                        nodeOptions.push({
                            id: node.id,
                            text: displayText,
                            materialNo: materialNo,
                            materialName: materialName,
                            customerDrawing: customerDrawing,
                            searchKeywords: searchKeywords,
                            level: level,
                            levelIndicator: levelIndicator
                        });

                        if (node.children && node.children.length > 0) {
                            collectNodeOptions(node.children, level + 1);
                        }
                    }
                });
            }

            // 收集所有节点选项
            collectNodeOptions(allBomNodes);

            // 添加选项到select
            nodeOptions.forEach(option => {
                select.append(`<option value="${option.id}"
                    data-material-no="${option.materialNo}"
                    data-material-name="${option.materialName}"
                    data-customer-drawing="${option.customerDrawing}"
                    data-search-keywords="${option.searchKeywords}"
                    data-level="${option.level}"
                    data-level-indicator="${option.levelIndicator}"
                    class="node-level-${option.level}">
                    ${option.text}
                </option>`);
            });

            // 尝试初始化Select2，如果失败则使用原生select
            initTargetParentSelect2();
        }

        // 调试函数：打印节点数据结构
        function debugNodeStructure() {
            console.log('[DEBUG] 所有BOM节点数据结构:');
            if (allBomNodes && allBomNodes.length > 0) {
                console.log('第一个节点的完整数据:', allBomNodes[0]);
                console.log('节点字段列表:', Object.keys(allBomNodes[0]));

                // 递归打印所有节点的关键字段
                function printNodeFields(nodes, level = 0) {
                    nodes.forEach((node, index) => {
                        const indent = '  '.repeat(level);
                        console.log(`${indent}节点${index}: {`);
                        console.log(`${indent}  id: ${node.id}`);
                        console.log(`${indent}  name: ${node.name}`);
                        console.log(`${indent}  materialName: ${node.materialName}`);
                        console.log(`${indent}  material_name: ${node.material_name}`);
                        console.log(`${indent}  materialNo: ${node.materialNo}`);
                        console.log(`${indent}  material_no: ${node.material_no}`);
                        console.log(`${indent}  drawingNumber: ${node.drawingNumber}`);
                        console.log(`${indent}  customer_drawing: ${node.customer_drawing}`);
                        console.log(`${indent}  material_drawingno: ${node.material_drawingno}`);
                        console.log(`${indent}}`);

                        if (node.children && node.children.length > 0 && level < 2) {
                            printNodeFields(node.children, level + 1);
                        }
                    });
                }

                printNodeFields(allBomNodes.slice(0, 2)); // 只打印前2个节点避免输出过多
            } else {
                console.log('allBomNodes 为空或未定义');
            }
        }

        // 简化版本的填充父节点选项（不使用Select2）
        function populateParentNodeOptionsSimple(excludeBomId) {
            const select = $('#targetParentSelect');
            select.empty();
            select.append('<option value="">-- 请选择目标父节点 --</option>');

            function addNodeOptions(nodes, level = 0) {
                nodes.forEach(node => {
                    if (node.id != excludeBomId) {
                        // 正确映射字段名称
                        const materialNo = node.materialNo || node.material_no || '';
                        const materialName = node.materialName || node.material_name || node.name || '未命名';
                        const customerDrawing = node.drawingNumber || node.customer_drawing || node.material_drawingno || '';

                        // 构建层级指示器
                        let levelIndicator = '';
                        if (level > 0) {
                            levelIndicator = '　'.repeat(level - 1) + '└─ ';
                        }

                        // 构建显示文本：层级指示器 + [物料编码] 物料名称 (客户图号)
                        let displayText = levelIndicator;
                        if (materialNo) {
                            displayText += `[${materialNo}] `;
                        }
                        displayText += materialName;
                        if (customerDrawing) {
                            displayText += ` (${customerDrawing})`;
                        }

                        // 构建搜索关键词
                        const searchKeywords = [materialNo, materialName, customerDrawing]
                            .filter(keyword => keyword).join(' ');

                        select.append(`<option value="${node.id}"
                            data-material-no="${materialNo}"
                            data-material-name="${materialName}"
                            data-customer-drawing="${customerDrawing}"
                            data-search-keywords="${searchKeywords}"
                            data-level="${level}"
                            class="node-level-${level}">
                            ${displayText}
                        </option>`);

                        if (node.children && node.children.length > 0) {
                            addNodeOptions(node.children, level + 1);
                        }
                    }
                });
            }

            addNodeOptions(allBomNodes);
        }

        // 初始化目标父节点Select2
        function initTargetParentSelect2() {
            // 检查Select2是否可用
            if (typeof $.fn.select2 === 'undefined') {
                console.warn('Select2库未加载，使用原生select');
                addSearchFunctionality(); // 为原生select添加搜索功能
                return;
            }

            try {
                $('#targetParentSelect').select2({
                placeholder: '请输入物料编码、物料名称或客户图号进行搜索',
                allowClear: true,
                width: '100%',
                dropdownParent: $('#moveNodeModal'),
                language: {
                    noResults: function() {
                        return '未找到匹配的节点';
                    },
                    searching: function() {
                        return '搜索中...';
                    },
                    inputTooShort: function() {
                        return '请输入至少1个字符进行搜索';
                    }
                },
                matcher: function(params, data) {
                    // 如果没有搜索词，显示所有选项
                    if ($.trim(params.term) === '') {
                        return data;
                    }

                    // 如果是默认选项，不进行匹配
                    if (!data.element || !data.element.dataset) {
                        return null;
                    }

                    const searchTerm = params.term.toLowerCase();
                    const searchKeywords = data.element.dataset.searchKeywords || '';
                    const materialNo = data.element.dataset.materialNo || '';
                    const materialName = data.element.dataset.materialName || '';
                    const customerDrawing = data.element.dataset.customerDrawing || '';

                    // 检查是否匹配搜索词
                    if (searchKeywords.toLowerCase().indexOf(searchTerm) > -1 ||
                        materialNo.toLowerCase().indexOf(searchTerm) > -1 ||
                        materialName.toLowerCase().indexOf(searchTerm) > -1 ||
                        customerDrawing.toLowerCase().indexOf(searchTerm) > -1) {
                        return data;
                    }

                    return null;
                },
                templateResult: function(data) {
                    if (data.loading) {
                        return data.text;
                    }

                    // 如果是默认选项，直接返回
                    if (!data.element || !data.element.dataset) {
                        return $('<div>').text(data.text);
                    }

                    const materialNo = data.element.dataset.materialNo || '';
                    const materialName = data.element.dataset.materialName || '';
                    const customerDrawing = data.element.dataset.customerDrawing || '';
                    const level = parseInt(data.element.dataset.level) || 0;

                    console.log(`[DEBUG] templateResult - materialNo: ${materialNo}, materialName: ${materialName}, customerDrawing: ${customerDrawing}, level: ${level}`);

                    // 创建格式化的显示内容
                    const $result = $('<div>').addClass(`node-level-${level}`);

                    // 总是创建容器，即使某些信息为空
                    const $container = $('<div>').addClass('material-info-container');

                    // 添加层级指示器
                    if (level > 0) {
                        const levelSymbol = '　'.repeat(level - 1) + '└─ ';
                        const $levelIndicator = $('<span>')
                            .addClass('level-indicator')
                            .text(levelSymbol);
                        $container.append($levelIndicator);
                    }

                    // 添加物料编码徽章
                    if (materialNo && materialNo.trim() !== '') {
                        const $badge = $('<span>')
                            .addClass('material-code-badge')
                            .text(materialNo);
                        $container.append($badge);
                    }

                    // 添加物料名称（总是显示，即使为空也显示"未命名"）
                    const displayName = materialName && materialName.trim() !== '' ? materialName : '未命名';
                    const $name = $('<span>')
                        .addClass('material-name-text')
                        .text(displayName);
                    $container.append($name);

                    // 添加客户图号
                    if (customerDrawing && customerDrawing.trim() !== '') {
                        const $drawing = $('<span>')
                            .addClass('customer-drawing-text')
                            .text(`(${customerDrawing})`);
                        $container.append($drawing);
                    }

                    $result.append($container);
                    return $result;
                },
                templateSelection: function(data) {
                    if (!data.element || !data.element.dataset) {
                        return data.text;
                    }

                    const materialNo = data.element.dataset.materialNo || '';
                    const materialName = data.element.dataset.materialName || '';
                    const customerDrawing = data.element.dataset.customerDrawing || '';
                    const level = parseInt(data.element.dataset.level) || 0;

                    console.log(`[DEBUG] templateSelection - materialNo: ${materialNo}, materialName: ${materialName}, customerDrawing: ${customerDrawing}`);

                    let text = '';

                    // 添加层级指示（简化版本）
                    if (level > 0) {
                        text += '　'.repeat(level - 1) + '└─ ';
                    }

                    // 添加物料信息
                    if (materialNo && materialNo.trim() !== '') {
                        text += `[${materialNo}] `;
                    }

                    // 添加物料名称
                    const displayName = materialName && materialName.trim() !== '' ? materialName : '未命名';
                    text += displayName;

                    // 添加客户图号
                    if (customerDrawing && customerDrawing.trim() !== '') {
                        text += ` (${customerDrawing})`;
                    }

                    return text || data.text;
                }
            });
            } catch (error) {
                console.error('初始化Select2失败:', error);
                console.warn('降级使用原生select');
                addSearchFunctionality(); // 为原生select添加搜索功能
            }
        }

        // 为原生select添加搜索功能
        function addSearchFunctionality() {
            // 如果已经添加了搜索框，则不重复添加
            if ($('#targetParentSearch').length > 0) {
                return;
            }

            // 在select前面添加搜索框
            const searchInput = $(`
                <div class="form-group mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                        </div>
                        <input type="text" class="form-control" id="targetParentSearch"
                               placeholder="输入物料编码、物料名称或客户图号进行搜索">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <small class="text-muted">支持模糊搜索，输入任意关键词进行过滤</small>
                </div>
            `);

            $('#targetParentSelect').before(searchInput);

            // 绑定搜索事件
            $('#targetParentSearch').on('input', function() {
                const searchTerm = $(this).val().toLowerCase().trim();
                const select = $('#targetParentSelect');
                let visibleCount = 0;

                select.find('option').each(function() {
                    const option = $(this);

                    // 跳过默认选项
                    if (option.val() === '') {
                        option.show();
                        return;
                    }

                    const text = option.text().toLowerCase();
                    const materialNo = (option.data('material-no') || '').toLowerCase();
                    const materialName = (option.data('material-name') || '').toLowerCase();
                    const customerDrawing = (option.data('customer-drawing') || '').toLowerCase();

                    // 多字段匹配
                    const isMatch = searchTerm === '' ||
                        text.indexOf(searchTerm) > -1 ||
                        materialNo.indexOf(searchTerm) > -1 ||
                        materialName.indexOf(searchTerm) > -1 ||
                        customerDrawing.indexOf(searchTerm) > -1;

                    if (isMatch) {
                        option.show();
                        visibleCount++;
                    } else {
                        option.hide();
                    }
                });

                // 更新搜索结果提示
                updateSearchResultHint(visibleCount, searchTerm);
            });

            // 清除搜索按钮事件
            $('#clearSearch').on('click', function() {
                $('#targetParentSearch').val('').trigger('input');
            });

            // 回车键搜索
            $('#targetParentSearch').on('keypress', function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    // 如果只有一个可见选项，自动选中
                    const visibleOptions = $('#targetParentSelect option:visible').not('[value=""]');
                    if (visibleOptions.length === 1) {
                        $('#targetParentSelect').val(visibleOptions.val());
                    }
                }
            });
        }

        // 更新搜索结果提示
        function updateSearchResultHint(count, searchTerm) {
            let hintElement = $('#searchResultHint');
            if (hintElement.length === 0) {
                hintElement = $('<small id="searchResultHint" class="text-info d-block mt-1"></small>');
                $('#targetParentSearch').parent().parent().append(hintElement);
            }

            if (searchTerm) {
                if (count === 0) {
                    hintElement.text('未找到匹配的节点').removeClass('text-info').addClass('text-warning');
                } else {
                    hintElement.text(`找到 ${count} 个匹配的节点`).removeClass('text-warning').addClass('text-info');
                }
            } else {
                hintElement.text('');
            }
        }



        // 确认删除节点
        function confirmDeleteNode(bomId) {
            const node = findNodeById(bomId, allBomNodes);
            if (!node) return;

            const childCount = getChildNodeCount(bomId);
            let message = `确定要删除节点"${node.material_name || '未命名'}"吗？`;
            if (childCount > 0) {
                message += `\n\n注意：该节点包含 ${childCount} 个子节点，删除后子节点也将被删除。`;
            }

            if (confirm(message)) {
                deleteBomNode(bomId);
            }
        }

        // 获取子节点数量
        function getChildNodeCount(bomId) {
            const node = findNodeById(bomId, allBomNodes);
            if (!node || !node.children) return 0;

            let count = 0;
            function countChildren(children) {
                count += children.length;
                children.forEach(child => {
                    if (child.children && child.children.length > 0) {
                        countChildren(child.children);
                    }
                });
            }

            countChildren(node.children);
            return count;
        }

        // 显示右键菜单
        function showContextMenu(x, y) {
            const menu = $('#contextMenu');
            menu.css({
                left: x + 'px',
                top: y + 'px'
            }).show();

            // 确保菜单不超出屏幕
            const menuWidth = menu.outerWidth();
            const menuHeight = menu.outerHeight();
            const windowWidth = $(window).width();
            const windowHeight = $(window).height();

            if (x + menuWidth > windowWidth) {
                menu.css('left', (x - menuWidth) + 'px');
            }
            if (y + menuHeight > windowHeight) {
                menu.css('top', (y - menuHeight) + 'px');
            }
        }

        // 模态框事件处理
        function bindModalEvents() {
            // 操作类型切换
            $('input[name="nodeAction"]').change(function() {
                const action = $(this).val();
                if (action === 'create') {
                    $('#createMaterialSection').show();
                    $('#bindMaterialSection').hide();
                    $('#saveMaterialBtn').show();
                    $('#confirmNodeAction').hide();
                } else {
                    $('#createMaterialSection').hide();
                    $('#bindMaterialSection').show();
                    $('#saveMaterialBtn').hide();
                    $('#confirmNodeAction').show();

                    // 切换到绑定现有物料时，自动搜索一次
                    setTimeout(function() {
                        searchMaterialsForBind();
                    }, 100);
                }
            });

            // 确认节点操作（仅用于绑定现有物料）
            $('#confirmNodeAction').click(function() {
                const action = $('input[name="nodeAction"]:checked').val();
                const parentBomId = $('#bomNodeModal').data('parent-bom-id');

                if (action === 'bind') {
                    bindExistingMaterial(parentBomId);
                }
                // 创建新物料通过iframe处理，不需要在这里处理
            });

            // 确认移动节点
            $('#confirmMoveNode').click(function() {
                const bomId = $('#moveNodeModal').data('bom-id');
                const targetParentId = $('#targetParentSelect').val();
                const position = $('#insertPosition').val();

                if (!targetParentId) {
                    alert('请选择目标父节点');
                    return;
                }

                moveBomNode(bomId, targetParentId, position);
            });

            // 移动节点模态框关闭时清理Select2
            $('#moveNodeModal').on('hidden.bs.modal', function() {
                const select = $('#targetParentSelect');
                if (typeof $.fn.select2 !== 'undefined' && select.hasClass('select2-hidden-accessible')) {
                    try {
                        select.select2('destroy');
                    } catch (error) {
                        console.warn('清理Select2实例失败:', error);
                    }
                }
            });

            // 保存物料按钮事件（用于添加子件模态框）
            $('#saveMaterialBtn').click(function() {
                const iframe = document.getElementById('materialAddIframe');
                if (iframe && iframe.contentWindow) {
                    try {
                        // 显示保存遮罩层
                        showSavingOverlay();

                        // 设置保存超时
                        setSaveTimeout();

                        // 获取父BOM ID
                        const parentBomId = $('#bomNodeModal').data('parent-bom-id');

                        // 获取BOM基本信息（使用统一的字段）
                        const bomNum = $('#bomNum').val() || 1;
                        const bomLossRate = $('#bomLossRate').val() || 0;
                        const bomPartCount = $('#bomPartCount').val() || 1;
                        const bomProduceCount = $('#bomProduceCount').val() || 1;

                        // 使用postMessage发送保存命令，包含BOM子件创建所需的参数
                        const messageData = {
                            action: 'save',
                            bomId: parentBomId,
                            isAddMode: true,
                            sourceType: 'bom_tree',  // 确保source_type正确
                            bomPid: parentBomId,     // 确保bom_pid参数传递
                            // 添加BOM基本信息
                            bomNum: bomNum,
                            bomLossRate: bomLossRate,
                            bomPartCount: bomPartCount,
                            bomProduceCount: bomProduceCount
                        };

                        console.log('发送保存命令到iframe:', messageData);
                        iframe.contentWindow.postMessage(messageData, '*');

                    } catch (error) {
                        console.error("发送消息失败:", error);
                        hideSavingOverlay();
                        alert("保存失败：无法与iframe通信");
                    }
                } else {
                    alert("请等待页面加载完成");
                }
            });

            // 搜索物料
            $('#addChildSearchMaterialBtn').click(function() {
                searchMaterialsForBind();
            });

            $('#addChildMaterialSearchInput').keypress(function(e) {
                if (e.which === 13) {
                    searchMaterialsForBind();
                }
            });

            // 选择搜索结果中的物料
            $(document).on('click', '#materialSearchResults tr', function() {
                $('#materialSearchResults tr').removeClass('selected');
                $(this).addClass('selected');
            });
        }

        // 创建新BOM节点现在通过iframe中的material_add.html处理
        // 不再需要单独的创建函数

        // 绑定现有物料（添加为子件）
        function bindExistingMaterial(parentBomId) {
            const selectedRow = $('#materialSearchResults tr.selected');
            if (selectedRow.length === 0) {
                alert('请选择要绑定的物料');
                return;
            }

            const materialId = selectedRow.data('material-id');
            const materialName = selectedRow.find('td:nth-child(3)').text();

            // 获取BOM基本信息
            const bomNum = $('#bomNum').val() || 1;
            const bomLossRate = $('#bomLossRate').val() || 0;
            const bomPartCount = $('#bomPartCount').val() || 1;
            const bomProduceCount = $('#bomProduceCount').val() || 1;

            console.log('添加子件 - 绑定现有物料:', {
                parentBomId, materialId, materialName,
                bomNum, bomLossRate, bomPartCount, bomProduceCount
            });

            $.ajax({
                url: '{% url "add_bom_child_node" %}',
                type: 'POST',
                data: {
                    'parent_bom_id': parentBomId,
                    'material_id': materialId,
                    'bom_num': bomNum,
                    'bom_lossrate': bomLossRate,
                    'bom_partcount': bomPartCount,
                    'bom_producecount': bomProduceCount,
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                },
                beforeSend: function() {
                    $('#confirmNodeAction').prop('disabled', true).text('添加中...');
                },
                success: function(response) {
                    if (response.success) {
                        $('#bomNodeModal').modal('hide');
                        loadBomTreeData(); // 刷新BOM数据
                        alert('子件添加成功');
                    } else {
                        alert('添加失败：' + (response.error || response.message));
                    }
                },
                error: function(xhr, status, error) {
                    alert('添加失败：' + error);
                },
                complete: function() {
                    $('#confirmNodeAction').prop('disabled', false).text('绑定物料');
                }
            });
        }

        // 搜索物料用于绑定
        function searchMaterialsForBind() {
            // 获取客户信息
            let customerCode = '';
            if (window.currentCustomerCode) {
                customerCode = window.currentCustomerCode;
            } else if (currentCustomer && currentCustomer.code) {
                customerCode = currentCustomer.code;
            }

            if (!customerCode) {
                alert("无法获取客户信息，请重新进入页面");
                return;
            }

            const keyword = $('#addChildMaterialSearchInput').val().trim();
            const searchData = {
                customer: customerCode,
                keyword: keyword,
                state: '1',  // 默认只搜索在用状态的物料
                page: 1,
                page_size: 20
            };

            console.log('搜索物料参数:', searchData);

            $.ajax({
                url: '/search_materials_for_bind/',
                type: 'GET',
                data: searchData,
                dataType: 'json',
                beforeSend: function() {
                    $('#addChildSearchMaterialBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
                },
                success: function(response) {
                    console.log('搜索物料响应:', response);
                    if (response.success) {
                        console.log('搜索到的物料数量:', response.materials ? response.materials.length : 0);
                        renderMaterialSearchResults(response.materials);
                    } else {
                        alert('搜索失败：' + (response.error || response.message));
                    }
                },
                error: function(xhr, status, error) {
                    alert('搜索失败：' + error);
                },
                complete: function() {
                    $('#addChildSearchMaterialBtn').prop('disabled', false).html('<i class="fas fa-search"></i>');
                }
            });
        }

        // 渲染物料搜索结果
        function renderMaterialSearchResults(materials) {
            console.log('开始渲染物料搜索结果:', materials);
            const tbody = $('#materialSearchResults');
            tbody.empty();

            if (!materials || materials.length === 0) {
                console.log('没有找到物料数据');
                tbody.html('<tr><td colspan="5" class="text-center text-muted">未找到匹配的物料</td></tr>');
                return;
            }

            console.log('渲染', materials.length, '条物料记录');
            materials.forEach((material, index) => {
                console.log(`物料 ${index + 1}:`, material);
                const row = `
                    <tr data-material-id="${material.id}" style="cursor: pointer;">
                        <td><input type="radio" name="selectedMaterial" value="${material.id}"></td>
                        <td>${material.no || ''}</td>
                        <td>${material.name || ''}</td>
                        <td>${material.drawing_no || ''}</td>
                        <td>${material.version || ''}</td>
                    </tr>
                `;
                tbody.append(row);
            });

            // 绑定行点击事件
            $('#materialSearchResults tr[data-material-id]').click(function() {
                // 清除其他选中状态
                $('#materialSearchResults tr').removeClass('selected');
                $(this).addClass('selected');

                // 选中对应的单选按钮
                $(this).find('input[type="radio"]').prop('checked', true);
            });

            console.log('物料搜索结果渲染完成');
        }

        // 移动BOM节点
        function moveBomNode(bomId, targetParentId, position) {
            // 获取节点信息用于提示
            const sourceNode = findNodeById(bomId, allBomNodes);
            const targetNode = findNodeById(targetParentId, allBomNodes);

            $.ajax({
                url: '{% url "move_bom_node" %}',
                type: 'POST',
                data: {
                    'bom_id': bomId,
                    'target_parent_id': targetParentId,
                    'position': position,
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                },
                beforeSend: function() {
                    $('#confirmMoveNode').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>移动中...');
                },
                success: function(response) {
                    if (response.success) {
                        $('#moveNodeModal').modal('hide');

                        // 构建成功提示信息
                        const sourceName = sourceNode ? (sourceNode.material_name || '未命名') : '节点';
                        const targetName = targetNode ? (targetNode.material_name || '未命名') : '目标节点';
                        const positionText = position === 'first' ? '第一个子节点' : '最后一个子节点';

                        // 显示成功提示
                        showSuccessMessage(`节点"${sourceName}"已成功移动到"${targetName}"下，位置：${positionText}`);

                        // 刷新BOM数据
                        loadBomTreeData();
                    } else {
                        showErrorMessage('移动失败：' + (response.error || response.message));
                    }
                },
                error: function(xhr, status, error) {
                    showErrorMessage('移动失败：' + error);
                },
                complete: function() {
                    $('#confirmMoveNode').prop('disabled', false).html('<i class="fas fa-check mr-1"></i>确定移动');
                }
            });
        }

        // 显示成功消息（使用友好的Bootstrap Alert，不使用alert弹窗）
        // 注意：此函数已在上方定义，这里删除重复定义

        // 显示错误消息
        function showErrorMessage(message) {
            if (typeof toastr !== 'undefined') {
                toastr.error(message);
            } else {
                alert(message);
            }
        }



        // 删除BOM节点
        function deleteBomNode(bomId) {
            $.ajax({
                url: '{% url "delete_bom_node_new" %}',
                type: 'POST',
                data: {
                    'bom_id': bomId,
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                },
                success: function(response) {
                    if (response.success) {
                        loadBomTreeData(); // 刷新BOM数据
                        alert(response.message || '节点删除成功');
                    } else {
                        alert('删除失败：' + (response.error || response.message));
                    }
                },
                error: function(xhr, status, error) {
                    alert('删除失败：' + error);
                }
            });
        }

        // 显示保存遮罩层
        function showSavingOverlay() {
            $('#savingOverlay').fadeIn(300);
        }

        // 隐藏保存遮罩层
        function hideSavingOverlay() {
            $('#savingOverlay').fadeOut(300);
        }

        // 刷新BOM表格数据
        function refreshBomTable() {
            loadBomTreeData();
        }

        // iframe消息监听
        function bindIframeMessageListener() {
            window.addEventListener('message', function(event) {
                console.log('收到iframe消息:', event.data);

                // 检查消息来源和内容
                if (event.data && event.data.action === 'refreshBomData') {
                    console.log('收到iframe刷新消息:', event.data);

                    // 清除保存超时
                    clearSaveTimeout();

                    // 隐藏保存遮罩层
                    hideSavingOverlay();

                    if (event.data.success) {
                        // 关闭模态框
                        $('#bomNodeModal').modal('hide');

                        // 刷新BOM表格数据
                        refreshBomTable();

                    } else {
                        // 显示错误消息
                        alert('保存失败：' + (event.data.error || event.data.message || '未知错误'));
                    }
                }
            });
        }

        // 添加iframe加载完成监听，用于检测保存成功
        function monitorIframeForSuccess() {
            const iframe = document.getElementById('materialAddIframe');
            if (iframe) {
                iframe.onload = function() {
                    try {
                        // 检查iframe的URL是否包含success=true
                        const iframeUrl = iframe.contentWindow.location.href;
                        console.log('iframe加载完成，URL:', iframeUrl);

                        if (iframeUrl.includes('success=true')) {
                            console.log('检测到保存成功，手动触发刷新');

                            // 清除保存超时
                            clearSaveTimeout();

                            // 隐藏保存遮罩层
                            hideSavingOverlay();

                            // 关闭模态框
                            $('#bomNodeModal').modal('hide');

                            // 刷新BOM表格数据
                            refreshBomTable();

                        }
                    } catch (e) {
                        // 跨域访问限制，忽略错误
                        console.log('无法访问iframe URL（跨域限制）');
                    }
                };
            }
        }

        // 保存超时处理
        let saveTimeout;
        function setSaveTimeout() {
            // 清除之前的超时
            if (saveTimeout) {
                clearTimeout(saveTimeout);
            }

            // 设置30秒超时
            saveTimeout = setTimeout(function() {
                hideSavingOverlay();
                alert('保存超时，请检查网络连接后重试');
            }, 30000);
        }

        function clearSaveTimeout() {
            if (saveTimeout) {
                clearTimeout(saveTimeout);
                saveTimeout = null;
            }
        }

        // 初始化时绑定事件
        $(document).ready(function() {
            bindBomOperationEvents();
            bindModalEvents();
            bindIframeMessageListener();
        });
    });
</script>

{% endblock %}
