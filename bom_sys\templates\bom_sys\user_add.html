{% extends 'base.html' %}

{% block title %}添加用户 - 物料清单管理系统{% endblock %}

{% block body_class %}{% if is_iframe %}{% else %}hold-transition sidebar-mini layout-fixed{% endif %}{% endblock %}

{% block content %}
{% if is_iframe %}
    <!-- 主体内容 -->
    <section class="content">
        <div class="container-fluid">
            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">添加新用户</h3>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'user_add' %}?iframe=1">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="username">用户名</label>
                            <input type="text" class="form-control" id="username" name="username" value="{{ form_data.username|default:'' }}" required>
                        </div>
                        <div class="form-group">
                            <label for="password">密码</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label for="confirm_password">确认密码</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <div class="form-group">
                            <label for="nickname">昵称</label>
                            <input type="text" class="form-control" id="nickname" name="nickname" value="{{ form_data.nickname|default:'' }}">
                        </div>
                        <div class="form-group">
                            <label for="user_role">用户角色</label>
                            <select class="form-control" id="user_role" name="user_role">
                                <option value="1" {% if form_data.user_role == '1' %}selected{% endif %}>管理员</option>
                                <option value="2" {% if form_data.user_role == '2' %}selected{% endif %}>工程用户</option>
                                <option value="3" {% if form_data.user_role == '3' %}selected{% endif %}>阅读用户</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">提交</button>
                        <a href="{% url 'user_list' %}?iframe=1" class="btn btn-default">取消</a>
                    </form>
                </div>
            </div>
        </div>
    </section>
{% else %}
    <div class="wrapper">
        <!-- 导航栏 -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- 左侧导航栏切换按钮 -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
            </ul>

            <!-- 右侧导航栏 -->
            <ul class="navbar-nav ml-auto">
                <!-- 用户信息下拉菜单 -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline">{{ user_nick|default:username }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> 个人资料
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{% url 'logout' %}" class="dropdown-item">
                            <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                        </a>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- 左侧边栏 -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- 品牌Logo -->
            <a href="#" class="brand-link">
                <span class="brand-text font-weight-light">物料清单管理系统</span>
            </a>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 用户面板 -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-light"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block">{{ user_nick|default:username }}</a>
                    </div>
                </div>

                <!-- 侧边栏菜单 -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'dashboard' %}" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>
                                    仪表盘
                                </p>
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-boxes"></i>
                                <p>
                                    物料管理
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>物料清单</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>添加物料</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>物料分类</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-file-invoice"></i>
                                <p>
                                    BOM管理
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>BOM清单</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>导入BOM</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-cogs"></i>
                                <p>
                                    系统设置
                                </p>
                            </a>
                        </li>
                        
                        <li class="nav-item menu-open">
                            <a href="#" class="nav-link active">
                                <i class="nav-icon fas fa-users"></i>
                                <p>
                                    用户管理
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{% url 'user_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>用户列表</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'user_add' %}" class="nav-link active">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>添加用户</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 主体内容 -->
            <section class="content">
                <div class="container-fluid">
                    <!-- 消息提示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">添加新用户</h3>
                        </div>
                        <div class="card-body">
                            <form method="post" action="{% url 'user_add' %}">
                                {% csrf_token %}
                                <div class="form-group">
                                    <label for="username">用户名</label>
                                    <input type="text" class="form-control" id="username" name="username" value="{{ form_data.username|default:'' }}" required>
                                </div>
                                <div class="form-group">
                                    <label for="password">密码</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                <div class="form-group">
                                    <label for="confirm_password">确认密码</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                                <div class="form-group">
                                    <label for="nickname">昵称</label>
                                    <input type="text" class="form-control" id="nickname" name="nickname" value="{{ form_data.nickname|default:'' }}">
                                </div>
                                <div class="form-group">
                                    <label for="user_role">用户角色</label>
                                    <select class="form-control" id="user_role" name="user_role">
                                        <option value="1" {% if form_data.user_role == '1' %}selected{% endif %}>管理员</option>
                                        <option value="2" {% if form_data.user_role == '2' %}selected{% endif %}>工程用户</option>
                                        <option value="3" {% if form_data.user_role == '3' %}selected{% endif %}>阅读用户</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">提交</button>
                                <a href="{% url 'user_list' %}" class="btn btn-default">取消</a>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </div>

    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 自动隐藏提示信息
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
        
        // 表单验证
        $('form').submit(function(e) {
            const password = $('#password').val();
            const confirmPassword = $('#confirm_password').val();
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('两次输入的密码不一致');
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %} 