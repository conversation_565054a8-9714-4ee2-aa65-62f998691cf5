{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}
{% load none_filter %}

{% block title %}添加工艺路线 - 物料清单管理系统{% endblock %}

{% block body_class %}{% if is_iframe %}{% else %}hold-transition sidebar-mini layout-fixed{% endif %}{% endblock %}

{% block content %}
{% if is_iframe %}
    <!-- 主体内容 -->
    <section class="content">
        <div class="container-fluid">
            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">添加工艺路线</h3>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'process_route_add' %}{% if is_iframe %}?iframe=1{% endif %}">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="route_no">工艺路线编号 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="route_no" name="route_no" placeholder="请输入工艺路线编号" value="{{ form_data.route_no|default:'' }}" required>
                            <small class="form-text text-muted">工艺路线编号不能重复</small>
                        </div>
                        <div class="form-group">
                            <label for="route_name">工艺路线名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="route_name" name="route_name" placeholder="请输入工艺路线名称" value="{{ form_data.route_name|default:'' }}" required>
                        </div>
                        <div class="form-group">
                            <label>工艺路线详情 <span class="text-danger">*</span></label>
                            <div class="card mt-3">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">工艺详情表</h5>
                                        <button type="button" class="btn btn-primary btn-sm" id="add-process-row">
                                            <i class="fas fa-plus"></i> 添加工序
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="process-table">
                                            <thead class="thead-light">
                                                <tr>
                                                    <th width="5%">序号</th>
                                                    <th width="15%">工序名称 <span class="text-danger">*</span></th>
                                                    <th width="10%">工时</th>
                                                    <th width="15%">设备</th>
                                                    <th width="10%">工价</th>
                                                    <th width="10%">加工方式</th>
                                                    <th width="15%">委外供应商</th>
                                                    <th width="10%">委外单价</th>
                                                    <th width="10%">关键工序</th>
                                                    <th width="5%">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="process-tbody">
                                                <!-- 动态添加的行将在这里 -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <div id="empty-process-message" class="text-center text-muted py-3">
                                        <i class="fas fa-info-circle"></i> 请点击"添加工序"按钮添加工艺步骤
                                    </div>

                                    <!-- 隐藏字段存储数据 -->
                                    <input type="hidden" id="route_content" name="route_content" value="{{ form_data.route_content|default:'' }}">
                                    <input type="hidden" id="route_times" name="route_times" value="{{ form_data.route_times|default:'' }}">
                                    <input type="hidden" id="route_device" name="route_device" value="{{ form_data.route_device|default:'' }}">
                                    <input type="hidden" id="route_price" name="route_price" value="{{ form_data.route_price|default:'' }}">
                                    <input type="hidden" id="route_type" name="route_type" value="{{ form_data.route_type|default:'' }}">
                                    <input type="hidden" id="route_outsupplier" name="route_outsupplier" value="{{ form_data.route_outsupplier|default:'' }}">
                                    <input type="hidden" id="route_outprice" name="route_outprice" value="{{ form_data.route_outprice|default:'' }}">
                                    <input type="hidden" id="route_important" name="route_important" value="{{ form_data.route_important|default:'' }}">
                                    <input type="hidden" id="route_code" name="route_code" value="{{ form_data.route_code|default:'' }}">
                                </div>
                            </div>

                           
                        </div>
                        <div class="form-group">
                            <label for="route_order">排序</label>
                            <input type="number" class="form-control" id="route_order" name="route_order" placeholder="请输入排序" value="{{ form_data.route_order|default:default_order }}">
                        </div>
                        <div class="form-group">
                            <a href="{% url 'process_route_list' %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
{% else %}
    <div class="wrapper">
        <!-- 导航栏 -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- 左侧导航栏切换按钮 -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
            </ul>

            <!-- 右侧导航栏 -->
            <ul class="navbar-nav ml-auto">
                <!-- 用户信息下拉菜单 -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline">{{ user_nick|default:username }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> 个人资料
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{% url 'logout' %}" class="dropdown-item">
                            <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                        </a>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- 左侧边栏 -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- 品牌Logo -->
            <a href="#" class="brand-link">
                <span class="brand-text font-weight-light">物料清单管理系统</span>
            </a>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 用户面板 -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-light"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block">{{ user_nick|default:username }}</a>
                    </div>
                </div>

                <!-- 侧边栏菜单 -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'dashboard' %}" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>
                                    仪表盘
                                </p>
                            </a>
                        </li>
                        
                        <li class="nav-item menu-open">
                            <a href="#" class="nav-link active">
                                <i class="nav-icon fas fa-database"></i>
                                <p>
                                    基础数据
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{% url 'data_dictionary_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>数据字典</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'customer_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>客户信息</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'process_route_list' %}" class="nav-link active">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>工艺路线</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 主体内容 -->
            <section class="content">
                <div class="container-fluid">
                    <!-- 消息提示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">添加工艺路线</h3>
                        </div>
                        <div class="card-body">
                            <form method="post" action="{% url 'process_route_add' %}">
                                {% csrf_token %}
                                <div class="form-group">
                                    <label for="route_no">工艺路线编号 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="route_no" name="route_no" placeholder="请输入工艺路线编号" value="{{ form_data.route_no|default:'' }}" required>
                                    <small class="form-text text-muted">工艺路线编号不能重复</small>
                                </div>
                                <div class="form-group">
                                    <label for="route_name">工艺路线名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="route_name" name="route_name" placeholder="请输入工艺路线名称" value="{{ form_data.route_name|default:'' }}" required>
                                </div>
                                <div class="form-group">
                                    <label>工艺路线详情 <span class="text-danger">*</span></label>
                                    <div class="card mt-3">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">工艺详情表</h5>
                                                <button type="button" class="btn btn-primary btn-sm" id="add-process-row">
                                                    <i class="fas fa-plus"></i> 添加工序
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-bordered" id="process-table">
                                                    <thead class="thead-light">
                                                        <tr>
                                                            <th width="5%">序号</th>
                                                            <th width="15%">工序名称 <span class="text-danger">*</span></th>
                                                            <th width="10%">工时</th>
                                                            <th width="15%">设备</th>
                                                            <th width="10%">工价</th>
                                                            <th width="10%">加工方式</th>
                                                            <th width="15%">委外供应商</th>
                                                            <th width="10%">委外单价</th>
                                                            <th width="10%">关键工序</th>
                                                            <th width="5%">操作</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="process-tbody">
                                                        <!-- 动态添加的行将在这里 -->
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div id="empty-process-message" class="text-center text-muted py-3">
                                                <i class="fas fa-info-circle"></i> 请点击"添加工序"按钮添加工艺步骤
                                            </div>

                                            <!-- 隐藏字段存储数据 -->
                                            <input type="hidden" id="route_content" name="route_content" value="{{ form_data.route_content|default:'' }}">
                                            <input type="hidden" id="route_times" name="route_times" value="{{ form_data.route_times|default:'' }}">
                                            <input type="hidden" id="route_device" name="route_device" value="{{ form_data.route_device|default:'' }}">
                                            <input type="hidden" id="route_price" name="route_price" value="{{ form_data.route_price|default:'' }}">
                                            <input type="hidden" id="route_type" name="route_type" value="{{ form_data.route_type|default:'' }}">
                                            <input type="hidden" id="route_outsupplier" name="route_outsupplier" value="{{ form_data.route_outsupplier|default:'' }}">
                                            <input type="hidden" id="route_outprice" name="route_outprice" value="{{ form_data.route_outprice|default:'' }}">
                                            <input type="hidden" id="route_important" name="route_important" value="{{ form_data.route_important|default:'' }}">
                                        </div>
                                    </div>

                                    
                                </div>
                                <div class="form-group">
                                    <label for="route_order">排序</label>
                                    <input type="number" class="form-control" id="route_order" name="route_order" placeholder="请输入排序" value="{{ form_data.route_order|default:default_order }}">
                                </div>
                                <div class="form-group">
                                    <a href="{% url 'process_route_list' %}" class="btn btn-secondary">取消</a>
                                    <button type="submit" class="btn btn-primary">保存</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    #process-table {
        margin-bottom: 0;
    }

    #process-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        border: 1px solid #dee2e6;
    }

    #process-table td {
        vertical-align: middle;
        border: 1px solid #dee2e6;
    }

    .process-input {
        border: none;
        background: transparent;
        width: 100%;
        padding: 5px;
    }

    .process-input:focus {
        outline: none;
        background-color: #fff3cd;
        border: 1px solid #ffc107;
        border-radius: 3px;
    }

    .process-select {
        border: none;
        background: transparent;
        width: 100%;
        padding: 5px;
    }

    .process-select:focus {
        outline: none;
        background-color: #fff3cd;
        border: 1px solid #ffc107;
        border-radius: 3px;
    }

    .process-checkbox {
        transform: scale(1.2);
    }

    .btn-remove-row {
        padding: 2px 6px;
        font-size: 12px;
    }

    #empty-process-message {
        display: none;
    }

    .table-responsive {
        max-height: 500px;
        overflow-y: auto;
    }

    .drag-handle {
        cursor: move;
        color: #6c757d;
    }

    .drag-handle:hover {
        color: #495057;
    }

    .ui-sortable-helper {
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .ui-sortable-placeholder {
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        visibility: visible !important;
        height: 50px;
    }
</style>
<!-- Select2 CSS -->
<link href="/static/css/select2.min.css" rel="stylesheet" />
<link href="/static/css/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<link rel="stylesheet" href="/static/css/jquery-ui.css">

{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="/static/js/select2.min.js"></script>
<script src="/static/js/jquery-ui.min.js"></script>
<script>
    $(document).ready(function() {
        // 设备数据
        let equipmentData = [];

        // 获取设备数据
        $.ajax({
            url: '{% url "process_route_add" %}?get_equipment=1',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if (data.success) {
                    equipmentData = data.equipment;
                } else {
                    console.error('加载设备数据失败:', data.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('加载设备数据请求失败:', error);
            }
        });

        // 检查是否有工艺内容，更新空提示信息
        function updateEmptyMessage() {
            const rowCount = $('#process-tbody tr').length;
            if (rowCount === 0) {
                $('#empty-process-message').show();
                $('#process-table').hide();
            } else {
                $('#empty-process-message').hide();
                $('#process-table').show();
            }
        }

        // 添加工序行
        $('#add-process-row').on('click', function() {
            addProcessRow();
        });

        // 添加工序行函数
        function addProcessRow(data = {}) {
            const rowIndex = $('#process-tbody tr').length;
            const sequenceNumber = rowIndex + 1;

            // 构建设备选择框选项
            let equipmentOptions = '<option value="">请选择设备</option>';
            equipmentData.forEach(equipment => {
                const selected = data.device === equipment.id ? 'selected' : '';
                equipmentOptions += `<option value="${equipment.id}" ${selected}>${equipment.name} (${equipment.no})</option>`;
            });

            // 格式化价格（保留两位小数）
            const formatPrice = (price) => {
                if (!price || price === '') return '';
                const num = parseFloat(price);
                return isNaN(num) ? price : num.toFixed(2);
            };

            const row = `
                <tr data-index="${rowIndex}">
                    <td class="text-center">
                        <span class="sequence-number">${sequenceNumber}</span>
                        <i class="fas fa-grip-vertical drag-handle ml-2" style="cursor: move;"></i>
                    </td>
                    <td>
                        <select class="form-control select2 process-select" name="process_content_${rowIndex}"
                                id="process_content_${rowIndex}" required>
                            <option value="">请选择工序</option>
                        </select>
                    </td>
                    <td>
                        <input type="text" class="process-input" name="process_time_${rowIndex}"
                               placeholder="如：1.5小时" value="${data.time || ''}">
                    </td>
                    <td>
                        <select class="process-select" name="process_device_${rowIndex}">
                            ${equipmentOptions}
                        </select>
                    </td>
                    <td>
                        <input type="text" class="process-input price-input" name="process_price_${rowIndex}"
                               placeholder="工价" value="${formatPrice(data.price)}">
                    </td>
                    <td>
                        <select class="process-select" name="process_type_${rowIndex}">
                            <option value="">请选择</option>
                            <option value="自制" ${data.type === '自制' ? 'selected' : ''}>自制</option>
                            <option value="委外" ${data.type === '委外' ? 'selected' : ''}>委外</option>
                        </select>
                    </td>
                    <td>
                        <input type="text" class="process-input" name="process_outsupplier_${rowIndex}"
                               placeholder="委外供应商" value="${data.outsupplier || ''}">
                    </td>
                    <td>
                        <input type="text" class="process-input price-input" name="process_outprice_${rowIndex}"
                               placeholder="委外单价" value="${formatPrice(data.outprice)}">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" class="process-checkbox" name="process_important_${rowIndex}"
                               value="1" ${data.important === '1' ? 'checked' : ''}>
                    </td>
                    <td class="text-center">
                        <button type="button" class="btn btn-danger btn-sm btn-remove-row">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            $('#process-tbody').append(row);
            updateEmptyMessage();

            // 只在不跳过时更新序号和属性
            if (!window.skipSequenceUpdate) {
                updateSequenceNumbersOnly();
            }

            // 绑定删除事件
            bindRemoveEvents();

            // 绑定输入事件
            bindInputEvents();

            // 绑定价格输入格式化事件
            bindPriceFormatEvents();

            // 初始化新添加行的工序名称Select2
            initProcessSelect2(`#process_content_${rowIndex}`, data.content);
        }
        
        // 绑定删除事件
        function bindRemoveEvents() {
            $('.btn-remove-row').off('click').on('click', function() {
                $(this).closest('tr').remove();
                updateRowIndices();
                updateEmptyMessage();
                updateHiddenFields();
            });
        }

        // 绑定输入事件
        function bindInputEvents() {
            $('.process-input, .process-select, .process-checkbox').off('input change').on('input change', function() {
                updateHiddenFields();
            });
        }

        // 绑定价格格式化事件
        function bindPriceFormatEvents() {
            $('.price-input').off('blur').on('blur', function() {
                const value = $(this).val().trim();
                if (value && value !== '') {
                    const num = parseFloat(value);
                    if (!isNaN(num)) {
                        $(this).val(num.toFixed(2));
                        updateHiddenFields();
                    }
                }
            });
        }

        // 初始化工序名称Select2
        function initProcessSelect2(selector, selectedValue = '') {
            console.log('初始化Select2:', selector, '选中值:', selectedValue);

            // 检查元素是否存在
            if (!$(selector).length) {
                console.error('Select2初始化失败：元素不存在', selector);
                return;
            }

            // 检查是否已经初始化过Select2
            if ($(selector).hasClass('select2-hidden-accessible')) {
                console.log('Select2已经初始化过，跳过重复初始化', selector);
                return;
            }

            $(selector).select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: '请选择工序',
                allowClear: false,
                ajax: {
                    url: '{% url "process_route_add" %}',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            search_processes: 1,
                            q: params.term || ''
                        };
                    },
                    processResults: function (data) {
                        console.log('AJAX响应数据:', data);
                        if (data.success && data.processes) {
                            console.log('工序数据:', data.processes);
                            return {
                                results: data.processes.map(function(process) {
                                    return {
                                        id: process.name,
                                        text: process.name,
                                        code: process.code
                                    };
                                })
                            };
                        } else {
                            console.log('没有工序数据或请求失败');
                            return { results: [] };
                        }
                    },
                    cache: true
                },
                minimumInputLength: 0
            });

            // 如果有选中值，设置选中项
            if (selectedValue) {
                console.log('设置Select2选中值:', selectedValue);

                // 先创建一个临时选项并设置为选中
                const tempOption = new Option(selectedValue, selectedValue, true, true);
                $(selector).append(tempOption);

                // 触发Select2更新显示
                $(selector).trigger('change');

                // 异步获取完整的工序数据，包括code，但不改变当前选中状态
                $.ajax({
                    url: '{% url "process_route_add" %}',
                    data: { search_processes: 1, q: selectedValue },
                    dataType: 'json',
                    success: function(data) {
                        if (data.success && data.processes) {
                            const process = data.processes.find(p => p.name === selectedValue);
                            if (process) {
                                // 更新选项的data属性，但保持选中状态
                                const $option = $(selector).find('option[value="' + selectedValue + '"]');
                                if ($option.length > 0) {
                                    $option.data('code', process.code);
                                    console.log('已设置工序代码:', process.code, '对应工序:', selectedValue);
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.warn('获取工序数据失败，但保持当前选中值:', error);
                    }
                });
            }

            // 绑定change事件
            $(selector).on('change', function() {
                updateHiddenFields();
            });
        }

        // 测试Select2功能的辅助函数
        window.testSelect2 = function() {
            console.log('=== Select2测试开始 ===');

            // 检查jQuery和Select2是否加载
            console.log('jQuery版本:', typeof jQuery !== 'undefined' ? jQuery.fn.jquery : '未加载');
            console.log('Select2是否可用:', typeof jQuery !== 'undefined' && jQuery.fn.select2 ? '是' : '否');

            // 检查现有的工序选择框
            const processSelects = $('#process-tbody select[name^="process_content_"]');
            console.log('找到工序选择框数量:', processSelects.length);

            processSelects.each(function(index) {
                const $select = $(this);
                const isInitialized = $select.hasClass('select2-hidden-accessible');
                console.log(`工序选择框 ${index + 1}:`, {
                    id: $select.attr('id'),
                    initialized: isInitialized,
                    value: $select.val()
                });
            });

            // 测试AJAX请求
            $.ajax({
                url: '{% url "process_route_add" %}',
                data: { search_processes: 1, q: '' },
                dataType: 'json',
                success: function(data) {
                    console.log('AJAX测试成功:', data);
                },
                error: function(xhr, status, error) {
                    console.error('AJAX测试失败:', status, error);
                }
            });

            console.log('=== Select2测试结束 ===');
        };

        // 只更新序号和属性，不重新初始化Select2
        function updateSequenceNumbersOnly() {
            $('#process-tbody tr').each(function(index) {
                $(this).find('.sequence-number').text(index + 1);
                $(this).attr('data-index', index);

                // 更新所有input和select的name属性
                $(this).find('input, select').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        const baseName = name.replace(/_\d+$/, '');
                        $(this).attr('name', baseName + '_' + index);

                        // 如果是工序选择框，也要更新ID
                        if (baseName === 'process_content') {
                            $(this).attr('id', baseName + '_' + index);
                        }
                    }
                });
            });
        }

        // 更新序号并重新初始化Select2（仅在必要时使用）
        function updateSequenceNumbers() {
            $('#process-tbody tr').each(function(index) {
                $(this).find('.sequence-number').text(index + 1);
                $(this).attr('data-index', index);

                // 更新所有input和select的name属性
                $(this).find('input, select').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        const baseName = name.replace(/_\d+$/, '');
                        $(this).attr('name', baseName + '_' + index);

                        // 如果是工序选择框，也要更新ID
                        if (baseName === 'process_content') {
                            $(this).attr('id', baseName + '_' + index);
                        }
                    }
                });

                // 重新初始化工序Select2
                const processSelect = $(this).find('select[name^="process_content_"]');
                if (processSelect.length > 0) {
                    let currentValue = '';

                    // 如果已经初始化了Select2，先获取当前选中的值
                    if (processSelect.hasClass('select2-hidden-accessible')) {
                        try {
                            const selectedData = processSelect.select2('data');
                            if (selectedData && selectedData.length > 0) {
                                currentValue = selectedData[0].id || selectedData[0].text || '';
                            }
                        } catch (e) {
                            currentValue = processSelect.val() || '';
                        }

                        // 销毁Select2
                        processSelect.select2('destroy');
                    } else {
                        currentValue = processSelect.val() || '';
                    }

                    // 只有当有选中值时才重新初始化
                    if (currentValue) {
                        console.log('重新初始化Select2:', processSelect.attr('id'), '选中值:', currentValue);
                        initProcessSelect2('#' + processSelect.attr('id'), currentValue);
                    } else {
                        // 如果没有选中值，初始化为空的Select2
                        initProcessSelect2('#' + processSelect.attr('id'), '');
                    }
                }
            });
        }

        // 更新行索引
        function updateRowIndices() {
            updateSequenceNumbers();
        }

        // 更新隐藏字段
        function updateHiddenFields() {
            const contents = [];
            const times = [];
            const devices = [];
            const prices = [];
            const types = [];
            const outsuppliers = [];
            const outprices = [];
            const importants = [];
            const codes = [];

            $('#process-tbody tr').each(function() {
                const $row = $(this);
                const processSelect = $row.find('select[name*="process_content"]');
                const processName = processSelect.val() || '';

                // 从Select2的数据中获取code
                let processCode = '';
                try {
                    if (processSelect.hasClass('select2-hidden-accessible')) {
                        const selectedData = processSelect.select2('data');
                        if (selectedData && selectedData.length > 0 && selectedData[0].code) {
                            processCode = selectedData[0].code;
                        }
                    }
                } catch (error) {
                    console.warn('获取Select2数据时出错:', error);
                }

                contents.push(processName);
                codes.push(processCode);
                times.push($row.find('input[name*="process_time"]').val() || '');
                devices.push($row.find('select[name*="process_device"]').val() || '');
                prices.push($row.find('input[name*="process_price"]').val() || '');
                types.push($row.find('select[name*="process_type"]').val() || '');
                outsuppliers.push($row.find('input[name*="process_outsupplier"]').val() || '');
                outprices.push($row.find('input[name*="process_outprice"]').val() || '');
                importants.push($row.find('input[name*="process_important"]').is(':checked') ? '1' : '0');
            });

            $('#route_content').val(contents.join('|'));
            $('#route_code').val(codes.join('|'));
            $('#route_times').val(times.join('|'));
            $('#route_device').val(devices.join('|'));
            $('#route_price').val(prices.join('|'));
            $('#route_type').val(types.join('|'));
            $('#route_outsupplier').val(outsuppliers.join('|'));
            $('#route_outprice').val(outprices.join('|'));
            $('#route_important').val(importants.join('|'));
        }

        // 初始化表格排序
        function initSortable() {
            $('#process-tbody').sortable({
                handle: '.drag-handle',
                placeholder: 'ui-sortable-placeholder',
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    return $helper;
                },
                update: function(event, ui) {
                    updateSequenceNumbers();
                    updateHiddenFields();
                }
            });
        }

        // 从隐藏字段加载数据
        function loadFromHiddenFields() {
            const contentValue = $('#route_content').val() || '';
            const codeValue = $('#route_code').val() || '';
            const timesValue = $('#route_times').val() || '';
            const devicesValue = $('#route_device').val() || '';
            const pricesValue = $('#route_price').val() || '';
            const typesValue = $('#route_type').val() || '';
            const outsuppliersValue = $('#route_outsupplier').val() || '';
            const outpricesValue = $('#route_outprice').val() || '';
            const importantsValue = $('#route_important').val() || '';

            console.log('加载隐藏字段数据:', {
                content: contentValue,
                code: codeValue,
                times: timesValue
            });

            // 处理None值和0值
            const cleanValue = (val) => {
                if (!val || val === 'None' || val === 'null' || val === '0') return '';
                return val;
            };

            const contents = cleanValue(contentValue).split('|').filter(x => x.trim());
            const codes = cleanValue(codeValue).split('|');
            const times = cleanValue(timesValue).split('|');
            const devices = cleanValue(devicesValue).split('|');
            const prices = cleanValue(pricesValue).split('|');
            const types = cleanValue(typesValue).split('|');
            const outsuppliers = cleanValue(outsuppliersValue).split('|');
            const outprices = cleanValue(outpricesValue).split('|');
            const importants = cleanValue(importantsValue).split('|');

            // 临时禁用序号更新，避免重复调用
            window.skipSequenceUpdate = true;

            contents.forEach((content, index) => {
                if (content.trim()) {
                    addProcessRow({
                        content: content,
                        code: cleanValue(codes[index]) || '',
                        time: cleanValue(times[index]) || '',
                        device: cleanValue(devices[index]) || '',
                        price: cleanValue(prices[index]) || '',
                        type: cleanValue(types[index]) || '',
                        outsupplier: cleanValue(outsuppliers[index]) || '',
                        outprice: cleanValue(outprices[index]) || '',
                        important: cleanValue(importants[index]) || '0'
                    });
                }
            });

            // 恢复序号更新并统一更新一次
            window.skipSequenceUpdate = false;
            updateSequenceNumbersOnly();
        }

        // 初始化
        updateEmptyMessage();
        initSortable();

        // 如果有现有数据，加载它们
        if ($('#route_content').val()) {
            loadFromHiddenFields();
        }

        // 确保所有现有的工序选择框都被初始化
        setTimeout(function() {
            $('#process-tbody select[name^="process_content_"]').each(function() {
                const $select = $(this);
                if (!$select.hasClass('select2-hidden-accessible')) {
                    console.log('发现未初始化的工序选择框，正在初始化:', $select.attr('id'));
                    initProcessSelect2('#' + $select.attr('id'), $select.val());
                }
            });
        }, 100);

        // 自动隐藏提示信息
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
    });
</script>



{% endblock %}