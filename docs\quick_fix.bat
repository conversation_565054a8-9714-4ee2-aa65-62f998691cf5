@echo off
echo 快速修复MySQL兼容性问题...

:: 激活虚拟环境
call .venv\Scripts\activate.bat

:: 卸载当前Django版本
echo 卸载当前Django版本...
pip uninstall Django -y

:: 安装兼容MySQL 5.7的Django版本
echo 安装Django 3.1.14（完全兼容MySQL 5.7）...
pip install Django==3.1.14

:: 安装其他依赖
echo 安装其他依赖...
pip install pymysql==1.1.0
pip install openpyxl==3.1.2
pip install Pillow==10.1.0
pip install python-dateutil==2.8.2
pip install pytz==2023.3.post1

:: 进入项目目录
cd bom_sys

:: 检查Django配置
echo 检查Django配置...
python manage.py check

echo 修复完成！现在可以运行服务器了。
echo 运行命令: python manage.py runserver 0.0.0.0:8000

pause
