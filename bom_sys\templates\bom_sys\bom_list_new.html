{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}BOM清单{% endblock %}

{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}

{% block content %}
<div>
    <!-- Content Header (Page header) -->


    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- 搜索条件 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">搜索条件</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% csrf_token %}
                    <form id="searchForm" method="get">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="drawing_number">客户图号</label>
                                    <input type="text" class="form-control" id="drawing_number" name="drawing_number" 
                                        value="{{ query_params.drawing_number }}" placeholder="输入客户图号">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="material_code">物料编码</label>
                                    <input type="text" class="form-control" id="material_code" name="material_code" 
                                        value="{{ query_params.material_code }}" placeholder="输入物料编码">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="material_version">版本</label>
                                    <input type="text" class="form-control" id="material_version" name="material_version" 
                                        value="{{ query_params.material_version }}" placeholder="输入版本">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="material_name">存货名称</label>
                                    <input type="text" class="form-control" id="material_name" name="material_name" 
                                        value="{{ query_params.material_name }}" placeholder="输入存货名称">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="customer_name">客户名称</label>
                                    <select class="form-control select2" id="customer_name" name="customer_name" data-placeholder="请选择或输入客户名称">
                                        <option value="">全部</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.customer_name }}" data-code="{{ customer.customer_code }}" {% if query_params.customer_name == customer.customer_name %}selected{% endif %}>{{ customer.customer_name }}({{ customer.customer_code }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="d-flex flex-wrap">
                                        <button type="submit" class="btn btn-primary mr-2 mb-1">
                                             搜索
                                        </button>
                                        <button type="button" class="btn btn-secondary mr-2 mb-1" id="resetBtn">
                                             重置
                                        </button>
                                        <button type="button" class="btn btn-success mr-2 mb-1" id="exportBtn" disabled>
                                            导出
                                        </button>
                                        <button type="button" class="btn btn-info mb-1" id="addParentBtn">
                                            <i class="fas fa-plus mr-1"></i>添加父件
                                        </button>
                                        <input type="hidden" name="per_page" value="20">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 结果列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">BOM清单结果</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th style="width: 40px;">
                                    <div class="custom-control custom-checkbox">
                                        <input class="custom-control-input" type="checkbox" id="selectAll">
                                        <label for="selectAll" class="custom-control-label"></label>
                                    </div>
                                </th>
                                <th>序号</th>
                                <th>客户图号</th>
                                <th>物料编码</th>
                                <th>版本</th>
                                <th>存货名称</th>
                                <th>客户名称</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if materials %}
                                {% for material in materials %}
                                    <tr class="clickable-row" data-bom-id="{{ material.bom_id }}" data-material-id="{{ material.material_id }}" data-customer-code="{{ material.customer_code }}" data-customer-name="{{ material.customer_name }}">
                                        <td>
                                            <div class="custom-control custom-checkbox">
                                                <input class="custom-control-input material-checkbox" type="checkbox"
                                                       id="material_{{ material.bom_id }}"
                                                       value="{{ material.bom_id }}"
                                                       data-material-id="{{ material.material_id }}"
                                                       data-drawing-no="{{ material.material_drawingno }}"
                                                       data-material-code="{{ material.material_no }}"
                                                       data-version="{{ material.material_version }}"
                                                       data-material-name="{{ material.material_name }}"
                                                       data-customer-code="{{ material.customer_code }}"
                                                       data-customer-name="{{ material.customer_name }}">
                                                <label for="material_{{ material.bom_id }}" class="custom-control-label"></label>
                                            </div>
                                        </td>
                                        <td>{{ forloop.counter|stringformat:"04d" }}</td>
                                        <td>{{ material.material_drawingno }}</td>
                                        <td>{{ material.material_no }}</td>
                                        <td>{{ material.material_version }}</td>
                                        <td>{{ material.material_name }}</td>
                                        <td>{{ material.customer_name }}({{ material.customer_code }})</td>
                                        <td>
                                            <a href="{% url 'bom_tree_view' bom_id=material.bom_id %}" class="btn btn-sm btn-info" title="查看BOM树">
                                                <i class="fas fa-sitemap"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">暂无数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                <!-- 分页 -->
                <div class="card-footer clearfix">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="text-muted">
                                显示 {{ materials.start_index }} 到 {{ materials.end_index }} 条，共 {{ materials.paginator.count }} 条记录
                            </p>
                        </div>
                        <div class="col-md-6">
                            <ul class="pagination pagination-sm m-0 float-right">
                                {% if materials.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% for key, value in query_params.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&laquo;&laquo;</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ materials.previous_page_number }}{% for key, value in query_params.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&laquo;</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">&laquo;&laquo;</a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">&laquo;</a>
                                    </li>
                                {% endif %}

                                {% for i in materials.paginator.page_range %}
                                    {% if i > materials.number|add:"-3" and i < materials.number|add:"3" %}
                                        <li class="page-item {% if i == materials.number %}active{% endif %}">
                                            <a class="page-link" href="?page={{ i }}{% for key, value in query_params.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if materials.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ materials.next_page_number }}{% for key, value in query_params.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&raquo;</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ materials.paginator.num_pages }}{% for key, value in query_params.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&raquo;&raquo;</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">&raquo;</a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">&raquo;&raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- 导出模板选择模态框 -->
<div class="modal fade" id="exportTemplateModal" tabindex="-1" role="dialog" aria-labelledby="exportTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportTemplateModalLabel">
                    <i class="fas fa-file-export mr-2"></i>选择导出模板
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p class="mb-4">请选择要使用的导出模板：</p>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card template-card" data-template="1">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                                    <h5>模板一</h5>
                                    <p class="text-muted">标准导出模板</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card template-card" data-template="2">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-excel fa-3x text-primary mb-3"></i>
                                    <h5>模板二</h5>
                                    <p class="text-muted">详细导出模板</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 遮罩等待框 -->
<div id="loadingMask" class="loading-mask">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text" id="loadingText">正在处理，请稍候...</p>
    </div>
</div>

<!-- 包含公共BOM节点操作模态框 -->
{% include 'bom_sys/includes/bom_node_modal.html' %}

<!-- 保存遮罩层 -->
<div id="savingOverlay" class="saving-overlay" style="display: none;">
    <div class="saving-content">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">保存中...</span>
        </div>
        <div class="mt-3">
            <h5>保存中，请稍候...</h5>
            <p class="text-muted">正在保存物料信息</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="/static/css/select2.min.css" rel="stylesheet" />
<link href="/static/css/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- 遮罩等待框样式 -->
<style>
    .loading-mask {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
        justify-content: center;
        align-items: center;
    }

    .loading-content {
        background-color: white;
        padding: 30px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-width: 200px;
    }

    .loading-spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: #333;
        font-size: 16px;
        margin: 0;
    }

    /* 保存遮罩层样式 */
    .saving-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .saving-content {
        background-color: white;
        padding: 30px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-width: 200px;
    }

    .saving-content h5 {
        color: #333;
        margin-bottom: 10px;
    }

    .saving-content .text-muted {
        color: #666 !important;
        font-size: 14px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="/static/js/select2.min.js"></script>
<script>
    // 遮罩等待框函数
    function showLoadingMask(text) {
        if (text) {
            $('#loadingText').text(text);
        }
        $('#loadingMask').css('display', 'flex');
    }

    function hideLoadingMask() {
        $('#loadingMask').hide();
    }

    // 保存遮罩层函数
    function showSavingOverlay() {
        $('#savingOverlay').fadeIn(300);
    }

    function hideSavingOverlay() {
        $('#savingOverlay').fadeOut(300);
    }

    // 刷新页面数据
    function refreshPageData() {
        location.reload();
    }

    // 保存超时处理
    let saveTimeout;
    function setSaveTimeout() {
        // 清除之前的超时
        if (saveTimeout) {
            clearTimeout(saveTimeout);
        }

        saveTimeout = setTimeout(function() {
            hideSavingOverlay();
            alert('保存超时，请检查网络连接后重试');
        }, 30000);
    }

    function clearSaveTimeout() {
        if (saveTimeout) {
            clearTimeout(saveTimeout);
            saveTimeout = null;
        }
    }

    // iframe消息监听
    function bindIframeMessageListener() {
        window.addEventListener('message', function(event) {
            console.log('收到iframe消息:', event.data);

            // 检查消息来源和内容
            if (event.data && event.data.action === 'refreshBomData') {
                console.log('收到iframe刷新消息:', event.data);

                // 清除保存超时
                clearSaveTimeout();

                // 隐藏保存遮罩层
                hideSavingOverlay();

                if (event.data.success) {
                    // 关闭模态框
                    $('#bomNodeModal').modal('hide');

                    // 刷新页面数据
                    refreshPageData();

                } else {
                    // 显示错误消息
                    alert('保存失败：' + (event.data.error || event.data.message || '未知错误'));
                }
            }
        });
    }

    // 添加iframe加载完成监听，用于检测保存成功
    function monitorIframeForSuccess() {
        const iframe = document.getElementById('materialAddIframe');
        if (iframe) {
            iframe.onload = function() {
                try {
                    // 检查iframe的URL是否包含success=true
                    const iframeUrl = iframe.contentWindow.location.href;
                    console.log('iframe加载完成，URL:', iframeUrl);

                    if (iframeUrl.includes('success=true')) {
                        console.log('检测到保存成功，手动触发刷新');

                        // 清除保存超时
                        clearSaveTimeout();

                        // 隐藏保存遮罩层
                        hideSavingOverlay();

                        // 关闭模态框
                        $('#bomNodeModal').modal('hide');

                        // 刷新页面数据
                        refreshPageData();

                    }
                } catch (e) {
                    // 跨域访问限制，忽略错误
                    console.log('无法访问iframe URL（跨域限制）');
                }
            };
        }
    }

    $(document).ready(function() {
        // 全选/取消全选功能
        $("#selectAll").change(function() {
            var isChecked = $(this).is(':checked');
            $(".material-checkbox").prop('checked', isChecked);
            updateExportButton();
        });

        // 单个复选框变化时更新全选状态和导出按钮
        $(".material-checkbox").change(function() {
            var totalCheckboxes = $(".material-checkbox").length;
            var checkedCheckboxes = $(".material-checkbox:checked").length;

            // 更新全选复选框状态
            if (checkedCheckboxes === 0) {
                $("#selectAll").prop('indeterminate', false).prop('checked', false);
            } else if (checkedCheckboxes === totalCheckboxes) {
                $("#selectAll").prop('indeterminate', false).prop('checked', true);
            } else {
                $("#selectAll").prop('indeterminate', true);
            }

            updateExportButton();
        });

        // 更新导出按钮状态
        function updateExportButton() {
            var checkedCount = $(".material-checkbox:checked").length;
            if (checkedCount > 0) {
                $("#exportBtn").prop('disabled', false).text('导出 (' + checkedCount + ')');
            } else {
                $("#exportBtn").prop('disabled', true).text('导出');
            }
        }

        // 导出Excel功能
        $("#exportBtn").click(function() {
            var selectedMaterials = [];
            $(".material-checkbox:checked").each(function() {
                var checkbox = $(this);
                selectedMaterials.push({
                    bom_id: checkbox.val(),
                    material_id: checkbox.data('material-id'),
                    drawing_no: checkbox.data('drawing-no'),
                    material_code: checkbox.data('material-code'),
                    version: checkbox.data('version'),
                    material_name: checkbox.data('material-name'),
                    customer_code: checkbox.data('customer-code'),
                    customer_name: checkbox.data('customer-name')
                });
            });

            if (selectedMaterials.length === 0) {
                alert('请选择要导出的物料！');
                return;
            }

            // 存储选中的物料数据，供模板选择使用
            window.selectedMaterialsForExport = selectedMaterials;

            // 显示模板选择模态框
            $('#exportTemplateModal').modal('show');



        });

        // 模板选择处理
        $('.template-card').click(function() {
            var templateType = $(this).data('template');
            var selectedMaterials = window.selectedMaterialsForExport;

            if (!selectedMaterials || selectedMaterials.length === 0) {
                alert('没有选中的物料数据！');
                return;
            }

            // 关闭模态框
            $('#exportTemplateModal').modal('hide');

            // 显示遮罩等待框
            showLoadingMask('正在导出Excel，请稍候...');

            // 根据模板类型选择不同的导出URL
            var exportUrl = (templateType === '2' || templateType === 2) ?
                '/bom-export-excel-template2/' :
                '/bom-export-excel/';

            console.log('选择的模板类型:', templateType);
            console.log('模板类型的类型:', typeof templateType);
            console.log('导出URL:', exportUrl);

            // 使用Ajax请求导出
            $.ajax({
                url: exportUrl,
                type: 'POST',
                data: {
                    'selected_materials': JSON.stringify(selectedMaterials),
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val() || '{{ csrf_token }}'
                },
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    console.log('AJAX成功响应:', {data: data, status: status, xhr: xhr});
                    console.log('响应内容类型:', xhr.getResponseHeader('Content-Type'));

                    // 隐藏遮罩等待框
                    hideLoadingMask();

                    // 检查响应类型
                    var contentType = xhr.getResponseHeader('Content-Type');
                    if (contentType && contentType.indexOf('application/json') !== -1) {
                        // 如果是JSON响应，说明有错误
                        var reader = new FileReader();
                        reader.onload = function() {
                            try {
                                var result = JSON.parse(reader.result);
                                alert('导出失败: ' + result.error);
                            } catch (e) {
                                alert('导出失败: 响应解析错误');
                            }
                        };
                        reader.readAsText(data);
                    } else {
                        // 如果是Excel文件，创建下载链接
                        var blob = new Blob([data], {
                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        });
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;

                        // 从响应头获取文件名
                        var filename = templateType === '2' || templateType === 2 ? 'BOM清单导出_模板二.xlsx' : 'BOM清单导出.xlsx';
                        var disposition = xhr.getResponseHeader('Content-Disposition');
                        if (disposition && disposition.indexOf('filename=') !== -1) {
                            var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                            var matches = filenameRegex.exec(disposition);
                            if (matches != null && matches[1]) {
                                filename = matches[1].replace(/['"]/g, '');
                            }
                        }

                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);

                        console.log('文件下载完成:', filename);
                    }
                },
                error: function(xhr, status, error) {
                    // 隐藏遮罩等待框
                    hideLoadingMask();

                    console.log('AJAX错误:', {xhr: xhr, status: status, error: error});
                    console.log('响应状态:', xhr.status);
                    console.log('响应文本:', xhr.responseText);

                    // 处理错误响应
                    try {
                        if (xhr.responseJSON) {
                            alert('导出失败: ' + xhr.responseJSON.error);
                        } else if (xhr.responseText) {
                            // 尝试解析响应文本
                            try {
                                var errorResponse = JSON.parse(xhr.responseText);
                                alert('导出失败: ' + errorResponse.error);
                            } catch (parseError) {
                                alert('导出失败: ' + xhr.responseText);
                            }
                        } else {
                            alert('导出失败: ' + error);
                        }
                    } catch (e) {
                        console.error('错误处理失败:', e);
                        alert('导出失败: 未知错误');
                    }
                }
            });
        });

        // 重置按钮
        $("#resetBtn").click(function() {
            // 清空所有输入框
            $("#drawing_number").val("");
            $("#material_code").val("");
            $("#material_version").val("");
            $("#material_name").val("");
            $("#customer_name").val("").trigger('change'); // 清空并触发change事件
            // 提交表单
            $("#searchForm").submit();
        });
        
        // 表格行双击事件
        $(".clickable-row").dblclick(function() {
            var bomId = $(this).data("bom-id");
            if (bomId) {
                // 判断是否在iframe中
                if (window.parent && window.parent !== window) {
                    // 在iframe中，通过父窗口的URL处理
                    var bomUrl = "{% url 'bom_tree_view' bom_id=0 %}".replace('0', bomId) + "?iframe=1";
                    if (window.parent.location.href.includes("iframe=1")) {
                        // 如果父页面是iframe模式，直接替换当前iframe的URL
                        window.location.href = bomUrl;
                    } else {
                        // 通知父页面打开新标签
                        if (window.parent.openTab) {
                            window.parent.openTab("BOM树视图", bomUrl, "fas fa-sitemap", "bom-tree-" + bomId);
                        } else {
                            window.location.href = bomUrl;
                        }
                    }
                } else {
                    // 不在iframe中，直接跳转
                    window.location.href = "{% url 'bom_tree_view' bom_id=0 %}".replace('0', bomId);
                }
            }
        });
        
        // 添加鼠标悬停效果，提示可双击
        $(".clickable-row").hover(
            function() {
                $(this).css("cursor", "pointer");
                $(this).addClass("table-hover-highlight");
            },
            function() {
                $(this).removeClass("table-hover-highlight");
            }
        );

        // 初始化Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%',
            allowClear: true,
            placeholder: '请选择或输入客户名称',
            language: {
                noResults: function() {
                    return "没有找到匹配的客户";
                },
                searching: function() {
                    return "搜索中...";
                }
            },
            // 自定义匹配方法，同时匹配名称和代码
            matcher: function(params, data) {
                // 如果没有搜索词，返回所有数据
                if ($.trim(params.term) === '') {
                    return data;
                }

                // 搜索词转为小写
                var term = params.term.toLowerCase();

                // 获取选项文本和代码
                var text = data.text.toLowerCase();
                var code = $(data.element).data('code') ? $(data.element).data('code').toLowerCase() : '';

                // 如果名称或代码包含搜索词，返回该数据
                if (text.indexOf(term) > -1 || code.indexOf(term) > -1) {
                    return data;
                }

                // 否则返回null表示不匹配
                return null;
            }
        });

        // 添加父件按钮点击事件
        $("#addParentBtn").click(function() {
            showBomNodeModal('addParent', 0);
        });
    });

    // BOM节点模态框相关函数
    function showBomNodeModal(action, parentBomId) {
        $('#bomNodeModal').data('action', action);
        $('#bomNodeModal').data('parent-bom-id', parentBomId);

        // 重置表单
        resetBomNodeModal();

        // 根据操作类型设置不同的界面
        if (action === 'addParent') {
            // 添加父件模式
            $('#bomNodeModal .modal-title').html('<i class="fas fa-plus mr-2"></i>添加父件');

            // 设置iframe URL（不传递客户代码，让用户在模态框中选择）
            const iframeUrl = `/materials/add/?source_type=bom_list&bom_pid=0&iframe=1`;
            $('#materialAddIframe').attr('src', iframeUrl);

            // 启动iframe监听
            setTimeout(function() {
                monitorIframeForSuccess();
            }, 1000);
        } else {
            // 添加子件模式（保持原有逻辑）
            const parentName = '未知'; // 在bom_list_new页面中无法获取父节点名称
            $('#bomNodeModal .modal-title').html('<i class="fas fa-plus mr-2"></i>添加子件');

        }

        $('#bomNodeModal').modal('show');
    }

    function resetBomNodeModal() {
        // 重置单选按钮到默认状态（创建新物料）
        $('input[name="nodeAction"][value="create"]').prop('checked', true);

        // 重置按钮组的视觉状态
        $('.btn-group-toggle label').removeClass('active');
        $('.btn-group-toggle label:first').addClass('active');

        // 显示创建新物料区域，隐藏绑定现有物料区域
        $('#createMaterialSection').show();
        $('#bindMaterialSection').hide();

        // 显示保存按钮，隐藏绑定按钮
        $('#saveMaterialBtn').show();
        $('#confirmNodeAction').hide();

        // 重置BOM基本信息
        $('#bomNum').val(1);
        $('#bomLossRate').val(0);
        $('#bomPartCount').val(1);
        $('#bomProduceCount').val(1);

        // 清空物料搜索结果
        $('#materialSearchResults').html('<tr><td colspan="5" class="text-center text-muted">请输入搜索条件</td></tr>');
        $('#addChildMaterialSearchInput').val('');

        // 重置客户选择
        $('#parentCustomerSelect').val('').trigger('change');
    }

    function loadCustomerOptions() {
        // 从现有的客户下拉框中获取选项
        const customerSelect = $('#customer_name');
        const parentCustomerSelect = $('#parentCustomerSelect');

        // 清空现有选项
        parentCustomerSelect.empty();
        parentCustomerSelect.append('<option value="">请选择客户</option>');

        // 复制客户选项
        customerSelect.find('option').each(function() {
            if ($(this).val() !== '') {
                parentCustomerSelect.append($(this).clone());
            }
        });

        // 初始化Select2
        parentCustomerSelect.select2({
            theme: 'bootstrap-5',
            width: '100%',
            allowClear: true,
            placeholder: '请选择客户',
            dropdownParent: $('#bomNodeModal')
        });
    }

    // 操作类型切换事件
    $(document).on('change', 'input[name="nodeAction"]', function() {
        const action = $(this).val();
        if (action === 'create') {
            // 显示创建新物料区域
            $('#createMaterialSection').show();
            $('#bindMaterialSection').hide();
            $('#saveMaterialBtn').show();
            $('#confirmNodeAction').hide();
        } else if (action === 'bind') {
            // 显示绑定现有物料区域
            $('#createMaterialSection').hide();
            $('#bindMaterialSection').show();
            $('#saveMaterialBtn').hide();
            $('#confirmNodeAction').show();
        }
    });

    // 客户选择变化事件
    $(document).on('change', '#parentCustomerSelect', function() {
        const selectedCustomer = $(this).find('option:selected');
        const customerCode = selectedCustomer.data('code');
        const customerName = selectedCustomer.text();

        if (customerCode) {
            // 更新iframe URL，传递选中的客户信息
            const currentAction = $('#bomNodeModal').data('action');
            if (currentAction === 'addParent') {
                const iframeUrl = `/materials/add/?source_type=bom_list&bom_pid=0&customer_code=${customerCode}&iframe=1`;
                $('#materialAddIframe').attr('src', iframeUrl);
            }
        }
    });

    // 保存物料按钮点击事件（用于创建新物料）
    $(document).on('click', '#saveMaterialBtn', function() {
        const iframe = document.getElementById('materialAddIframe');
        if (iframe && iframe.contentWindow) {
            try {
                // 显示保存遮罩层
                showSavingOverlay();

                // 设置保存超时
                setSaveTimeout();

                // 获取BOM基本信息
                const bomNum = $('#bomNum').val() || 1;
                const bomLossRate = $('#bomLossRate').val() || 0;
                const bomPartCount = $('#bomPartCount').val() || 1;
                const bomProduceCount = $('#bomProduceCount').val() || 1;
                const parentBomId = $('#bomNodeModal').data('parent-bom-id') || 0;

                // 发送保存命令到iframe
                const messageData = {
                    action: 'save',
                    bomId: parentBomId,
                    isAddMode: true,
                    sourceType: 'bom_list',
                    bomPid: parentBomId,
                    bomNum: bomNum,
                    bomLossRate: bomLossRate,
                    bomPartCount: bomPartCount,
                    bomProduceCount: bomProduceCount
                };

                console.log('发送保存命令到iframe:', messageData);
                iframe.contentWindow.postMessage(messageData, '*');

                // 启动iframe监听
                setTimeout(function() {
                    monitorIframeForSuccess();
                }, 1000);

            } catch (error) {
                console.error("发送消息失败:", error);
                clearSaveTimeout();
                hideSavingOverlay();
                alert("保存失败：无法与iframe通信");
            }
        } else {
            alert("请等待页面加载完成");
        }
    });

    // 初始化时绑定事件
    $(document).ready(function() {
        bindIframeMessageListener();
    });
</script>

<style>
    .table-hover-highlight {
        background-color: rgba(0, 123, 255, 0.1) !important;
    }
    .clickable-row {
        transition: background-color 0.2s;
    }
    
    /* Select2样式调整 */
    .select2-container--bootstrap-5 .select2-selection {
        height: calc(1.5em + 0.75rem + 2px);
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
    }
    
    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
        padding-left: 0;
        line-height: 1.5;
    }
    
    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
        top: 50%;
        transform: translateY(-50%);
    }
    
    .select2-dropdown {
        border-color: #ced4da;
    }
    
    .select2-search--dropdown .select2-search__field {
        border-color: #ced4da;
    }

    /* 模板选择卡片样式 */
    .template-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid #dee2e6;
    }

    .template-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
        transform: translateY(-2px);
    }

    .template-card.selected {
        border-color: #007bff;
        background-color: #f8f9ff;
    }
</style>
{% endblock %}
