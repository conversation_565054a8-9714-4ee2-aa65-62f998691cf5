import hashlib
import json
import os
import urllib.parse
import uuid
from django.shortcuts import render, redirect, get_object_or_404
from django.views import View
from django.contrib import messages
from django.contrib.auth import login, logout
from django.utils import timezone
from django.http import JsonResponse, HttpResponse
from django.db.models import F

def get_local_now():
    """获取本地时间（无时区信息）"""
    from datetime import datetime
    return datetime.now()

def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def log_system_action(request, action, module, target_id=None, target_name=None, description=None, details=None):
    """记录系统操作日志"""
    try:
        from .models import SystemLog

        # 获取用户信息
        user_id = request.session.get('user_id')
        user_name = request.session.get('username', '未知用户')

        # 获取IP地址
        ip_address = get_client_ip(request)

        # 创建日志记录
        log = SystemLog(
            log_user_id=user_id,
            log_user_name=user_name,
            log_action=action,
            log_module=module,
            log_target_id=target_id,
            log_target_name=target_name,
            log_description=description,
            log_ip=ip_address,
            log_time=get_local_now(),
            log_details=details
        )
        log.save()

        print(f"日志记录成功: {user_name} - {action} - {module} - {target_name}")

    except Exception as e:
        print(f"日志记录失败: {str(e)}")
        # 日志记录失败不应该影响主要业务流程
from django.views.decorators.csrf import csrf_exempt, csrf_protect
from django.utils.decorators import method_decorator
from django.urls import reverse
from django.conf import settings
from .models import User, Customer, Datadictionary, PropertiesType, ProcessRoute, Material, Config, Bom, Equipment, SystemLog
from django.db.models import Q
import re
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.views.generic import TemplateView
from .import_utils import parse_bom_excel, delete_temp_bom_data
from django.db import connection


def get_dictionary_items(datadictionary_tag):
    """
    根据标签获取数据字典子项
    
    参数:
        datadictionary_tag: 数据字典标签
        
    返回:
        包含子项数据的列表，每个子项包含id、name和code字段
    """
    try:
        # 首先获取根节点
        try:
            root = Datadictionary.objects.get(
                datadictionary_tag=datadictionary_tag,
                datadictionary_pid=0
            )
            
            # 获取根节点下的所有子项
            items = Datadictionary.objects.filter(
                datadictionary_pid=root.datadictionary_id,
            ).values('datadictionary_id', 'datadictionary_name', 'datadictionary_code')
        except Datadictionary.DoesNotExist:
            # 如果根节点不存在，返回空列表
            items = []
        
        # 转换为标准格式列表返回
        result_list = []
        for item in items:
            result_list.append({
                'id': item['datadictionary_id'],
                'name': item['datadictionary_name'],
                'code': item['datadictionary_code']
            })
        
        return result_list
    except Exception as e:
        print(f"获取数据字典子项出错: {str(e)}")
        return []


class LoginView(View):
    """登录视图类"""
    
    def get(self, request):
        """GET请求处理，返回登录页面"""
        # 如果已登录，直接跳转到主页
        if request.session.get('is_login', False):
            return redirect('dashboard')
        return render(request, 'bom_sys/login.html')
    
    def post(self, request):
        """POST请求处理，处理登录逻辑"""
        username = request.POST.get('username', '')
        password = request.POST.get('password', '')
        
        # 对密码进行MD5加密
        md5 = hashlib.md5()
        md5.update(password.encode())
        md5_password = md5.hexdigest()
        
        # 使用ORM查询用户
        try:
            user = User.objects.get(user_name=username, user_pass=md5_password)
            
            # 用户存在，保存登录状态到session
            request.session['is_login'] = True
            request.session['user_id'] = user.user_id
            request.session['username'] = user.user_name
            
            # 更新最后登录时间
            user.user_logintime = get_local_now()
            user.save()

            # 记录登录日志
            log_system_action(
                request=request,
                action='用户登录',
                module='系统管理',
                target_name=username,
                description=f'用户 {username} 登录系统'
            )

            messages.success(request, f'欢迎回来，{username}！')
            return redirect('dashboard')
        except User.DoesNotExist:
            # 用户名或密码错误
            messages.error(request, '用户名或密码错误')
            return render(request, 'bom_sys/login.html')


class DashboardView(View):
    """主页视图类"""
    
    def get(self, request):
        """GET请求处理，返回主页"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取用户信息
        username = request.session.get('username', '')
        user_id = request.session.get('user_id', 0)
        
        try:
            # 使用ORM获取用户信息
            user = User.objects.get(user_id=user_id)
            
            # 获取统计数据
            material_count = Material.objects.filter(material_tempstate=0).count()
            bom_count = Bom.objects.filter(bom_state=0).count()
            properties_type_count = PropertiesType.objects.count()
            
            # 添加临时BOM总数统计
            temp_bom_count = Bom.objects.filter(bom_state__gt=0).count()
            
            # 获取最近添加的物料（正式数据，非临时数据）
            recent_materials = Material.objects.filter(
                material_tempstate=0
            ).order_by('-material_uptime')[:5]
            
            # 获取最近更新的BOM
            # 先获取最近更新的物料对应的BOM
            recent_boms_data = []
            recent_bom_materials = Material.objects.filter(
                material_tempstate=0
            ).order_by('-material_uptime')[:10]
            
            for material in recent_bom_materials:
                try:
                    bom = Bom.objects.filter(bom_material=material.material_id, bom_state=0).first()
                    if not bom:
                        continue
                        
                    # 获取父级物料信息（如果有）
                    parent_material = None
                    if bom.bom_pid > 0:
                        try:
                            parent_bom = Bom.objects.get(bom_id=bom.bom_pid)
                            if parent_bom.bom_material:
                                parent_material = Material.objects.get(material_id=parent_bom.bom_material)
                        except (Bom.DoesNotExist, Material.DoesNotExist):
                            pass
                    
                    recent_boms_data.append({
                        'bom_id': bom.bom_id,
                        'material': material,
                        'parent_material': parent_material,
                        'update_time': material.material_uptime
                    })
                    
                    # 只保留5个最近的BOM
                    if len(recent_boms_data) >= 5:
                        break
                        
                except Bom.DoesNotExist:
                    continue
            
            context = {
                'username': user.user_name,
                'user_nick': user.user_nick,
                'user_role': user.user_role,
                'material_count': material_count,
                'bom_count': bom_count,
                'properties_type_count': properties_type_count,
                'temp_bom_count': temp_bom_count,
                'recent_materials': recent_materials,
                'recent_boms': recent_boms_data
            }
        except User.DoesNotExist:
            context = {
                'username': username,
                'material_count': 0,
                'bom_count': 0,
                'properties_type_count': 0,
                'temp_bom_count': 0,
                'recent_materials': [],
                'recent_boms': []
            }
        
        return render(request, 'bom_sys/dashboard.html', context)


class LogoutView(View):
    """登出视图类"""
    
    def get(self, request):
        """处理登出请求"""
        # 清除session
        logout(request)
        request.session.flush()
        
        messages.success(request, '您已成功退出登录')
        return redirect('login')


class UserListView(View):
    """用户列表视图"""
    
    def get(self, request):
        """显示用户列表"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取所有用户
        users = User.objects.all().order_by('user_id')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'users': users,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'users': users,
                'is_iframe': is_iframe,
            }
        
        return render(request, 'bom_sys/user_list.html', context)


class UserAddView(View):
    """添加用户视图"""
    
    def get(self, request):
        """显示添加用户表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
            }
        
        return render(request, 'bom_sys/user_add.html', context)
    
    def post(self, request):
        """处理添加用户表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取表单数据
        username = request.POST.get('username', '')
        password = request.POST.get('password', '')
        confirm_password = request.POST.get('confirm_password', '')
        nickname = request.POST.get('nickname', '')
        user_role = request.POST.get('user_role', '0')
        
        # 获取当前用户信息（用于错误时重新渲染页面）
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                # 保留表单数据
                'form_data': {
                    'username': username,
                    'nickname': nickname,
                    'user_role': user_role,
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                # 保留表单数据
                'form_data': {
                    'username': username,
                    'nickname': nickname,
                    'user_role': user_role,
                }
            }
        
        # 表单验证
        if not username:
            messages.error(request, '用户名不能为空')
            if is_iframe:
                return render(request, 'bom_sys/user_add.html', context)
            else:
                return redirect('user_add')
        
        if not password:
            messages.error(request, '密码不能为空')
            if is_iframe:
                return render(request, 'bom_sys/user_add.html', context)
            else:
                return redirect('user_add')
        
        if password != confirm_password:
            messages.error(request, '两次输入的密码不一致')
            if is_iframe:
                return render(request, 'bom_sys/user_add.html', context)
            else:
                return redirect('user_add')
        
        # 检查用户名是否已存在
        if User.objects.filter(user_name=username).exists():
            messages.error(request, '用户名已存在')
            if is_iframe:
                return render(request, 'bom_sys/user_add.html', context)
            else:
                return redirect('user_add')
        
        # 对密码进行MD5加密
        md5 = hashlib.md5()
        md5.update(password.encode())
        md5_password = md5.hexdigest()
        
        # 创建用户
        user = User(
            user_name=username,
            user_pass=md5_password,
            user_nick=nickname,
            user_role=user_role,
            user_logintime=get_local_now()
        )
        user.save()
        
        messages.success(request, f'用户 {username} 创建成功')
        
        # 检查是否为iframe请求
        if is_iframe:
            # 返回带有刷新父页面脚本的响应
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'user_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('user_list')


class UserEditView(View):
    """编辑用户视图"""
    
    def get(self, request, user_id):
        """显示编辑用户表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要编辑的用户
        user = get_object_or_404(User, user_id=user_id)
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        current_user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=current_user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'edit_user': user,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'edit_user': user,
                'is_iframe': is_iframe,
            }
        
        return render(request, 'bom_sys/user_edit.html', context)
    
    def post(self, request, user_id):
        """处理编辑用户表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取要编辑的用户
        user = get_object_or_404(User, user_id=user_id)
        
        # 获取表单数据
        nickname = request.POST.get('nickname', '')
        password = request.POST.get('password', '')
        confirm_password = request.POST.get('confirm_password', '')
        user_role = request.POST.get('user_role', '0')
        
        # 获取当前用户信息（用于错误时重新渲染页面）
        current_user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=current_user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'edit_user': user,
                'is_iframe': is_iframe,
                # 保留表单数据
                'form_data': {
                    'nickname': nickname,
                    'user_role': user_role,
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'edit_user': user,
                'is_iframe': is_iframe,
                # 保留表单数据
                'form_data': {
                    'nickname': nickname,
                    'user_role': user_role,
                }
            }
        
        # 表单验证
        if password and password != confirm_password:
            messages.error(request, '两次输入的密码不一致')
            if is_iframe:
                return render(request, 'bom_sys/user_edit.html', context)
            else:
                return redirect('user_edit', user_id=user_id)
        
        # 更新用户信息
        user.user_nick = nickname
        user.user_role = user_role
        
        # 如果提供了密码，则更新密码
        if password:
            # 对密码进行MD5加密
            md5 = hashlib.md5()
            md5.update(password.encode())
            md5_password = md5.hexdigest()
            user.user_pass = md5_password
        
        # 保存更改
        user.save()
        
        messages.success(request, f'用户 {user.user_name} 信息已更新')
        
        # 检查是否为iframe请求
        if is_iframe:
            # 返回带有刷新父页面脚本的响应
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'user_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('user_list')


class UserDeleteView(View):
    """删除用户视图"""
    
    def get(self, request, user_id):
        """处理删除用户请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 防止用户删除自己
        current_user_id = request.session.get('user_id', 0)
        if int(user_id) == int(current_user_id):
            messages.error(request, '不能删除当前登录的用户')
            if is_iframe:
                return render(request, 'bom_sys/iframe_redirect.html', {
                    'view_name': 'user_list',
                    'query_string': '?iframe=1'
                })
            else:
                return redirect('user_list')
        
        # 获取要删除的用户
        user = get_object_or_404(User, user_id=user_id)
        
        # 删除用户
        user.delete()
        
        messages.success(request, f'用户 {user.user_name} 已删除')
        
        # 检查是否为iframe请求
        if is_iframe:
            # 返回带有刷新父页面脚本的响应
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'user_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('user_list')


@method_decorator(csrf_exempt, name='dispatch')
class ProfileView(View):
    """用户个人资料视图"""
    
    def get(self, request):
        """返回用户个人资料信息"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return JsonResponse({'success': False, 'error': '未登录'})
        
        # 获取当前用户
        user_id = request.session.get('user_id', 0)
        try:
            user = User.objects.get(user_id=user_id)
            return JsonResponse({
                'success': True,
                'user_nick': user.user_nick or '',
            })
        except User.DoesNotExist:
            return JsonResponse({'success': False, 'error': '用户不存在'})
    
    def post(self, request):
        """处理用户个人资料更新"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return JsonResponse({'success': False, 'error': '未登录'})
        
        # 获取表单数据
        try:
            # 尝试从POST中获取数据
            print("接收到的POST数据:", request.POST)
            user_nick = request.POST.get('user_nick', '')
            new_password = request.POST.get('new_password', '')
            
            # 获取当前用户
            user_id = request.session.get('user_id', 0)
            user = User.objects.get(user_id=user_id)
            
            # 更新昵称
            user.user_nick = user_nick
            
            # 如果提供了新密码，则更新密码
            if new_password:
                # 对密码进行MD5加密
                md5 = hashlib.md5()
                md5.update(new_password.encode())
                md5_password = md5.hexdigest()
                user.user_pass = md5_password
            
            # 保存更改
            user.save()
            print(f"用户数据已更新: user_id={user_id}, user_nick={user_nick}")
            
            # 更新session中的用户信息
            request.session['user_nick'] = user_nick
            
            return JsonResponse({'success': True})
        except Exception as e:
            print(f"更新个人资料错误: {str(e)}")
            return JsonResponse({'success': False, 'error': str(e)})


# 基础数据管理相关视图

class DataDictionaryListView(View):
    """数据字典列表视图"""
    
    def get(self, request):
        """显示数据字典列表"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取所有数据字典
        dictionaries = Datadictionary.objects.all().order_by('datadictionary_order', 'datadictionary_id')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'dictionaries': dictionaries,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'dictionaries': dictionaries,
                'is_iframe': is_iframe,
            }
        
        return render(request, 'bom_sys/data_dictionary_list.html', context)


class CustomerListView(View):
    """客户信息列表视图"""
    
    def get(self, request):
        """显示客户信息列表"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取所有客户信息
        customers = Customer.objects.all().order_by('customer_order', 'customer_id')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'customers': customers,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'customers': customers,
                'is_iframe': is_iframe,
            }
        
        return render(request, 'bom_sys/customer_list.html', context)



class DataDictionaryAddView(View):
    """添加数据字典视图"""
    
    def get(self, request):
        """显示添加数据字典表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有父级字典项，用于下拉选择
        parent_dicts = Datadictionary.objects.filter(datadictionary_pid=0).order_by('datadictionary_order')
        
        # 获取父级ID（如果从批量编辑页面跳转过来）
        parent_id = request.GET.get('parent_id', '0')
        
        # 获取最大排序号+1作为默认值
        # 如果有父级ID，则获取该父级下的最大排序号
        if parent_id and parent_id != '0':
            max_order = Datadictionary.objects.filter(datadictionary_pid=parent_id).order_by('-datadictionary_order').first()
        else:
            max_order = Datadictionary.objects.filter(datadictionary_pid=0).order_by('-datadictionary_order').first()
        
        default_order = 1
        if max_order:
            default_order = max_order.datadictionary_order + 1
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'parent_dicts': parent_dicts,
                'parent_id': parent_id,
                'default_order': default_order,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'parent_dicts': parent_dicts,
                'parent_id': parent_id,
                'default_order': default_order,
            }
        
        return render(request, 'bom_sys/data_dictionary_add.html', context)
    
    def post(self, request):
        """处理添加数据字典表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取表单数据
        dict_name = request.POST.get('dict_name', '')
        dict_code = request.POST.get('dict_code', '')
        dict_tag = request.POST.get('dict_tag', '')
        dict_pid = request.POST.get('dict_pid', '0')
        dict_order = request.POST.get('dict_order', '0')
        
        # 获取所有父级字典项，用于下拉选择
        parent_dicts = Datadictionary.objects.filter(datadictionary_pid=0).order_by('datadictionary_order')
        
        # 获取当前用户信息（用于错误时重新渲染页面）
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'parent_dicts': parent_dicts,
                # 保留表单数据
                'form_data': {
                    'dict_name': dict_name,
                    'dict_code': dict_code,
                    'dict_tag': dict_tag,
                    'dict_pid': dict_pid,
                    'dict_order': dict_order,
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'parent_dicts': parent_dicts,
                # 保留表单数据
                'form_data': {
                    'dict_name': dict_name,
                    'dict_code': dict_code,
                    'dict_tag': dict_tag,
                    'dict_pid': dict_pid,
                    'dict_order': dict_order,
                }
            }
        
        # 表单验证
        if not dict_name:
            messages.error(request, '字典名称不能为空')
            if is_iframe:
                return render(request, 'bom_sys/data_dictionary_add.html', context)
            else:
                return redirect('data_dictionary_add')
        
        if not dict_code:
            messages.error(request, '字典代码不能为空')
            if is_iframe:
                return render(request, 'bom_sys/data_dictionary_add.html', context)
            else:
                return redirect('data_dictionary_add')
        
        # 创建数据字典项
        dict_item = Datadictionary(
            datadictionary_name=dict_name,
            datadictionary_code=dict_code,
            datadictionary_tag=dict_tag,
            datadictionary_pid=dict_pid,
            datadictionary_order=dict_order
        )
        dict_item.save()
        
        messages.success(request, '数据字典项添加成功')
        
        # 如果是在iframe中，使用iframe重定向
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'data_dictionary_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('data_dictionary_list')


class CustomerAddView(View):
    """添加客户视图"""
    
    def get(self, request):
        """显示添加客户表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取最大排序号+1作为默认值
        max_order = Customer.objects.all().order_by('-customer_order').first()
        default_order = 1
        if max_order:
            default_order = max_order.customer_order + 1
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'default_order': default_order,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'default_order': default_order,
            }
        
        return render(request, 'bom_sys/customer_add.html', context)
    
    def post(self, request):
        """处理添加客户表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取表单数据
        customer_name = request.POST.get('customer_name', '')
        customer_code = request.POST.get('customer_code', '')
        customer_order = request.POST.get('customer_order', '0')
        
        # 获取当前用户信息（用于错误时重新渲染页面）
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                # 保留表单数据
                'form_data': {
                    'customer_name': customer_name,
                    'customer_code': customer_code,
                    'customer_order': customer_order,
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                # 保留表单数据
                'form_data': {
                    'customer_name': customer_name,
                    'customer_code': customer_code,
                    'customer_order': customer_order,
                }
            }
        
        # 表单验证
        if not customer_name:
            messages.error(request, '客户名称不能为空')
            if is_iframe:
                return render(request, 'bom_sys/customer_add.html', context)
            else:
                return redirect('customer_add')
        
        if not customer_code:
            messages.error(request, '客户代码不能为空')
            if is_iframe:
                return render(request, 'bom_sys/customer_add.html', context)
            else:
                return redirect('customer_add')
        
        # 检查代码是否已存在
        if Customer.objects.filter(customer_code=customer_code).exists():
            messages.error(request, '客户代码已存在')
            if is_iframe:
                return render(request, 'bom_sys/customer_add.html', context)
            else:
                return redirect('customer_add')
        
        # 创建客户
        customer = Customer(
            customer_name=customer_name,
            customer_code=customer_code,
            customer_order=customer_order,
            customer_addtime=get_local_now()
        )
        customer.save()
        
        messages.success(request, '客户添加成功')
        
        # 如果是在iframe中，使用iframe重定向
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'customer_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('customer_list')


class DataDictionaryEditView(View):
    """编辑数据字典视图"""
    
    def get(self, request, dict_id):
        """显示编辑数据字典表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要编辑的数据字典项
        try:
            dict_item = Datadictionary.objects.get(datadictionary_id=dict_id)
        except Datadictionary.DoesNotExist:
            messages.error(request, '数据字典项不存在')
            return redirect('data_dictionary_list')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有父级字典项，用于下拉选择
        parent_dicts = Datadictionary.objects.filter(datadictionary_pid=0).exclude(datadictionary_id=dict_id).order_by('datadictionary_order')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'parent_dicts': parent_dicts,
                'dict_item': dict_item,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'parent_dicts': parent_dicts,
                'dict_item': dict_item,
            }
        
        return render(request, 'bom_sys/data_dictionary_edit.html', context)
    
    def post(self, request, dict_id):
        """处理编辑数据字典表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要编辑的数据字典项
        try:
            dict_item = Datadictionary.objects.get(datadictionary_id=dict_id)
        except Datadictionary.DoesNotExist:
            messages.error(request, '数据字典项不存在')
            return redirect('data_dictionary_list')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取表单数据
        dict_name = request.POST.get('dict_name', '')
        dict_code = request.POST.get('dict_code', '')
        dict_tag = request.POST.get('dict_tag', '')
        dict_pid = request.POST.get('dict_pid', '0')
        dict_order = request.POST.get('dict_order', '0')
        
        # 获取所有父级字典项，用于下拉选择
        parent_dicts = Datadictionary.objects.filter(datadictionary_pid=0).exclude(datadictionary_id=dict_id).order_by('datadictionary_order')
        
        # 获取当前用户信息（用于错误时重新渲染页面）
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'parent_dicts': parent_dicts,
                'dict_item': dict_item,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'parent_dicts': parent_dicts,
                'dict_item': dict_item,
            }
        
        # 表单验证
        if not dict_name:
            messages.error(request, '字典名称不能为空')
            if is_iframe:
                return render(request, 'bom_sys/data_dictionary_edit.html', context)
            else:
                return redirect('data_dictionary_edit', dict_id=dict_id)
        
        if not dict_code:
            messages.error(request, '字典代码不能为空')
            if is_iframe:
                return render(request, 'bom_sys/data_dictionary_edit.html', context)
            else:
                return redirect('data_dictionary_edit', dict_id=dict_id)
        
        # 更新数据字典项
        dict_item.datadictionary_name = dict_name
        dict_item.datadictionary_code = dict_code
        dict_item.datadictionary_tag = dict_tag
        dict_item.datadictionary_pid = dict_pid
        dict_item.datadictionary_order = dict_order
        dict_item.save()
        
        messages.success(request, '数据字典项更新成功')
        
        # 如果是在iframe中，使用iframe重定向
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'data_dictionary_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('data_dictionary_list')


class DataDictionaryDeleteView(View):
    """删除数据字典视图"""
    
    def get(self, request, dict_id):
        """处理删除数据字典请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要删除的数据字典项
        try:
            dict_item = Datadictionary.objects.get(datadictionary_id=dict_id)
        except Datadictionary.DoesNotExist:
            messages.error(request, '数据字典项不存在')
            return redirect('data_dictionary_list')
        
        # 检查是否有子项
        if Datadictionary.objects.filter(datadictionary_pid=dict_id).exists():
            messages.error(request, '该数据字典项有子项，无法删除')
            
            # 检查是否为iframe请求
            is_iframe = request.GET.get('iframe', '0') == '1'
            if is_iframe:
                return render(request, 'bom_sys/iframe_redirect.html', {
                    'view_name': 'data_dictionary_list',
                    'query_string': '?iframe=1'
                })
            else:
                return redirect('data_dictionary_list')
        
        # 删除数据字典项
        dict_item.delete()
        
        messages.success(request, '数据字典项删除成功')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'data_dictionary_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('data_dictionary_list')


class CustomerEditView(View):
    """编辑客户视图"""
    
    def get(self, request, customer_id):
        """显示编辑客户表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要编辑的客户
        try:
            customer = Customer.objects.get(customer_id=customer_id)
        except Customer.DoesNotExist:
            messages.error(request, '客户不存在')
            return redirect('customer_list')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'customer': customer,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'customer': customer,
            }
        
        return render(request, 'bom_sys/customer_edit.html', context)
    
    def post(self, request, customer_id):
        """处理编辑客户表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要编辑的客户
        try:
            customer = Customer.objects.get(customer_id=customer_id)
        except Customer.DoesNotExist:
            messages.error(request, '客户不存在')
            return redirect('customer_list')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取表单数据
        customer_name = request.POST.get('customer_name', '')
        customer_code = request.POST.get('customer_code', '')
        customer_order = request.POST.get('customer_order', '0')
        
        # 获取当前用户信息（用于错误时重新渲染页面）
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'customer': customer,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'customer': customer,
            }
        
        # 表单验证
        if not customer_name:
            messages.error(request, '客户名称不能为空')
            if is_iframe:
                return render(request, 'bom_sys/customer_edit.html', context)
            else:
                return redirect('customer_edit', customer_id=customer_id)
        
        if not customer_code:
            messages.error(request, '客户代码不能为空')
            if is_iframe:
                return render(request, 'bom_sys/customer_edit.html', context)
            else:
                return redirect('customer_edit', customer_id=customer_id)
        
        # 检查代码是否已存在（排除自身）
        if Customer.objects.filter(customer_code=customer_code).exclude(customer_id=customer_id).exists():
            messages.error(request, '客户代码已存在')
            if is_iframe:
                return render(request, 'bom_sys/customer_edit.html', context)
            else:
                return redirect('customer_edit', customer_id=customer_id)
        
        # 更新客户
        customer.customer_name = customer_name
        customer.customer_code = customer_code
        customer.customer_order = customer_order
        customer.save()
        
        messages.success(request, '客户更新成功')
        
        # 如果是在iframe中，使用iframe重定向
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'customer_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('customer_list')


class CustomerDeleteView(View):
    """删除客户视图"""
    
    def get(self, request, customer_id):
        """处理删除客户请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要删除的客户
        try:
            customer = Customer.objects.get(customer_id=customer_id)
        except Customer.DoesNotExist:
            messages.error(request, '客户不存在')
            return redirect('customer_list')
        
        # 删除客户
        customer.delete()
        
        messages.success(request, '客户删除成功')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'customer_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('customer_list')


class DataDictionaryTreeView(View):
    """数据字典树形视图"""
    
    def get(self, request):
        """显示数据字典树形结构"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有数据字典项
        all_dicts = Datadictionary.objects.all().order_by('datadictionary_pid', 'datadictionary_order')
        
        # 构建树形结构数据
        root_dicts = []
        dict_map = {}
        
        # 先将所有项放入字典中
        for item in all_dicts:
            dict_map[item.datadictionary_id] = {
                'id': item.datadictionary_id,
                'name': item.datadictionary_name,
                'code': item.datadictionary_code,
                'tag': item.datadictionary_tag,
                'pid': item.datadictionary_pid,
                'order': item.datadictionary_order,
                'children': []
            }
        
        # 构建树形结构
        for item in all_dicts:
            if item.datadictionary_pid == 0:
                # 根节点
                root_dicts.append(dict_map[item.datadictionary_id])
            else:
                # 子节点
                if item.datadictionary_pid in dict_map:
                    dict_map[item.datadictionary_pid]['children'].append(dict_map[item.datadictionary_id])
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'root_dicts': root_dicts,
                'all_dicts': all_dicts,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'root_dicts': root_dicts,
                'all_dicts': all_dicts,
            }
        
        return render(request, 'bom_sys/data_dictionary_tree.html', context)


class DataDictionaryBatchEditView(View):
    """数据字典批量编辑视图"""
    
    def get(self, request):
        """显示数据字典批量编辑表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取父级ID
        parent_id = request.GET.get('parent_id', '0')
        
        # 获取当前父级下的所有字典项
        if parent_id == '0':
            dict_items = Datadictionary.objects.filter(datadictionary_pid=0).order_by('datadictionary_order')
            parent_name = "根级别"
        else:
            dict_items = Datadictionary.objects.filter(datadictionary_pid=parent_id).order_by('datadictionary_order')
            try:
                parent = Datadictionary.objects.get(datadictionary_id=parent_id)
                parent_name = parent.datadictionary_name
            except Datadictionary.DoesNotExist:
                parent_name = "未知"
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'dict_items': dict_items,
                'parent_id': parent_id,
                'parent_name': parent_name,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'dict_items': dict_items,
                'parent_id': parent_id,
                'parent_name': parent_name,
            }
        
        return render(request, 'bom_sys/data_dictionary_batch_edit.html', context)
    
    def post(self, request):
        """处理数据字典批量编辑表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取父级ID
        parent_id = request.POST.get('parent_id', '0')
        
        # 获取表单数据
        dict_ids = request.POST.getlist('dict_id')
        dict_names = request.POST.getlist('dict_name')
        dict_codes = request.POST.getlist('dict_code')
        dict_tags = request.POST.getlist('dict_tag')
        dict_orders = request.POST.getlist('dict_order')
        
        # 新增项的数据
        new_names = request.POST.getlist('new_name')
        new_codes = request.POST.getlist('new_code')
        new_tags = request.POST.getlist('new_tag')
        new_orders = request.POST.getlist('new_order')
        
        # 更新现有项
        for i in range(len(dict_ids)):
            try:
                dict_item = Datadictionary.objects.get(datadictionary_id=dict_ids[i])
                dict_item.datadictionary_name = dict_names[i]
                dict_item.datadictionary_code = dict_codes[i]
                dict_item.datadictionary_tag = dict_tags[i]
                dict_item.datadictionary_order = dict_orders[i]
                dict_item.save()
            except Datadictionary.DoesNotExist:
                continue
        
        # 添加新项
        for i in range(len(new_names)):
            if new_names[i] and new_codes[i]:  # 只有名称和代码都不为空时才添加
                dict_item = Datadictionary(
                    datadictionary_name=new_names[i],
                    datadictionary_code=new_codes[i],
                    datadictionary_tag=new_tags[i],
                    datadictionary_pid=parent_id,
                    datadictionary_order=new_orders[i] or 0
                )
                dict_item.save()
        
        messages.success(request, '数据字典批量更新成功')
        
        # 如果是在iframe中，使用iframe重定向
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'data_dictionary_tree',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('data_dictionary_tree')


class ProcessRouteListView(View):
    """工艺路线列表视图"""
    
    def get(self, request):
        """显示工艺路线列表"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取所有工艺路线
        process_routes = ProcessRoute.objects.all().order_by('processroute_order', 'processroute_id')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'process_routes': process_routes,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'process_routes': process_routes,
                'is_iframe': is_iframe,
            }
        
        return render(request, 'bom_sys/process_route_list.html', context)


class ProcessRouteAddView(View):
    """添加工艺路线视图"""
    
    def get(self, request):
        """显示添加工艺路线表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 处理AJAX请求获取设备数据
        if request.GET.get('get_equipment', '') == '1':
            try:
                from .models import Equipment
                equipment_list = []
                for equipment in Equipment.objects.all().order_by('equipment_no'):
                    equipment_list.append({
                        'id': str(equipment.equipment_id),
                        'no': equipment.equipment_no or '',
                        'name': equipment.equipment_name or '',
                    })

                return JsonResponse({
                    'success': True,
                    'equipment': equipment_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })

        # 处理AJAX请求获取工艺数据（保持向后兼容）
        if request.GET.get('get_processes', '') == '1':
            try:
                # 使用通用函数获取工艺数据
                process_list = get_dictionary_items('processes')

                return JsonResponse({
                    'success': True,
                    'processes': process_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })

        # 处理AJAX请求获取工序数据（用于Select2）
        if request.GET.get('search_processes', '') == '1':
            search_term = request.GET.get('q', '').strip()
            try:
                # 使用通用函数获取工序数据
                process_list = get_dictionary_items('processes')

                # 如果有搜索词，进行过滤
                if search_term:
                    filtered_processes = []
                    for process in process_list:
                        if search_term.lower() in process['name'].lower():
                            filtered_processes.append(process)
                    process_list = filtered_processes

                return JsonResponse({
                    'success': True,
                    'processes': process_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取最大排序号+1作为默认值
        max_order = ProcessRoute.objects.all().order_by('-processroute_order').first()
        default_order = 1
        if max_order:
            default_order = max_order.processroute_order + 1
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'default_order': default_order,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'default_order': default_order,
            }
        
        return render(request, 'bom_sys/process_route_add.html', context)
    
    def post(self, request):
        """处理添加工艺路线表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取表单数据
        route_no = request.POST.get('route_no', '')
        route_name = request.POST.get('route_name', '')
        route_content = request.POST.get('route_content', '')
        route_times = request.POST.get('route_times', '')
        route_device = request.POST.get('route_device', '')
        route_price = request.POST.get('route_price', '')
        route_type = request.POST.get('route_type', '')
        route_outsupplier = request.POST.get('route_outsupplier', '')
        route_outprice = request.POST.get('route_outprice', '')
        route_important = request.POST.get('route_important', '')
        route_code = request.POST.get('route_code', '')
        route_order = request.POST.get('route_order', '0')
        
        # 获取最大排序号+1作为默认值（添加到上下文中，防止表单验证失败时出错）
        max_order = ProcessRoute.objects.all().order_by('-processroute_order').first()
        default_order = 1
        if max_order:
            default_order = max_order.processroute_order + 1
        
        # 获取当前用户信息（用于错误时重新渲染页面）
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'default_order': default_order,  # 添加默认排序号
                # 保留表单数据
                'form_data': {
                    'route_no': route_no,
                    'route_name': route_name,
                    'route_content': route_content,
                    'route_order': route_order,
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'default_order': default_order,  # 添加默认排序号
                # 保留表单数据
                'form_data': {
                    'route_no': route_no,
                    'route_name': route_name,
                    'route_content': route_content,
                    'route_order': route_order,
                }
            }
        
        # 表单验证
        if not route_no:
            messages.error(request, '工艺路线编号不能为空')
            return render(request, 'bom_sys/process_route_add.html', context)
        
        if not route_name:
            messages.error(request, '工艺路线名称不能为空')
            return render(request, 'bom_sys/process_route_add.html', context)
        
        # 检查编号是否已存在
        if ProcessRoute.objects.filter(processroute_no=route_no).exists():
            messages.error(request, f'工艺路线编号 "{route_no}" 已存在，请使用其他编号')
            return render(request, 'bom_sys/process_route_add.html', context)
        
        # 创建工艺路线
        process_route = ProcessRoute(
            processroute_no=route_no,
            processroute_name=route_name,
            processroute_content=route_content,
            processroute_times=route_times,
            processroute_device=route_device,
            processroute_price=route_price,
            processroute_type=route_type,
            processroute_outsupplier=route_outsupplier,
            processroute_outprice=route_outprice,
            processroute_important=route_important,
            processroute_code=route_code,
            processroute_order=route_order
        )
        process_route.save()
        
        messages.success(request, '工艺路线添加成功')
        
        # 如果是在iframe中，使用iframe重定向
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'process_route_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('process_route_list')


class ProcessRouteEditView(View):
    """编辑工艺路线视图"""
    
    def get(self, request, route_id):
        """显示编辑工艺路线表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 处理AJAX请求获取设备数据
        if request.GET.get('get_equipment', '') == '1':
            try:
                from .models import Equipment
                equipment_list = []
                for equipment in Equipment.objects.all().order_by('equipment_no'):
                    equipment_list.append({
                        'id': str(equipment.equipment_id),
                        'no': equipment.equipment_no or '',
                        'name': equipment.equipment_name or '',
                    })

                return JsonResponse({
                    'success': True,
                    'equipment': equipment_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })

        # 处理AJAX请求获取工艺数据（保持向后兼容）
        if request.GET.get('get_processes', '') == '1':
            try:
                # 使用通用函数获取工艺数据
                process_list = get_dictionary_items('processes')

                return JsonResponse({
                    'success': True,
                    'processes': process_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })

        # 处理AJAX请求获取工序数据（用于Select2）
        if request.GET.get('search_processes', '') == '1':
            search_term = request.GET.get('q', '').strip()
            try:
                # 使用通用函数获取工序数据
                process_list = get_dictionary_items('processes')

                # 如果有搜索词，进行过滤
                if search_term:
                    filtered_processes = []
                    for process in process_list:
                        if search_term.lower() in process['name'].lower():
                            filtered_processes.append(process)
                    process_list = filtered_processes

                return JsonResponse({
                    'success': True,
                    'processes': process_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })
        
        # 获取要编辑的工艺路线
        try:
            process_route = ProcessRoute.objects.get(processroute_id=route_id)
        except ProcessRoute.DoesNotExist:
            messages.error(request, '工艺路线不存在')
            return redirect('process_route_list')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'process_route': process_route,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'process_route': process_route,
            }
        
        return render(request, 'bom_sys/process_route_edit.html', context)
    
    def post(self, request, route_id):
        """处理编辑工艺路线表单提交"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要编辑的工艺路线
        try:
            process_route = ProcessRoute.objects.get(processroute_id=route_id)
        except ProcessRoute.DoesNotExist:
            messages.error(request, '工艺路线不存在')
            return redirect('process_route_list')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取表单数据
        route_no = request.POST.get('route_no', '')
        route_name = request.POST.get('route_name', '')
        route_content = request.POST.get('route_content', '')
        route_times = request.POST.get('route_times', '')
        route_device = request.POST.get('route_device', '')
        route_price = request.POST.get('route_price', '')
        route_type = request.POST.get('route_type', '')
        route_outsupplier = request.POST.get('route_outsupplier', '')
        route_outprice = request.POST.get('route_outprice', '')
        route_important = request.POST.get('route_important', '')
        route_code = request.POST.get('route_code', '')
        route_order = request.POST.get('route_order', '0')
        
        # 获取当前用户信息（用于错误时重新渲染页面）
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'process_route': process_route,
                # 保留表单数据
                'form_data': {
                    'route_no': route_no,
                    'route_name': route_name,
                    'route_content': route_content,
                    'route_order': route_order,
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'process_route': process_route,
                # 保留表单数据
                'form_data': {
                    'route_no': route_no,
                    'route_name': route_name,
                    'route_content': route_content,
                    'route_order': route_order,
                }
            }
        
        # 表单验证
        if not route_no:
            messages.error(request, '工艺路线编号不能为空')
            return render(request, 'bom_sys/process_route_edit.html', context)
        
        if not route_name:
            messages.error(request, '工艺路线名称不能为空')
            return render(request, 'bom_sys/process_route_edit.html', context)
        
        # 检查编号是否已存在（排除自身）
        if ProcessRoute.objects.filter(processroute_no=route_no).exclude(processroute_id=route_id).exists():
            messages.error(request, f'工艺路线编号 "{route_no}" 已存在，请使用其他编号')
            return render(request, 'bom_sys/process_route_edit.html', context)
        
        # 更新工艺路线
        process_route.processroute_no = route_no
        process_route.processroute_name = route_name
        process_route.processroute_content = route_content
        process_route.processroute_times = route_times
        process_route.processroute_device = route_device
        process_route.processroute_price = route_price
        process_route.processroute_type = route_type
        process_route.processroute_outsupplier = route_outsupplier
        process_route.processroute_outprice = route_outprice
        process_route.processroute_important = route_important
        process_route.processroute_code = route_code
        process_route.processroute_order = route_order
        process_route.save()
        
        messages.success(request, '工艺路线更新成功')
        
        # 如果是在iframe中，使用iframe重定向
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'process_route_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('process_route_list')


class ProcessRouteDeleteView(View):
    """删除工艺路线视图"""
    
    def get(self, request, route_id):
        """处理删除工艺路线请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要删除的工艺路线
        try:
            process_route = ProcessRoute.objects.get(processroute_id=route_id)
        except ProcessRoute.DoesNotExist:
            messages.error(request, '工艺路线不存在')
            return redirect('process_route_list')
        
        # 删除工艺路线
        process_route.delete()
        
        messages.success(request, '工艺路线删除成功')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'process_route_list',
                'query_string': '?iframe=1'
            })
        else:
            return redirect('process_route_list')


class MaterialAddView(View):
    """单物料录入视图类，同时处理物料添加和编辑功能"""
    
    def get(self, request, material_id=None):
        """显示单物料录入/编辑表单"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
            
        # 优先从URL参数中获取edit_id
        edit_id = request.GET.get('edit_id')
        
        # 如果URL中没有edit_id，再使用路径中的material_id
        if not edit_id and material_id:
            edit_id = material_id
        # 如果有edit_id，将其转换为整数用于后续处理
        material_id = int(edit_id) if edit_id else None
        
        # 检查是否为预览模式
        is_preview = request.GET.get('preview', '0') == '1'
        
        # 获取临时状态
        temp_state = request.GET.get('temp_state', '0')
        # 获取BOM状态
        bom_state = request.GET.get('bom_state', '0')
        
        # 处理AJAX请求获取工艺数据
        if request.GET.get('get_processes', '') == '1':
            try:
                # 使用通用函数获取工艺数据
                process_list = get_dictionary_items('processes')

                return JsonResponse({
                    'success': True,
                    'processes': process_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })

        # 处理AJAX请求获取工序数据（用于Select2）
        if request.GET.get('search_processes', '') == '1':
            search_term = request.GET.get('q', '').strip()
            try:
                # 使用通用函数获取工序数据
                process_list = get_dictionary_items('processes')

                # 如果有搜索词，进行过滤
                if search_term:
                    filtered_processes = []
                    for process in process_list:
                        if search_term.lower() in process['name'].lower():
                            filtered_processes.append(process)
                    process_list = filtered_processes

                return JsonResponse({
                    'success': True,
                    'processes': process_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })
        
        # 处理AJAX请求获取设备数据
        if request.GET.get('get_equipment', '') == '1':
            try:
                from .models import Equipment
                equipment_list = []
                for equipment in Equipment.objects.all().order_by('equipment_no'):
                    equipment_list.append({
                        'id': str(equipment.equipment_id),
                        'no': equipment.equipment_no or '',
                        'name': equipment.equipment_name or '',
                    })

                return JsonResponse({
                    'success': True,
                    'equipment': equipment_list
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })

        # 处理AJAX请求搜索工艺路线（用于Select2）
        if request.GET.get('search_process_route', '') == '1':
            search_term = request.GET.get('q', '').strip()
            page = int(request.GET.get('page', 1))
            page_size = 20  # 每页显示20条记录

            try:
                # 构建查询条件
                if search_term:
                    # 模糊搜索工艺路线名称
                    process_routes = ProcessRoute.objects.filter(
                        processroute_name__icontains=search_term
                    ).order_by('processroute_name')
                else:
                    # 如果没有搜索词，返回所有工艺路线
                    process_routes = ProcessRoute.objects.all().order_by('processroute_name')

                # 分页处理
                start = (page - 1) * page_size
                end = start + page_size
                total_count = process_routes.count()
                page_routes = process_routes[start:end]

                # 构建返回数据
                routes_data = []
                for route in page_routes:
                    routes_data.append({
                        'id': route.processroute_id,
                        'no': route.processroute_no or '',
                        'name': route.processroute_name or '',
                    })

                return JsonResponse({
                    'success': True,
                    'process_routes': routes_data,
                    'has_more': end < total_count
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                })

        # 处理AJAX请求获取工艺路线
        if request.GET.get('get_process_route', '') == '1':
            route_no = request.GET.get('route_no', '')
            route_name = request.GET.get('route_name', '')

            if route_no:
                try:
                    # 通过编号查询工艺路线
                    process_route = ProcessRoute.objects.get(processroute_no=route_no)
                    return JsonResponse({
                        'success': True,
                        'process_route': {
                            'id': process_route.processroute_id,
                            'no': process_route.processroute_no,
                            'name': process_route.processroute_name,
                            'content': process_route.processroute_content or '',
                            'times': process_route.processroute_times or '',
                            'device': process_route.processroute_device or '',
                            'price': process_route.processroute_price or '',
                            'type': process_route.processroute_type or '',
                            'outsupplier': process_route.processroute_outsupplier or '',
                            'outprice': process_route.processroute_outprice or '',
                            'important': process_route.processroute_important or ''
                        }
                    })
                except ProcessRoute.DoesNotExist:
                    return JsonResponse({
                        'success': False,
                        'error': f'未找到编号为 {route_no} 的工艺路线'
                    })
                except Exception as e:
                    return JsonResponse({
                        'success': False,
                        'error': str(e)
                    })
            elif route_name:
                try:
                    # 通过名称查询工艺路线
                    process_route = ProcessRoute.objects.get(processroute_name=route_name)
                    return JsonResponse({
                        'success': True,
                        'process_route': {
                            'id': process_route.processroute_id,
                            'no': process_route.processroute_no,
                            'name': process_route.processroute_name,
                            'content': process_route.processroute_content or '',
                            'times': process_route.processroute_times or '',
                            'device': process_route.processroute_device or '',
                            'price': process_route.processroute_price or '',
                            'type': process_route.processroute_type or '',
                            'outsupplier': process_route.processroute_outsupplier or '',
                            'outprice': process_route.processroute_outprice or '',
                            'important': process_route.processroute_important or ''
                        }
                    })
                except ProcessRoute.DoesNotExist:
                    return JsonResponse({
                        'success': False,
                        'error': f'未找到名称为 {route_name} 的工艺路线'
                    })
                except Exception as e:
                    return JsonResponse({
                        'success': False,
                        'error': str(e)
                    })
            else:
                return JsonResponse({
                    'success': False,
                    'error': '请提供工艺路线编号或名称'
                })
        
        # 检查是否为ajax请求，用于通过图号查询物料
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            drawing_number = request.GET.get('drawing_number', '')
            customer_name = request.GET.get('customer_name', '')
            
            if drawing_number and customer_name:
                try:
                    # 先查找客户信息
                    try:
                        customer = Customer.objects.get(customer_name=customer_name)
                        customer_code = customer.customer_code
                    except Customer.DoesNotExist:
                        return JsonResponse({
                            'success': False,
                            'message': '未找到客户信息'
                        })
                    
                    # 查询符合条件的物料（同时匹配图号和客户代码）
                    materials = Material.objects.filter(
                        material_tempstate=0,
                        material_drawingno=drawing_number, 
                        material_customer=customer_code
                    )
                    
                    if materials.exists():
                        # 分离字母版本和数字版本的物料
                        letter_materials = [m for m in materials if m.material_version and m.material_version.isalpha() and len(m.material_version) == 1]
                        number_materials = [m for m in materials if m.material_version and m.material_version.isdigit()]
                        
                        # 优先使用字母版本，因为字母版本大于数字版本
                        if letter_materials:
                            # 找出字母版本中版本号最大的物料
                            material = max(letter_materials, key=lambda m: m.material_version)
                        elif number_materials:
                            # 找出数字版本中版本号最大的物料
                            material = max(number_materials, key=lambda m: int(m.material_version))
                        else:
                            # 如果没有有效的版本号，使用第一个物料
                            material = materials.first()
                        
                        # 解析存货属性JSON
                        inventory_attrs = {}
                        if material.material_inventory:
                            try:
                                inventory_attrs = json.loads(material.material_inventory)
                            except json.JSONDecodeError:
                                # 处理JSON解析错误
                                inventory_attrs = {}
                        
                        # 处理图纸文件路径
                        customer_drawing_url = None
                        production_drawing_url = None
                        process_drawing_url = None
                        inspection_sheet_files = None
                        
                        # 获取SMB基础路径
                        smb_base = get_smb_base_path()
                        
                        # 处理客户图纸
                        if material.material_drawing_customer:
                            customer_drawing_url = material.material_drawing_customer
                            if not os.path.isabs(customer_drawing_url) and not customer_drawing_url.startswith('http'):
                                customer_drawing_url = f"{smb_base}/{customer_drawing_url}"
                        
                        # 处理生产图纸
                        if material.material_drawing_finished:
                            production_drawing_url = material.material_drawing_finished
                            if not os.path.isabs(production_drawing_url) and not production_drawing_url.startswith('http'):
                                production_drawing_url = f"{smb_base}/{production_drawing_url}"
                        
                        # 处理工艺图纸
                        if material.material_drawing_workmanship:
                            process_drawing_url = material.material_drawing_workmanship
                            if not os.path.isabs(process_drawing_url) and not process_drawing_url.startswith('http'):
                                process_drawing_url = f"{smb_base}/{process_drawing_url}"
                        
                        # 处理检测单
                        if material.material_drawing_testingform:
                            inspection_sheet_files = material.material_drawing_testingform
                            if not os.path.isabs(inspection_sheet_files) and not inspection_sheet_files.startswith('http'):
                                inspection_sheet_files = f"{smb_base}/{inspection_sheet_files}"
                        
                        # 处理资料文件夹
                        document_folder_files = []
                        if material.material_drawing_datafolder:
                            document_paths = material.material_drawing_datafolder.split(';')
                            for path in document_paths:
                                if path.strip():
                                    file_name = os.path.basename(path)
                                    document_folder_files.append({
                                        'path': path,
                                        'name': file_name
                                    })
                        
                        # 返回物料信息
                        return JsonResponse({
                            'success': True,
                            'material': {
                                'material_id': material.material_id,
                                'material_drawingno': material.material_drawingno,
                                'material_name': material.material_name,
                                'material_no': material.material_no,
                                'material_count': material.material_count,
                                'material_quality': material.material_quality,  # 确保材质字段被正确返回
                                'material_spec': material.material_spec,
                                'material_version': material.material_version,
                                'material_semistate': material.material_semistate,
                                'material_producecount': material.material_producecount,
                                'material_partcount': material.material_partcount,
                                'material_unit': material.material_unit,
                                'material_lossrate': material.material_lossrate,
                                'material_customer': material.material_customer,
                                'customer_name': customer_name,
                                'material_workshop': material.material_workshop,
                                'material_attr': material.material_attr,
                                'material_supp_casting': material.material_supp_casting,
                                'material_supp_machining': material.material_supp_machining,
                                'material_supp_sheetmetal': material.material_supp_sheetmetal,
                                'material_supp_purchase': material.material_supp_purchase,
                                'material_processroute': material.material_processroute,
                                'material_supp_prochasemanager': material.material_supp_prochasemanager,
                                'inventory_attrs': inventory_attrs,
                                'material_img': material.material_img,
                                'material_img_url': f"{settings.MEDIA_URL}{material.material_img}" if material.material_img else None,
                                # 添加图纸相关字段
                                'material_drawing_customer': material.material_drawing_customer,
                                'material_drawing_finished': material.material_drawing_finished,
                                'material_drawing_workmanship': material.material_drawing_workmanship,
                                'material_drawing_testingform': material.material_drawing_testingform,
                                'material_drawing_datafolder': material.material_drawing_datafolder,
                                # 添加处理后的URL
                                'customer_drawing_url': customer_drawing_url,
                                'production_drawing_url': production_drawing_url,
                                'process_drawing_url': process_drawing_url,
                                'inspection_sheet_files': inspection_sheet_files,
                                'document_folder_files': document_folder_files
                            }
                        })
                    else:
                        # 没有找到匹配的物料
                        return JsonResponse({
                            'success': False,
                            'message': '未找到匹配的物料信息'
                        })
                except Exception as e:
                    # 记录错误信息
                    print(f"查询物料错误: {str(e)}")
                    return JsonResponse({
                        'success': False,
                        'message': f'查询失败: {str(e)}'
                    }, status=500)
            # 如果没有提供图号或客户名称
            return JsonResponse({
                'success': False,
                'message': '请提供客户图号和客户名称'
            })
        
        # 检查是否为编辑模式
        material = None
        inventory_attrs = {}
        customer_name = ""
        current_process_route_name = ""
        product_image_url = None
        customer_drawing_url = None
        production_drawing_url = None
        process_drawing_url = None
        inspection_sheet_files = []
        document_folder_files = []
        
        if material_id:
            # 编辑模式，获取物料信息
            try:
                material = Material.objects.get(material_id=material_id)
                
                # 解析存货属性JSON
                if material.material_inventory:
                    try:
                        inventory_attrs = json.loads(material.material_inventory)
                    except:
                        inventory_attrs = {}
                
                # 获取客户名称
                if material.material_customer:
                    try:
                        customer = Customer.objects.get(customer_code=material.material_customer)
                        customer_name = customer.customer_name
                    except Customer.DoesNotExist:
                        pass

                # 获取当前工艺路线名称（如果有工艺路线内容，尝试匹配工艺路线）
                if material.material_processroute:
                    try:
                        # 通过工艺路线内容查找对应的工艺路线名称
                        process_route = ProcessRoute.objects.filter(
                            processroute_content=material.material_processroute
                        ).first()
                        if process_route:
                            current_process_route_name = process_route.processroute_name
                    except Exception:
                        pass
                
                # 获取产品图片URL
                if material.material_img:
                    product_image_url = material.material_img
                    if not os.path.isabs(product_image_url) and not product_image_url.startswith('http'):
                        product_image_url = os.path.join(settings.MEDIA_URL, product_image_url)
                
                # 获取文件信息
                if material.material_drawing_customer:
                    customer_drawing_url = material.material_drawing_customer
                    # 如果是相对路径，拼接基础路径
                    if customer_drawing_url and not os.path.isabs(customer_drawing_url) and not customer_drawing_url.startswith('http'):
                        smb_base = get_smb_base_path()
                        customer_drawing_url = f"{smb_base}/{customer_drawing_url}"
                
                if material.material_drawing_finished:
                    production_drawing_url = material.material_drawing_finished
                    # 如果是相对路径，拼接基础路径
                    if production_drawing_url and not os.path.isabs(production_drawing_url) and not production_drawing_url.startswith('http'):
                        smb_base = get_smb_base_path()
                        production_drawing_url = f"{smb_base}/{production_drawing_url}"
                
                if material.material_drawing_workmanship:
                    process_drawing_url = material.material_drawing_workmanship
                    # 如果是相对路径，拼接基础路径
                    if process_drawing_url and not os.path.isabs(process_drawing_url) and not process_drawing_url.startswith('http'):
                        smb_base = get_smb_base_path()
                        process_drawing_url = f"{smb_base}/{process_drawing_url}"
                
                # 处理单文件 - 检测单
                if material.material_drawing_testingform:
                    inspection_sheet_files = material.material_drawing_testingform
                    # 如果是相对路径，拼接基础路径用于显示
                    if inspection_sheet_files and not os.path.isabs(inspection_sheet_files) and not inspection_sheet_files.startswith('http'):
                        smb_base = get_smb_base_path()
                        inspection_sheet_files = f"{smb_base}/{inspection_sheet_files}"
                
                # 处理多文件 - 资料文件夹
                if material.material_drawing_datafolder:
                    # 使用分号分隔的多个文件路径
                    document_paths = material.material_drawing_datafolder.split(';')
                    for path in document_paths:
                        if path.strip():
                            file_name = os.path.basename(path)
                            document_folder_files.append({
                                'path': path,
                                'name': file_name
                            })
                            
            except Material.DoesNotExist:
                messages.error(request, '物料不存在')
                return redirect('material_list')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取来源页面类型
        source_type = request.GET.get('source_type', 'direct')
        
        # 获取BOM父节点ID参数
        bom_pid = request.GET.get('bom_pid', None)
        
        # 获取客户代码参数
        customer_code_param = request.GET.get('customer_code', '')

        # 获取客户名称参数（直接从URL参数获取）
        customer_name_param = request.GET.get('customerName', '')

        # 优先使用URL参数中的客户名称
        if customer_name_param:
            customer_name = customer_name_param
        elif customer_code_param and not customer_name:
            # 如果没有客户名称参数但有客户代码，尝试查找对应的客户
            try:
                customer_obj = Customer.objects.get(customer_code=customer_code_param)
                customer_name = customer_obj.customer_name
            except Customer.DoesNotExist:
                pass
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            username = current_user.user_name
            user_nick = current_user.user_nick
        except User.DoesNotExist:
            username = request.session.get('username', '')
            user_nick = ''
        
        # 获取客户列表
        customers = Customer.objects.all().order_by('customer_order')
        
        # 获取物料属性分类
        material_categories = []
        try:
            # 从PropertiesType表获取物料属性分类
            properties_types = PropertiesType.objects.all().order_by('propertiestype_order')
            for prop_type in properties_types:
                material_categories.append({
                    'code': prop_type.propertiestype_code,
                    'name': prop_type.propertiestype_name,
                    'rule': prop_type.propertiesType_Rule
                })
            if not material_categories:
                messages.warning(request, '未找到物料属性分类数据，请先在基础数据中添加物料属性')
        except Exception as e:
            messages.error(request, f'获取物料属性分类失败: {str(e)}')
            # 使用空列表，不从数据字典获取
        
        # 从数据字典获取半成品状态
        semifinished_statuses = []
        try:
            # 查找半成品状态的父节点
            status_parent = Datadictionary.objects.get(datadictionary_tag='state')
            # 获取所有半成品状态
            statuses = Datadictionary.objects.filter(datadictionary_pid=status_parent.datadictionary_id).order_by('datadictionary_order')
            for status in statuses:
                semifinished_statuses.append({
                    'code': status.datadictionary_code,
                    'name': status.datadictionary_name
                })
        except Datadictionary.DoesNotExist:
            # 如果没有找到，使用默认值
            semifinished_statuses = []
        
        # 从数据字典获取计量单位
        units = []
        try:
            # 查找计量单位的父节点
            unit_parent = Datadictionary.objects.get(datadictionary_tag='unit')
            # 获取所有计量单位
            unit_list = Datadictionary.objects.filter(datadictionary_pid=unit_parent.datadictionary_id).order_by('datadictionary_order')
            for unit in unit_list:
                units.append({
                    'code': unit.datadictionary_code,
                    'name': unit.datadictionary_name
                })
        except Datadictionary.DoesNotExist:
            # 如果没有找到，使用默认值
            units = []
        
        # 从数据字典获取生产车间
        workshops = []
        try:
            # 查找生产车间的父节点
            workshop_parent = Datadictionary.objects.get(datadictionary_tag='workshop')
            # 获取所有生产车间
            workshop_list = Datadictionary.objects.filter(datadictionary_pid=workshop_parent.datadictionary_id).order_by('datadictionary_order')
            for workshop in workshop_list:
                workshops.append({
                    'code': workshop.datadictionary_code,
                    'name': workshop.datadictionary_name
                })
        except Datadictionary.DoesNotExist:
            # 如果没有找到，使用默认值
            workshops = []
        
        context = {
            'username': username,
            'user_nick': user_nick,
            'is_iframe': is_iframe,
            'is_preview': is_preview,
            'material': material,
            'customers': customers,
            'customer_name': customer_name,
            'current_process_route_name': current_process_route_name,
            'material_categories': material_categories,
            'semifinished_statuses': semifinished_statuses,
            'units': units,
            'workshops': workshops,
            'inventory_attrs': inventory_attrs,
            'product_image_url': product_image_url,
            'customer_drawing_url': customer_drawing_url,
            'production_drawing_url': production_drawing_url,
            'process_drawing_url': process_drawing_url,
            'inspection_sheet_files': inspection_sheet_files,
            'document_folder_files': document_folder_files,
            'source_type': source_type,
            'bom_pid': bom_pid,
            'customer_code': customer_code_param,
            'temp_state': temp_state,
            'bom_state': bom_state
        }
        
        return render(request, 'bom_sys/material_add.html', context)
    
    def post(self, request, material_id=None):
        """处理单物料录入表单提交，包括添加和编辑功能"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
                
        # 优先从URL参数中获取edit_id
        edit_id = request.POST.get('edit_id')
        # 如果URL中没有edit_id，再使用路径中的material_id
        if not edit_id and material_id:
            edit_id = material_id
        # 如果有edit_id，将其转换为整数用于后续处理
        material_id = int(edit_id) if edit_id else None
        # 获取表单数据
        drawing_number = request.POST.get('drawingNumber', '')
        customer_name = request.POST.get('customerName', '')
        material_category = request.POST.get('materialCategory', '')
        material_code = request.POST.get('materialCode', '')
        material_name = request.POST.get('materialName', '')
        version = request.POST.get('version', '')
        material_quality = request.POST.get('material_quality', '')
        material_spec = request.POST.get('materialSpec', '')
        
        # 不再从表单获取以下字段的值，统一设置为空值
        semifinished_status = ''
        loss_rate = 0
        quantity = 0
        production_quantity = 0
        part_usage = 0
        
        unit = request.POST.get('unit', '')
        workshop = request.POST.get('workshop', '')
        tech_manager = request.POST.get('techManager', '')
        procurement_manager = request.POST.get('procurementManager', '')
        upload_date = request.POST.get('uploadDate', '')
                
        # 获取来源页面类型
        source_type = request.POST.get('sourceType', 'direct')
                
        # 获取BOM父节点ID参数
        bom_pid = request.GET.get('bom_pid', None)
        if bom_pid:
            try:
                bom_pid = int(bom_pid)
            except (ValueError, TypeError):
                bom_pid = None
                
        # 获取供应商信息
        casting_supplier = request.POST.get('castingSupplier', '')
        machining_supplier = request.POST.get('machiningSupplier', '')
        sheet_metal_supplier = request.POST.get('sheetMetalSupplier', '')
        procurement_supplier = request.POST.get('procurementSupplier', '')
        
        # 获取存货属性
        customer_supply = request.POST.get('customerSupply', '否')
        outsourcing = request.POST.get('outsourcing', '否')
        sales = request.POST.get('sales', '否')
        self_made = request.POST.get('selfMade', '否')
        subcontract = request.POST.get('subcontract', '否')
        production_consumption = request.POST.get('productionConsumption', '否')
        batch_management = request.POST.get('batchManagement', '否')
        virtual_item = request.POST.get('virtualItem', '否')
        cutting_size = request.POST.get('cuttingSize', '')
        
        # 获取工艺路线相关字段
        material_processroute = request.POST.get('material_processroute', '')
        material_ProcessrouteTimes = request.POST.get('material_ProcessrouteTimes', '')
        material_ProcessrouteDevice = request.POST.get('material_ProcessrouteDevice', '')
        material_ProcessroutePrice = request.POST.get('material_ProcessroutePrice', '')
        material_ProcessrouteType = request.POST.get('material_ProcessrouteType', '')
        material_ProcessrouteOutSupplier = request.POST.get('material_ProcessrouteOutSupplier', '')
        material_ProcessrouteOutPrice = request.POST.get('material_ProcessrouteOutPrice', '')
        material_ProcessrouteImportant = request.POST.get('material_ProcessrouteImportant', '')
        material_ProcessrouteCode = request.POST.get('material_ProcessrouteCode', '')
        print(f"获取到的工艺路线内容: {material_processroute}")
        print(f"获取到的工艺详情: 工时={material_ProcessrouteTimes}, 设备={material_ProcessrouteDevice}, 工价={material_ProcessroutePrice}")
        
        # 存货属性
        inventory_attrs = {
            'customer_supply': customer_supply,
            'outsourcing': outsourcing,
            'sales': sales,
            'self_made': self_made,
            'subcontract': subcontract,
            'production_consumption': production_consumption,
            'batch_management': batch_management,
            'virtual_item': virtual_item,
            'cutting_size': cutting_size
        }
        
        # 获取要删除的文件列表
        files_to_delete = request.POST.get('filesToDelete', '')
        files_to_delete_list = [f.strip() for f in files_to_delete.split(',') if f.strip()]
        
        
        # 检查是否为编辑模式
        is_edit_mode = material_id is not None
        
        # 查找客户代码
        customer_code = ''
        try:
            customer = Customer.objects.get(customer_name=customer_name)
            customer_code = customer.customer_code
        except Customer.DoesNotExist:
            pass
            
        # 从数据库获取SMB路径
        smb_path = None
        try:
            # 尝试从Config表获取共享路径
            config = Config.objects.first()
            if config and config.config_smbpath:
                smb_path = config.config_smbpath.rstrip('/')  # 去除末尾可能存在的斜杠
                print(f"从数据库获取到SMB路径: {smb_path}")
        except Exception as e:
            print(f"获取SMB路径出错: {str(e)}")
            
        # 检查SMB路径是否可访问
        smb_root = os.path.dirname(smb_path)
        if not os.path.exists(smb_root):
            messages.error(request, f"无法访问共享路径{smb_root}，请确认网络连接或联系管理员")
            if is_edit_mode:
                return redirect(f"{reverse('material_edit', args=[material_id])}?success=false")
            else:
                return redirect(f"{reverse('material_add')}?success=false")
            
        
        
        # 处理文件上传 - 产品图片
        image_path = None
        if 'productImage' in request.FILES:
            # 获取上传的文件
            uploaded_file = request.FILES['productImage']
            
            # 构建文件路径 - 图片依然保存到media/upimg/下
            file_path = f"upimg/{customer_code}/{drawing_number}/{version}/Material_Img{os.path.splitext(uploaded_file.name)[1]}"
            
            # 确保目录存在
            os.makedirs(os.path.dirname(os.path.join(settings.MEDIA_ROOT, file_path)), exist_ok=True)
            
            # 保存文件
            with open(os.path.join(settings.MEDIA_ROOT, file_path), 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)
            
            # 设置图片路径（相对路径，用于存储到数据库）
            image_path = file_path
            print(f"上传了新图片: {image_path}")
        elif not is_edit_mode:
            # 如果是新增模式（可能是新增版本）且有原始图片路径，复制原始图片到新版本目录
            original_path = request.POST.get('original_image_path', '')
            print(f"原始图片路径: {original_path}")
            
            # 检查原始图片路径是否有效
            if original_path and original_path.strip():
                # 构建新文件路径
                file_ext = os.path.splitext(original_path)[1] if '.' in original_path else '.png'
                new_file_path = f"upimg/{customer_code}/{drawing_number}/{version}/Material_Img{file_ext}"
                
                # 确保目录存在
                os.makedirs(os.path.dirname(os.path.join(settings.MEDIA_ROOT, new_file_path)), exist_ok=True)
                
                # 复制原始文件到新路径
                try:
                    original_full_path = os.path.join(settings.MEDIA_ROOT, original_path)
                    new_full_path = os.path.join(settings.MEDIA_ROOT, new_file_path)
                    
                    print(f"尝试复制图片: {original_full_path} -> {new_full_path}")
                    if os.path.exists(original_full_path):
                        import shutil
                        shutil.copy2(original_full_path, new_full_path)
                        image_path = new_file_path
                        print(f"复制图片成功: {image_path}")
                    else:
                        print(f"原始图片不存在: {original_full_path}")
                        # 尝试使用绝对URL路径
                        if original_path.startswith('/'):
                            # 可能是相对于MEDIA_URL的路径
                            alternative_path = original_path[1:] if original_path.startswith('/') else original_path
                            alternative_full_path = os.path.join(settings.MEDIA_ROOT, alternative_path)
                            print(f"尝试替代路径: {alternative_full_path}")
                            
                            if os.path.exists(alternative_full_path):
                                shutil.copy2(alternative_full_path, new_full_path)
                                image_path = new_file_path
                                print(f"使用替代路径复制成功: {image_path}")
                except Exception as e:
                    print(f"复制图片失败: {str(e)}")
                    messages.warning(request, f"复制原始图片失败: {str(e)}")
        
        # 处理文件上传 - 客户图纸
        customer_drawing_path = None
        if 'customerDrawing' in request.FILES:
            uploaded_file = request.FILES['customerDrawing']
            file_ext = os.path.splitext(uploaded_file.name)[1]
            
            # 获取属性名称
            attr_name = ""
            try:
                prop_type = PropertiesType.objects.get(propertiestype_code=material_category)
                attr_name = prop_type.propertiestype_name
            except PropertiesType.DoesNotExist:
                pass
            
            # 使用新规则生成文件路径
            file_path = generate_file_path(
                smb_path, customer_code, customer_name, material_category, attr_name,
                material_code, drawing_number, material_name, version, 'customer', file_ext
            )
            
            # 保存文件
            with open(file_path, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)
            
            # 只保存相对路径部分 - 去除smb_path前缀
            if file_path.startswith(smb_path):
                relative_path = file_path[len(smb_path):].lstrip('/')
            else:
                relative_path = file_path
            customer_drawing_path = relative_path
        
        # 处理文件上传 - 生产图纸
        production_drawing_path = None
        if 'productionDrawing' in request.FILES:
            uploaded_file = request.FILES['productionDrawing']
            file_ext = os.path.splitext(uploaded_file.name)[1]
            
            # 获取属性名称
            attr_name = ""
            try:
                prop_type = PropertiesType.objects.get(propertiestype_code=material_category)
                attr_name = prop_type.propertiestype_name
            except PropertiesType.DoesNotExist:
                pass
            
            # 使用新规则生成文件路径
            file_path = generate_file_path(
                smb_path, customer_code, customer_name, material_category, attr_name,
                material_code, drawing_number, material_name, version, 'finished', file_ext
            )
            
            # 保存文件
            with open(file_path, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)
            
            # 只保存相对路径部分 - 去除smb_path前缀
            if file_path.startswith(smb_path):
                relative_path = file_path[len(smb_path):].lstrip('/')
            else:
                relative_path = file_path
            production_drawing_path = relative_path
        
        # 处理文件上传 - 工艺图纸
        process_drawing_path = None
        if 'processDrawing' in request.FILES:
            uploaded_file = request.FILES['processDrawing']
            file_ext = os.path.splitext(uploaded_file.name)[1]
            
            # 获取属性名称
            attr_name = ""
            try:
                prop_type = PropertiesType.objects.get(propertiestype_code=material_category)
                attr_name = prop_type.propertiestype_name
            except PropertiesType.DoesNotExist:
                pass
            
            # 使用新规则生成文件路径
            file_path = generate_file_path(
                smb_path, customer_code, customer_name, material_category, attr_name,
                material_code, drawing_number, material_name, version, 'workmanship', file_ext
            )
            
            # 保存文件
            with open(file_path, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)
            
            # 只保存相对路径部分 - 去除smb_path前缀
            if file_path.startswith(smb_path):
                relative_path = file_path[len(smb_path):].lstrip('/')
            else:
                relative_path = file_path
            process_drawing_path = relative_path
        
        # 处理文件上传 - 检测单（单文件）
        inspection_sheet_path = None
        if 'inspectionSheet' in request.FILES:
            uploaded_file = request.FILES['inspectionSheet']
            # 检查文件类型是否为Excel
            if not uploaded_file.name.endswith(('.xls', '.xlsx')):
                messages.error(request, '检测单只支持Excel格式文件(.xls, .xlsx)')
                if is_edit_mode:
                    return redirect(f"{reverse('material_edit', args=[material_id])}?success=false")
                else:
                    return redirect(f"{reverse('material_add')}?success=false")
            
            file_ext = os.path.splitext(uploaded_file.name)[1]
            
            # 获取属性名称
            attr_name = ""
            try:
                prop_type = PropertiesType.objects.get(propertiestype_code=material_category)
                attr_name = prop_type.propertiestype_name
            except PropertiesType.DoesNotExist:
                pass
            
            # 使用新规则生成文件路径
            file_path = generate_file_path(
                smb_path, customer_code, customer_name, material_category, attr_name,
                material_code, drawing_number, material_name, version, 'testingform', file_ext
            )
            
            # 保存文件
            with open(file_path, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)
            
            # 只保存相对路径部分 - 去除smb_path前缀
            if file_path.startswith(smb_path):
                relative_path = file_path[len(smb_path):].lstrip('/')
            else:
                relative_path = file_path
            inspection_sheet_path = relative_path
        
        # 资料文件夹不再处理上传，仅创建目录
        document_folder_path = None
        # 获取属性名称
        attr_name = ""
        try:
            prop_type = PropertiesType.objects.get(propertiestype_code=material_category)
            attr_name = prop_type.propertiestype_name
        except PropertiesType.DoesNotExist:
            pass
            
        # 生成资料文件夹路径
        document_folder_path = generate_file_path(
            smb_path, customer_code, customer_name, material_category, attr_name,
            material_code, drawing_number, material_name, version, 'datafolder'
        )
        
        # 只保存相对路径部分 - 去除smb_path前缀
        if document_folder_path.startswith(smb_path):
            document_folder_relative = document_folder_path[len(smb_path):].lstrip('/')
        else:
            document_folder_relative = document_folder_path
        document_folder_paths = document_folder_relative  # 资料文件夹路径
        
        # 获取临时状态参数
        temp_state_param = request.POST.get('temp_state')
        print('temp_state_param:',temp_state_param)
        # 只有当不是临时数据时才检查重复
        if not temp_state_param or temp_state_param == '0':
            # 检查是否存在重复记录
            duplicate_query = Material.objects.filter(
                material_tempstate=0,
                material_customer=customer_code,
                material_drawingno=drawing_number,
                material_version=version
            )
            # 如果是编辑模式，排除当前物料
            if is_edit_mode:
                duplicate_query = duplicate_query.exclude(material_id=material_id)
            
            
            # 检查是否存在重复记录
            if duplicate_query.exists():
                duplicate_material = duplicate_query.first()
                error_message = f'已存在相同的物料记录！客户名称：{customer_name}，客户图号：{drawing_number}，版本：{version}，物料编码：{duplicate_material.material_no}'
                messages.error(request, error_message)
                
                if is_edit_mode:
                    return redirect(f"{reverse('material_edit', args=[material_id])}?success=false")
                else:
                    return redirect(f"{reverse('material_add')}?success=false")
        
        try:
            # 获取相同客户名称和图号的所有物料版本
            related_materials = Material.objects.filter(
                material_tempstate=0,
                material_customer=customer_code,
                material_drawingno=drawing_number
            )
            
            # 如果是编辑模式，排除当前物料本身
            if is_edit_mode:
                related_materials = related_materials.exclude(material_id=material_id)
            
            # 确定当前版本是否是最大版本
            is_max_version = True
            
            # 判断是否有比当前版本更高的版本
            if related_materials.exists():
                # 分离字母版本和数字版本
                letter_versions = [m.material_version for m in related_materials if m.material_version and m.material_version.isalpha() and len(m.material_version) == 1]
                number_versions = [m.material_version for m in related_materials if m.material_version and m.material_version.isdigit()]
                
                # 判断当前版本是否是最大版本
                if version.isalpha() and len(version) == 1:
                    # 如果是字母版本，与其他字母版本比较
                    if letter_versions:
                        max_letter = max(letter_versions)
                        if version < max_letter:
                            is_max_version = False
                    # 字母版本始终大于数字版本，所以不需要与数字版本比较
                elif version.isdigit():
                    # 如果是数字版本，与其他数字版本比较
                    if number_versions:
                        max_number = max([int(v) for v in number_versions])
                        if int(version) < max_number:
                            is_max_version = False
                    # 如果存在字母版本，数字版本始终小于字母版本
                    if letter_versions:
                        is_max_version = False
            
            if is_edit_mode:
                # 编辑现有物料
                try:
                    material = Material.objects.get(material_id=material_id)
                    
                    # 更新物料信息，半成品状态、损耗率、数量、生产数量、零件用量设置为空值
                    material.material_drawingno = drawing_number
                    material.material_name = material_name
                    material.material_no = material_code
                    material.material_count = None
                    material.material_quality = material_quality
                    material.material_spec = material_spec
                    material.material_version = version
                    material.material_semistate = None
                    material.material_producecount = None
                    material.material_partcount = None
                    material.material_unit = unit
                    material.material_lossrate = None
                    material.material_customer = customer_code  # 存储客户代码而不是客户ID
                    material.material_customername = customer_name  # 存储客户名称
                    material.material_workshop = workshop
                    material.material_attr = material_category  # 存储属性代码
                    
                    # 获取并保存属性名称
                    try:
                        prop_type = PropertiesType.objects.get(propertiestype_code=material_category)
                        material.material_attrname = prop_type.propertiestype_name
                    except PropertiesType.DoesNotExist:
                        material.material_attrname = ""
                    
                    # 更新图片路径
                    if image_path:
                        material.material_img = image_path
                    
                    # 更新图纸信息
                    if customer_drawing_path:
                        material.material_drawing_customer = customer_drawing_path
                    if production_drawing_path:
                        material.material_drawing_finished = production_drawing_path
                    if process_drawing_path:
                        material.material_drawing_workmanship = process_drawing_path
                    
                    # 更新检测单和资料文件夹
                    if inspection_sheet_path:
                        # 如果上传了新的检测单，替换旧的
                        material.material_drawing_testingform = inspection_sheet_path
                    elif material.material_drawing_testingform and files_to_delete_list:
                        # 处理删除检测单的情况
                        if material.material_drawing_testingform in files_to_delete_list:
                            material.material_drawing_testingform = ""
                    
                    # 资料文件夹仅处理删除，不处理新增
                    if material.material_drawing_datafolder and files_to_delete_list:
                        # 过滤掉已标记为删除的文件
                        existing_paths = material.material_drawing_datafolder.split(';')
                        existing_paths = [p for p in existing_paths if p.strip() and p not in files_to_delete_list]
                        material.material_drawing_datafolder = ";".join(existing_paths) if existing_paths else ""
                    
                    # 更新存货属性
                    material.material_inventory = json.dumps(inventory_attrs)
                    material.material_size = cutting_size
                    
                    # 更新供应商信息
                    material.material_supp_casting = casting_supplier
                    material.material_supp_machining = machining_supplier
                    material.material_supp_sheetmetal = sheet_metal_supplier
                    material.material_supp_purchase = procurement_supplier
                    
                    # 更新工艺路线相关字段
                    material.material_processroute = material_processroute
                    material.material_ProcessrouteTimes = material_ProcessrouteTimes
                    material.material_ProcessrouteDevice = material_ProcessrouteDevice
                    material.material_ProcessroutePrice = material_ProcessroutePrice
                    material.material_ProcessrouteType = material_ProcessrouteType
                    material.material_ProcessrouteOutSupplier = material_ProcessrouteOutSupplier
                    material.material_ProcessrouteOutPrice = material_ProcessrouteOutPrice
                    material.material_ProcessrouteImportant = material_ProcessrouteImportant
                    material.material_ProcessrouteCode = material_ProcessrouteCode
                    material.material_supp_prochasemanager = procurement_manager
                    material.material_techmanager = tech_manager

                    # 更新修改时间
                    material.material_updateTime = get_local_now()
                    
                    # 设置物料状态
                    if is_max_version:
                        material.material_state = 1  # 启用
                    else:
                        material.material_state = 2  # 停用
                    
                    # 检查是否为临时状态（导入预览模式）
                    temp_state = request.POST.get('temp_state')
                    if temp_state:
                        try:
                            temp_state = int(temp_state)
                            material.material_tempstate = temp_state
                            # 设置用户ID
                            material.material_tempUserId = request.session.get('user_id')
                        except (ValueError, TypeError):
                            pass

                    # 保存物料
                    material.save()

                    # 记录日志
                    log_system_action(
                        request=request,
                        action='修改物料',
                        module='物料管理',
                        target_id=material.material_id,
                        target_name=f"{material.material_no} - {material.material_name}",
                        description=f'修改了物料信息'
                    )

                    # 检查是否有更新物料ID参数，如果有则更新所有关联的BOM表记录
                    update_material_id = request.POST.get('update_material_id')
                    if update_material_id:
                        try:
                            # 查询所有使用该物料ID的BOM记录
                            bom_records = Bom.objects.filter(bom_material=update_material_id)
                            print(bom_records)
                            if bom_records.exists():
                                # 批量更新所有相关BOM记录
                                for bom in bom_records:
                                    bom.bom_material = material.material_id
                                    bom.bom_state = material.material_tempstate
                                    bom.save()
                                
                                # 只在非iframe模式下添加消息
                                if request.GET.get('iframe', '0') != '1':
                                    messages.success(request, f'物料和{bom_records.count()}条BOM数据已成功更新')
                            else:
                                if request.GET.get('iframe', '0') != '1':
                                    messages.info(request, f'未找到使用物料ID {update_material_id} 的BOM记录，仅更新了物料数据')
                        except Exception as e:
                            if request.GET.get('iframe', '0') != '1':
                                messages.error(request, f'更新BOM数据失败: {str(e)}')
                    
                    # 检查是否有BOM父节点ID参数，如果有则创建新的BOM记录
                    if bom_pid is not None and (source_type == 'bom_list' or source_type == 'bom_tree'):
                        try:
                            # 确定BOM层级
                            bom_level = 0  # 默认为顶级节点（层级0）
                            if bom_pid > 0:
                                try:
                                    # 获取父节点的层级
                                    parent_bom = Bom.objects.get(bom_id=bom_pid)
                                    # 确保parent_bom.bom_level不为None
                                    parent_level = parent_bom.bom_level if parent_bom.bom_level is not None else 0
                                    bom_level = parent_level + 1
                                except Bom.DoesNotExist:
                                    # 如果父节点不存在，则默认为顶级节点
                                    bom_level = 0
                            
                            # 获取BOM基本信息参数
                            bom_num = request.POST.get('bomNum', 1)
                            bom_lossrate = request.POST.get('bomLossRate', 0)
                            bom_partcount = request.POST.get('bomPartCount', 1)
                            bom_producecount = request.POST.get('bomProduceCount', 1)

                            # 转换参数类型
                            try:
                                bom_num = int(bom_num)
                                bom_lossrate = float(bom_lossrate)
                                bom_partcount = int(bom_partcount)
                                bom_producecount = int(bom_producecount)
                            except (ValueError, TypeError):
                                # 如果转换失败，使用默认值
                                bom_num = 1
                                bom_lossrate = 0
                                bom_partcount = 1
                                bom_producecount = 1

                            print(f"编辑模式创建BOM记录的基本信息: num={bom_num}, lossrate={bom_lossrate}, partcount={bom_partcount}, producecount={bom_producecount}")

                            # 创建新的BOM记录
                            new_bom = Bom(
                                bom_pid=bom_pid,
                                bom_level=bom_level,
                                bom_material=material.material_id,
                                bom_num=bom_num,
                                bom_lossrate=bom_lossrate,
                                bom_partcount=bom_partcount,
                                bom_producecount=bom_producecount,
                                bom_state=material.material_state,
                                # 设置BOM时间字段
                                bom_addTime=get_local_now(),
                                bom_updateTime=get_local_now()
                            )
                            
                            # 检查是否需要设置为临时BOM状态（导入预览模式）
                            bom_state = request.POST.get('bom_state')
                            if bom_state:
                                try:
                                    bom_state = int(bom_state)
                                    new_bom.bom_state = bom_state
                                    # 设置导入用户ID
                                    new_bom.bom_ImportUser = request.session.get('user_id')
                                except (ValueError, TypeError):
                                    pass
                            
                            new_bom.save()
                            # 只在非iframe模式下添加消息
                            if request.GET.get('iframe', '0') != '1':
                                messages.success(request, f'物料已成功更新并添加到BOM树中')
                        except Exception as e:
                            if request.GET.get('iframe', '0') != '1':
                                messages.error(request, f'创建BOM记录失败: {str(e)}')
                    
                    # 如果当前版本是最大版本，将其他版本设置为停用
                    if is_max_version:
                        related_materials.exclude(material_id=material.material_id).update(material_state=2)
                    
                    
                    
                    # 根据来源页面类型决定跳转行为
                    if source_type == 'material_list':
                        # 如果是从物料清单页面来的，检查是否为iframe请求
                        is_iframe = request.GET.get('iframe', '0') == '1'

                        if is_iframe:
                            # iframe模式下，重定向到当前页面并添加success参数，不跳转到material_list页面
                            # 这样可以保持iframe内容不变，同时触发成功消息的显示和postMessage通知
                            current_url = request.get_full_path()
                            if '?' in current_url:
                                redirect_url = current_url + '&success=true'
                            else:
                                redirect_url = current_url + '?success=true'
                            print(f"编辑物料iframe模式重定向 - 当前URL: {current_url}")
                            print(f"编辑物料iframe模式重定向 - 重定向URL: {redirect_url}")
                            return redirect(redirect_url)
                        else:
                            # 非iframe模式，跳回物料清单页面
                            # 获取所有的查询参数，以便保留搜索条件
                            query_params = request.GET.copy()

                            # 构建查询字符串
                            query_string = query_params.urlencode()
                            if query_string:
                                query_string = '?' + query_string

                            # 使用reverse获取URL，然后附加查询参数
                            redirect_url = reverse('material_list') + query_string
                            return redirect(redirect_url)
                    else:
                        # 如果是从单物料录入页面来的，留在当前页面并添加success参数
                        # 检查是否为iframe请求
                        is_iframe = request.GET.get('iframe', '0') == '1'
                        if is_iframe:
                            # iframe模式下，重定向到当前页面并添加success参数
                            current_url = request.get_full_path()
                            if '?' in current_url:
                                redirect_url = current_url + '&success=true'
                            else:
                                redirect_url = current_url + '?success=true'
                            return redirect(redirect_url)
                        else:
                            return redirect(f"{reverse('material_edit', args=[material.material_id])}?success=true")
                        
                except Material.DoesNotExist:
                    messages.error(request, '物料不存在')
                    return redirect('material_list')
            else:
                # 如果当前版本是最大版本，将其他版本设置为停用
                if is_max_version:
                    related_materials.update(material_state=2)
                    
                # 获取属性名称
                attr_name = ""
                try:
                    prop_type = PropertiesType.objects.get(propertiestype_code=material_category)
                    attr_name = prop_type.propertiestype_name
                except PropertiesType.DoesNotExist:
                    pass
                    
                # 创建新物料
                material = Material(
                    material_drawingno=drawing_number,
                    material_name=material_name,
                    material_no=material_code,
                    material_count=None,
                    material_quality=material_quality,
                    material_spec=material_spec,
                    material_version=version,
                    material_semistate=None,
                    material_producecount=None,
                    material_partcount=None,
                    material_unit=unit,
                    material_lossrate=None,
                    material_customer=customer_code,  # 存储客户代码而不是客户ID
                    material_customername=customer_name,  # 存储客户名称
                    material_workshop=workshop,
                    material_attr=material_category,
                    material_attrname=attr_name,  # 存储属性名称
                    material_inventory=json.dumps(inventory_attrs),
                    material_size=cutting_size,
                    material_supp_casting=casting_supplier,
                    material_supp_machining=machining_supplier,
                    material_supp_sheetmetal=sheet_metal_supplier,
                    material_supp_purchase=procurement_supplier,
                    material_processroute=material_processroute,
                    material_ProcessrouteTimes=material_ProcessrouteTimes,
                    material_ProcessrouteDevice=material_ProcessrouteDevice,
                    material_ProcessroutePrice=material_ProcessroutePrice,
                    material_ProcessrouteType=material_ProcessrouteType,
                    material_ProcessrouteOutSupplier=material_ProcessrouteOutSupplier,
                    material_ProcessrouteOutPrice=material_ProcessrouteOutPrice,
                    material_ProcessrouteImportant=material_ProcessrouteImportant,
                    material_ProcessrouteCode=material_ProcessrouteCode,
                    material_supp_prochasemanager=procurement_manager,
                    material_techmanager=tech_manager,
                    material_uptime=get_local_now(),
                    material_state=1 if is_max_version else 2,  # 如果是最大版本则启用，否则停用
                    # 设置添加时间和更新时间
                    material_addTime=get_local_now(),
                    material_updateTime=get_local_now()
                )
                
                # 检查是否为临时状态（导入预览模式）
                temp_state = request.POST.get('temp_state')
                print('temp_stateeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee',temp_state)
                if temp_state:
                    try:
                        temp_state = int(temp_state)
                        material.material_tempstate = temp_state
                        # 设置用户ID
                        material.material_tempUserId = request.session.get('user_id')
                    except (ValueError, TypeError):
                        pass
                
                # 设置图片和图纸路径
                if image_path:
                    material.material_img = image_path
                if customer_drawing_path:
                    material.material_drawing_customer = customer_drawing_path
                if production_drawing_path:
                    material.material_drawing_finished = production_drawing_path
                if process_drawing_path:
                    material.material_drawing_workmanship = process_drawing_path
                
                # 设置检测单（单文件）和资料文件夹
                if inspection_sheet_path:
                    material.material_drawing_testingform = inspection_sheet_path
                # 设置资料文件夹路径
                if document_folder_paths:
                    material.material_drawing_datafolder = document_folder_paths
                
                # 保存物料
                material.save()

                # 记录日志
                log_system_action(
                    request=request,
                    action='新增物料',
                    module='物料管理',
                    target_id=material.material_id,
                    target_name=f"{material.material_no} - {material.material_name}",
                    description=f'新增了物料'
                )

                # 检查是否有更新物料ID参数，如果有则更新所有关联的BOM表记录
                update_material_id = request.POST.get('update_material_id')
                if update_material_id:
                    try:
                        # 查询所有使用该物料ID的BOM记录
                        bom_records = Bom.objects.filter(bom_material=update_material_id)
                        
                        if bom_records.exists():
                            # 批量更新所有相关BOM记录
                            for bom in bom_records:
                                bom.bom_material = material.material_id
                                bom.bom_state = material.material_tempstate
                                # 更新BOM修改时间
                                bom.bom_updateTime = get_local_now()
                                bom.save()
                            
                            # 只在非iframe模式下添加消息
                            if request.GET.get('iframe', '0') != '1':
                                messages.success(request, f'物料和{bom_records.count()}条BOM数据已成功保存')
                        else:
                            if request.GET.get('iframe', '0') != '1':
                                messages.info(request, f'未找到使用物料ID {update_material_id} 的BOM记录，仅保存了物料数据')
                    except Exception as e:
                        if request.GET.get('iframe', '0') != '1':
                            messages.error(request, f'更新BOM数据失败: {str(e)}')
                
                # 检查是否有BOM父节点ID参数，如果有则创建新的BOM记录
                if bom_pid is not None and (source_type == 'bom_list' or source_type == 'bom_tree'):
                    try:
                        # 确定BOM层级
                        bom_level = 0  # 默认为顶级节点（层级0）
                        if bom_pid > 0:
                            try:
                                # 获取父节点的层级
                                parent_bom = Bom.objects.get(bom_id=bom_pid)
                                # 确保parent_bom.bom_level不为None
                                parent_level = parent_bom.bom_level if parent_bom.bom_level is not None else 0
                                bom_level = parent_level + 1
                            except Bom.DoesNotExist:
                                # 如果父节点不存在，则默认为顶级节点
                                bom_level = 0
                        
                        # 获取BOM基本信息参数
                        bom_num = request.POST.get('bomNum', 1)
                        bom_lossrate = request.POST.get('bomLossRate', 0)
                        bom_partcount = request.POST.get('bomPartCount', 1)
                        bom_producecount = request.POST.get('bomProduceCount', 1)

                        # 转换参数类型
                        try:
                            bom_num = int(bom_num)
                            bom_lossrate = float(bom_lossrate)
                            bom_partcount = int(bom_partcount)
                            bom_producecount = int(bom_producecount)
                        except (ValueError, TypeError):
                            # 如果转换失败，使用默认值
                            bom_num = 1
                            bom_lossrate = 0
                            bom_partcount = 1
                            bom_producecount = 1

                        print(f"创建BOM记录的基本信息: num={bom_num}, lossrate={bom_lossrate}, partcount={bom_partcount}, producecount={bom_producecount}")

                        # 创建新的BOM记录
                        new_bom = Bom(
                            bom_pid=bom_pid,
                            bom_level=bom_level,
                            bom_material=material.material_id,
                            bom_num=bom_num,
                            bom_lossrate=bom_lossrate,
                            bom_partcount=bom_partcount,
                            bom_producecount=bom_producecount,
                            bom_state=material.material_state,
                            # 设置BOM时间字段
                            bom_addTime=get_local_now(),
                            bom_updateTime=get_local_now()
                        )
                        
                        # 检查是否需要设置为临时BOM状态（导入预览模式）
                        bom_state = request.POST.get('bom_state')
                        print('bom_stateeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee',bom_state)
                        if bom_state:
                            try:
                                bom_state = int(bom_state)
                                new_bom.bom_state = bom_state
                                # 设置导入用户ID
                                new_bom.bom_ImportUser = request.session.get('user_id')
                            except (ValueError, TypeError):
                                pass
                        
                        new_bom.save()
                        # 只在非iframe模式下添加消息
                        if request.GET.get('iframe', '0') != '1':
                            messages.success(request, f'物料已成功保存并添加到BOM树中')
                    except Exception as e:
                        if request.GET.get('iframe', '0') != '1':
                            messages.error(request, f'创建BOM记录失败: {str(e)}')

                # 检查是否需要更新BOM中的物料ID（从BOM树视图新增物料时）
                update_bom_material = request.POST.get('update_bom_material')
                print("update_bom_material:",update_bom_material)
                print("is_edit_mode:",is_edit_mode)
                print("所有POST参数:", dict(request.POST))
                if update_bom_material and not is_edit_mode:
                    try:
                        # 查找对应的BOM记录
                        bom_record = Bom.objects.get(bom_id=update_bom_material)
                        # 更新BOM记录的物料ID
                        bom_record.bom_material = material.material_id
                        # 更新BOM修改时间（因为BOM关联的物料发生了变化）
                        bom_record.bom_updateTime = get_local_now()
                        bom_record.save()
                        # 只在非iframe模式下添加消息
                        if request.GET.get('iframe', '0') != '1':
                            messages.success(request, f'物料已成功保存并更新到BOM记录中')
                        print(f"更新BOM记录 {update_bom_material} 的物料ID为 {material.material_id}")
                    except Bom.DoesNotExist:
                        if request.GET.get('iframe', '0') != '1':
                            messages.warning(request, f'未找到BOM记录 {update_bom_material}')
                    except Exception as e:
                        if request.GET.get('iframe', '0') != '1':
                            messages.error(request, f'更新BOM记录失败: {str(e)}')

             
                
                # 根据来源页面类型决定跳转行为
                print(f"编辑模式 - source_type: {source_type}")
                print(f"编辑模式 - request.GET: {dict(request.GET)}")
                if source_type == 'material_list':
                    # 如果是从物料清单页面来的，检查是否为iframe请求
                    is_iframe = request.GET.get('iframe', '0') == '1'
                    print(f"编辑模式 - is_iframe: {is_iframe}")

                    if is_iframe:
                        # iframe模式下，重定向到当前页面并添加success参数，不跳转到material_list页面
                        # 这样可以保持iframe内容不变，同时触发成功消息的显示和postMessage通知
                        current_url = request.get_full_path()
                        if '?' in current_url:
                            redirect_url = current_url + '&success=true'
                        else:
                            redirect_url = current_url + '?success=true'
                        print(f"iframe模式重定向 - 当前URL: {current_url}")
                        print(f"iframe模式重定向 - 重定向URL: {redirect_url}")
                        return redirect(redirect_url)
                    else:
                        # 非iframe模式，跳回物料清单页面
                        # 获取所有的查询参数，以便保留搜索条件
                        query_params = request.GET.copy()

                        # 构建查询字符串
                        query_string = query_params.urlencode()
                        if query_string:
                            query_string = '?' + query_string

                        # 使用reverse获取URL，然后附加查询参数
                        redirect_url = reverse('material_list') + query_string
                        return redirect(redirect_url)
                elif source_type == 'bom_tree':
                    # 如果是从BOM树视图来的，留在当前页面并添加success参数
                    # 检查是否为iframe请求
                    is_iframe = request.GET.get('iframe', '0') == '1'

                    # 构建重定向URL，保留所有原有参数并添加success参数
                    current_url = request.get_full_path()
                    if '?' in current_url:
                        redirect_url = current_url + '&success=true'
                    else:
                        redirect_url = current_url + '?success=true'

                    return redirect(redirect_url)
                elif source_type == 'bom_list':
                    # 如果是从BOM列表来的，留在当前页面并添加success参数
                    # 检查是否为iframe请求
                    is_iframe = request.GET.get('iframe', '0') == '1'

                    print(f"BOM列表保存重定向 - iframe: {is_iframe}")
                    print(f"当前完整路径: {request.get_full_path()}")
                    print(f"GET参数: {dict(request.GET)}")

                    # 构建重定向URL，保留所有原有参数并添加success参数
                    current_url = request.get_full_path()
                    if '?' in current_url:
                        redirect_url = current_url + '&success=true'
                    else:
                        redirect_url = current_url + '?success=true'

                    print(f"重定向URL: {redirect_url}")
                    return redirect(redirect_url)
                else:
                    # 如果是从单物料录入页面来的，留在当前页面并添加success参数
                    # 检查是否为iframe请求
                    is_iframe = request.GET.get('iframe', '0') == '1'
                    if is_iframe:
                        # iframe模式下，重定向到当前页面并添加success参数
                        current_url = request.get_full_path()
                        if '?' in current_url:
                            redirect_url = current_url + '&success=true'
                        else:
                            redirect_url = current_url + '?success=true'
                        print(f"新增物料iframe模式重定向 - 当前URL: {current_url}")
                        print(f"新增物料iframe模式重定向 - 重定向URL: {redirect_url}")
                        return redirect(redirect_url)
                    else:
                        return redirect(f"{reverse('material_edit', args=[material.material_id])}?success=true")
                
                
        except Exception as e:
            error_message = f'操作失败：{str(e)}'
            messages.error(request, error_message)
            
            if is_edit_mode:
                return redirect(f"{reverse('material_edit', args=[material_id])}?success=false")
            else:
                return redirect(f"{reverse('material_add')}?success=false")



class MaterialListView(View):
    """物料清单列表视图类"""
    
    def get(self, request):
        """显示物料清单列表，支持分页和多条件查询"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取查询参数
        drawing_number = request.GET.get('drawing_number', '')
        material_name = request.GET.get('material_name', '')
        material_code = request.GET.get('material_code', '')
        material_category = request.GET.get('material_category', '')
        customer_id = request.GET.get('customer_id', '')
        semifinished_status = request.GET.get('semifinished_status', '')
        material_state = request.GET.get('material_state', '1')  # 默认查询启用状态的物料
        
        # 构建查询条件
        query = Q()
        if drawing_number:
            query &= Q(material_drawingno__icontains=drawing_number)
        if material_name:
            query &= Q(material_name__icontains=material_name)
        if material_code:
            query &= Q(material_no__icontains=material_code)
        if material_category:
            # 支持通过属性代码或属性名称搜索
            query &= (Q(material_attr=material_category) | Q(material_attrname__icontains=material_category))
        if customer_id:
            # 尝试通过客户名称或代码查找
            try:
                # 首先尝试精确匹配客户名称
                customer = Customer.objects.get(customer_name=customer_id)
                customer_code = customer.customer_code
                query &= (Q(material_customer=customer_code) | Q(material_customername=customer_id))
            except Customer.DoesNotExist:
                try:
                    # 如果精确匹配失败，尝试通过ID查找
                    if customer_id.isdigit():
                        customer = Customer.objects.get(customer_id=int(customer_id))
                        customer_code = customer.customer_code
                        query &= (Q(material_customer=customer_code) | Q(material_customername=customer.customer_name))
                    else:
                        # 如果不是数字，直接搜索客户名称字段
                        query &= Q(material_customername__icontains=customer_id)
                except Customer.DoesNotExist:
                    # 如果还是找不到，直接搜索客户名称字段
                    query &= Q(material_customername__icontains=customer_id)
        if semifinished_status:
            query &= Q(material_semistate=semifinished_status)
        # 当material_state为"-1"时表示查询全部状态，不添加状态筛选条件
        if material_state and material_state != '-1':
            query &= Q(material_state=material_state)
        
        query &= Q(material_tempstate=0)
        # 执行查询
        materials = Material.objects.filter(query)
        
        # 定义版本号排序函数
        def get_version_sort_key(version):
            if not version:
                return (0, 0)  # 无版本号排在最前面
            
            if version.isalpha() and len(version) == 1:
                # 字母版本，使用ASCII码的负值表示降序
                return (-ord(version), 0)
            elif version.isdigit():
                # 数字版本，使用负值表示降序
                return (0, -int(version))
            else:
                # 其他格式的版本号
                return (0, 0)
        
        # 自定义排序：先按中缀（中间三位）、再按前缀（前两位）、再按后缀（后四位），最后按版本降序
        def material_sort_key(material):
            # 先按物料编码排序
            prefix = material.material_no[0:2] if material.material_no and len(material.material_no) >= 2 else ''
            middle = material.material_no[2:5] if material.material_no and len(material.material_no) >= 5 else ''
            suffix = material.material_no[5:9] if material.material_no and len(material.material_no) >= 9 else ''
            
            # 按照指定顺序返回排序键
            return (middle, prefix, suffix, get_version_sort_key(material.material_version))
        
        # 应用排序
        materials = sorted(materials, key=material_sort_key)
        
        # 分页处理
        page = request.GET.get('page', 1)
        per_page = request.GET.get('per_page', 10)  # 每页显示的记录数
        
        paginator = Paginator(materials, per_page)
        try:
            materials_page = paginator.page(page)
        except PageNotAnInteger:
            materials_page = paginator.page(1)
        except EmptyPage:
            materials_page = paginator.page(paginator.num_pages)
        
        # 获取客户列表，用于筛选
        customers = Customer.objects.all().order_by('customer_order')
        
        # 获取物料属性分类
        material_categories = []
        try:
            # 从PropertiesType表获取物料属性分类
            properties_types = PropertiesType.objects.all().order_by('propertiestype_order')
            for prop_type in properties_types:
                material_categories.append({
                    'code': prop_type.propertiestype_code,
                    'name': prop_type.propertiestype_name,
                    'rule': prop_type.propertiesType_Rule
                })
            if not material_categories:
                messages.warning(request, '未找到物料属性分类数据，请先在基础数据中添加物料属性')
        except Exception as e:
            messages.error(request, f'获取物料属性分类失败: {str(e)}')
            # 使用空列表，不从数据字典获取
        
        # 从数据字典获取半成品状态
        semifinished_statuses = []
        try:
            # 查找半成品状态的父节点
            status_parent = Datadictionary.objects.get(datadictionary_tag='state')
            # 获取所有半成品状态
            statuses = Datadictionary.objects.filter(datadictionary_pid=status_parent.datadictionary_id).order_by('datadictionary_order')
            for status in statuses:
                semifinished_statuses.append({
                    'code': status.datadictionary_code,
                    'name': status.datadictionary_name
                })
        except Datadictionary.DoesNotExist:
            # 如果没有找到，使用默认值
            semifinished_statuses = []
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'materials': materials_page,
                'customers': customers,
                'material_categories': material_categories,
                'semifinished_statuses': semifinished_statuses,
                # 保存查询条件，用于分页时保持查询参数
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_category': material_category,
                    'customer_id': customer_id,
                    'semifinished_status': semifinished_status,
                    'material_state': material_state,
                    'per_page': per_page
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'materials': materials_page,
                'customers': customers,
                'material_categories': material_categories,
                'semifinished_statuses': semifinished_statuses,
                # 保存查询条件，用于分页时保持查询参数
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_category': material_category,
                    'customer_id': customer_id,
                    'semifinished_status': semifinished_status,
                    'material_state': material_state,
                    'per_page': per_page
                }
            }
        
        return render(request, 'bom_sys/material_list.html', context)


class MaterialDeleteView(View):
    """删除物料视图类"""
    
    def get(self, request, material_id):
        """处理删除物料请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取要删除的物料
        try:
            material = Material.objects.get(material_id=material_id)
        except Material.DoesNotExist:
            messages.error(request, '物料不存在')
            return redirect('material_list')
        
        # 查找并删除相关的BOM记录
        affected_boms = Bom.objects.filter(bom_material=material_id)
        bom_count = affected_boms.count()

        # 记录日志（在删除前记录）
        log_description = f'删除了物料'
        if bom_count > 0:
            log_description += f'，同时删除了{bom_count}个相关BOM记录'

        log_system_action(
            request=request,
            action='删除物料',
            module='物料管理',
            target_id=material.material_id,
            target_name=f"{material.material_no} - {material.material_name}",
            description=log_description
        )

        # 先删除相关的BOM记录
        affected_boms.delete()

        # 再删除物料
        material.delete()

        # 获取所有的查询参数，以便保留搜索条件
        query_params = request.GET.copy()
        
        # 检查是否为iframe请求
        is_iframe = query_params.get('iframe', '0') == '1'
        
        # 移除不相关的参数
        if 'iframe' in query_params:
            del query_params['iframe']
            
        # 构建查询字符串
        query_string = query_params.urlencode()
        if query_string:
            query_string = '?' + query_string
        
        if is_iframe:
            return render(request, 'bom_sys/iframe_redirect.html', {
                'view_name': 'material_list',
                'query_string': f'?iframe=1{query_string[1:] if query_string else ""}'
            })
        else:
            # 使用reverse获取URL，然后附加查询参数
            from django.urls import reverse
            redirect_url = reverse('material_list') + query_string
            return redirect(redirect_url)


def get_max_material_code(request):
    """获取最大物料编码的自增部分"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '未登录'})
    
    # 获取类别代码和中缀代码
    category_code = request.GET.get('category_code', '')
    middle_code = request.GET.get('middle_code', '')
    
    if not category_code or not middle_code:
        return JsonResponse({'success': False, 'message': '参数不完整'})
    
    try:
        # 查询最大编码
        prefix = f"{category_code}{middle_code}"
        materials = Material.objects.filter(material_no__startswith=prefix).order_by('-material_no')
        
        if materials.exists():
            # 获取最大的编码
            max_code = materials.first().material_no
            # 提取后四位数字
            suffix = max_code[len(prefix):]
            try:
                # 转换为整数并加1
                next_number = str(int(suffix) + 1).zfill(4)
                return JsonResponse({'success': True, 'next_number': next_number})
            except (ValueError, TypeError):
                # 如果无法转换为整数，使用默认值0001
                return JsonResponse({'success': False, 'message': '编码格式错误'})
        else:
            # 如果没有找到匹配的记录，使用默认值0001
            return JsonResponse({'success': True, 'next_number': '0001'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


def check_duplicate_material(request):
    """检查是否存在重复的物料记录"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'duplicate': False, 'message': '未登录'})
    
    # 获取请求参数
    drawing_number = request.GET.get('drawing_number', '')
    customer_name = request.GET.get('customer_name', '')
    version = request.GET.get('version', '')
    material_id = request.GET.get('material_id', None)
    # 获取临时状态参数，判断是否为临时数据
    temp_state = request.GET.get('temp_state', '0')
    
    print('temp_state:',temp_state)
    # 如果是临时数据，不检查重复
    if temp_state and temp_state != '0':
        return JsonResponse({'duplicate': False})
    
    if not drawing_number or not customer_name or not version:
        return JsonResponse({'duplicate': False, 'message': '参数不完整'})
    
    try:
        # 查找客户代码
        customer_code = ''
        try:
            customer = Customer.objects.get(customer_name=customer_name)
            customer_code = customer.customer_code
        except Customer.DoesNotExist:
            return JsonResponse({'duplicate': False, 'message': '客户不存在'})
        
        # 查询是否存在重复记录
        duplicate_query = Material.objects.filter(
            material_tempstate=0,
            material_customer=customer_code,
            material_drawingno=drawing_number,
            material_version=version
           
        )
        
        # 如果提供了物料ID，排除当前物料
        if material_id and material_id.isdigit():
            duplicate_query = duplicate_query.exclude(material_id=int(material_id))
        
        # 检查是否存在重复记录
        if duplicate_query.exists():
            duplicate_material = duplicate_query.first()
            return JsonResponse({
                'duplicate': True,
                'material_id': duplicate_material.material_id,
                'material_no': duplicate_material.material_no,
                'message': f'已存在相同的物料记录！物料编码：{duplicate_material.material_no}'
            })
        else:
            return JsonResponse({'duplicate': False})
    except Exception as e:
        return JsonResponse({'duplicate': False, 'message': str(e)})


@method_decorator(csrf_exempt, name='dispatch')
class PropertiesTypeListView(View):
    """物料属性列表视图"""
    
    def get(self, request):
        """显示物料属性列表"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取所有物料属性
        properties_types = PropertiesType.objects.all().order_by('propertiestype_order')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有客户信息用于中缀规则选择
        customers = Customer.objects.all().order_by('customer_order')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'properties_types': properties_types,
                'customers': customers,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'properties_types': properties_types,
                'customers': customers,
                'is_iframe': is_iframe,
            }
        
        return render(request, 'bom_sys/properties_type_list.html', context)
    
    def post(self, request):
        """处理物料属性批量保存请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return JsonResponse({'status': 'error', 'message': '未登录'}, status=401)
        
        try:
            # 解析JSON数据
            data = json.loads(request.body)
            properties_data = data.get('properties', [])
            
            # 验证前缀编码唯一性和格式
            prefix_codes = []
            for prop in properties_data:
                prefix_code = prop.get('code')
                
                # 验证前缀编码是否为两位数字
                if not re.match(r'^\d{2}$', prefix_code):
                    return JsonResponse({
                        'status': 'error', 
                        'message': f'前缀编码 "{prefix_code}" 必须是两位数字'
                    }, status=400)
                
                # 检查前缀编码是否重复
                if prefix_code in prefix_codes:
                    return JsonResponse({
                        'status': 'error', 
                        'message': f'前缀编码 "{prefix_code}" 重复'
                    }, status=400)
                
                prefix_codes.append(prefix_code)
                
                # 验证中缀规则
                rule = prop.get('rule')
                if rule != '客户码' and not (rule.isdigit() and len(rule) == 3):
                    return JsonResponse({
                        'status': 'error', 
                        'message': f'中缀规则必须是"客户码"或3位数字，当前值: {rule}'
                    }, status=400)
            
            # 保存所有数据
            for prop in properties_data:
                prop_id = prop.get('id')
                prop_name = prop.get('name')
                prop_code = prop.get('code')
                prop_rule = prop.get('rule')
                prop_order = prop.get('order', 0)
                
                if prop_id:  # 更新现有记录
                    property_type = PropertiesType.objects.get(propertiestype_id=prop_id)
                    property_type.propertiestype_name = prop_name
                    property_type.propertiestype_code = prop_code
                    property_type.propertiesType_Rule = prop_rule
                    property_type.propertiestype_order = prop_order
                    property_type.save()
                else:  # 创建新记录
                    PropertiesType.objects.create(
                        propertiestype_name=prop_name,
                        propertiestype_code=prop_code,
                        propertiesType_Rule=prop_rule,
                        propertiestype_order=prop_order
                    )
            
            # 处理删除的记录
            deleted_ids = data.get('deleted_ids', [])
            for del_id in deleted_ids:
                try:
                    prop = PropertiesType.objects.get(propertiestype_id=del_id)
                    prop.delete()
                except PropertiesType.DoesNotExist:
                    pass
            
            return JsonResponse({'status': 'success', 'message': '物料属性保存成功'})
        
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'保存失败: {str(e)}'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class PropertiesTypeDeleteView(View):
    """删除物料属性视图"""
    
    def post(self, request, prop_id):
        """处理删除物料属性请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return JsonResponse({'status': 'error', 'message': '未登录'}, status=401)
        
        try:
            # 获取要删除的物料属性
            prop = get_object_or_404(PropertiesType, propertiestype_id=prop_id)
            
            # 删除物料属性
            prop.delete()
            
            return JsonResponse({'status': 'success', 'message': '物料属性删除成功'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'删除失败: {str(e)}'}, status=500)


def get_max_version(request):
    """获取指定客户名称和图号的最大版本号"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '未登录'})

    # 获取请求参数
    drawing_number = request.GET.get('drawing_number', '')
    customer_name = request.GET.get('customer_name', '')
    temp_mode = request.GET.get('temp_mode', '0') == '1'  # 是否为临时模式

    if not drawing_number or not customer_name:
        return JsonResponse({'success': False, 'message': '参数不完整'})

    try:
        # 查找客户代码
        customer_code = ''
        try:
            customer = Customer.objects.get(customer_name=customer_name)
            customer_code = customer.customer_code
        except Customer.DoesNotExist:
            return JsonResponse({'success': False, 'message': '客户不存在'})

        # 根据模式查询所有匹配的物料记录
        if temp_mode:
            # 临时模式：查询所有数据（正式数据 + 临时数据）
            materials = Material.objects.filter(
                material_customer=customer_code,
                material_drawingno=drawing_number
            )
            print(f"临时模式查询版本：客户={customer_code}, 图号={drawing_number}, 找到{materials.count()}条记录")
        else:
            # 正式模式：只查询正式数据
            materials = Material.objects.filter(
                material_tempstate=0,
                material_customer=customer_code,
                material_drawingno=drawing_number
            )
        
        if not materials.exists():
            # 如果没有找到匹配的记录，返回默认版本号
            return JsonResponse({'success': True, 'max_version': '01'})
        
        # 获取所有版本号
        versions = [m.material_version for m in materials if m.material_version]
        print(f"找到的所有版本号: {versions}")

        if not versions:
            # 如果没有有效的版本号，返回默认版本号
            print("没有找到有效版本号，返回默认版本01")
            return JsonResponse({'success': True, 'max_version': '01'})

        # 分离字母版本和数字版本
        letter_versions = [v for v in versions if v.isalpha() and len(v) == 1]
        number_versions = [v for v in versions if v.isdigit()]
        print(f"字母版本: {letter_versions}, 数字版本: {number_versions}")

        # 确定下一个版本号
        next_version = '01'  # 默认值

        # 优先使用字母版本，因为字母版本大于数字版本
        if letter_versions:
            max_letter = max(letter_versions)
            char_code = ord(max_letter)
            if char_code < 90:  # Z的ASCII码是90
                next_version = chr(char_code + 1)
            else:
                next_version = 'A'  # 如果是Z，循环回A
            print(f"使用字母版本，最大版本: {max_letter}, 下一版本: {next_version}")
        # 如果没有字母版本，使用数字版本
        elif number_versions:
            # 转换为整数进行比较
            max_number = max([int(v) for v in number_versions])
            next_number = max_number + 1
            next_version = str(next_number).zfill(2)  # 格式化为两位数
            if next_number > 99:
                next_version = '01'  # 如果超过99，循环回01
            print(f"使用数字版本，最大版本: {max_number}, 下一版本: {next_version}")

        print(f"最终返回的版本号: {next_version}")
        return JsonResponse({'success': True, 'max_version': next_version})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


# 获取SMB基础路径
def get_smb_base_path():
    try:
        # 尝试从Config表获取共享路径
        config = Config.objects.first()
        if config and config.config_smbpath:
            smb_path = config.config_smbpath.rstrip('/')  # 去除末尾可能存在的斜杠
            return smb_path
    except Exception as e:
        print(f"获取SMB路径出错: {str(e)}")
    
    return ""


# 实用函数：生成文件路径并确保目录存在
def generate_file_path(smb_base, material_customer, material_customername, material_attr, material_attrname, 
                      material_no, material_drawingno, material_name, material_version, file_type, file_ext=None):
    """
    根据规则生成文件路径，并确保目录存在
    
    参数:
        smb_base: SMB基础路径
        material_customer: 客户代码
        material_customername: 客户名称
        material_attr: 物料属性代码
        material_attrname: 物料属性名称
        material_no: 物料编码
        material_drawingno: 图号
        material_name: 物料名称
        material_version: 版本
        file_type: 文件类型，可选值: 'customer', 'finished', 'workmanship', 'testingform', 'datafolder'
        file_ext: 文件扩展名，如 '.pdf', '.xlsx'，对于资料文件夹不需要
        
    返回:
        完整的文件路径
    """
    # 定义清理文件名的函数，移除不允许的特殊字符
    def clean_filename(name):
        if name is None:
            return ""
        # 替换 Windows 文件系统不允许的特殊字符
        invalid_chars = r'"*<>?\\|/:+'
        for char in invalid_chars:
            name = name.replace(char, "_")
        return name if name else "_"
    
    # 清理所有路径组件
    material_customer = clean_filename(material_customer)
    material_customername = clean_filename(material_customername)
    material_attr = clean_filename(material_attr)
    material_attrname = clean_filename(material_attrname)
    material_no = clean_filename(material_no)
    material_drawingno = clean_filename(material_drawingno)
    material_name = clean_filename(material_name)
    material_version = clean_filename(material_version)
    
 
    
    # 物料编码后四位
    material_no_suffix = material_no[-4:] if len(material_no) >= 4 else material_no
    
    # 根据文件类型确定路径
    if file_type == 'customer':
        # 客户图纸
        dir_path = f"{smb_base}/(01) 客户图纸/({material_customer}) {material_customername}/({material_attr}) {material_attrname}"
        filename = f"{material_no_suffix} {material_drawingno} {material_name} {material_version}{file_ext}"
        full_path = f"{dir_path}/{filename}"
    elif file_type == 'finished':
        # 成品图纸
        dir_path = f"{smb_base}/(02) 成品图纸"
        filename = f"{material_no} {material_name} {material_version}{file_ext}"
        full_path = f"{dir_path}/{filename}"
    elif file_type in ['workmanship', 'testingform']:
        # 工艺图纸或检测表单
        if file_type == 'workmanship':
            dir_path = f"{smb_base}/(03) 工艺图纸"
        else:
            dir_path = f"{smb_base}/(04) 检测表单"
        
        # 检查是否有同名文件，并确定顺序码
        seq_num = "01"  # 默认顺序码
        
        # 确保目录存在，以便我们可以检查其中的文件
        os.makedirs(dir_path, exist_ok=True)
        
        # 获取目录中的文件列表
        base_filename = f"{material_no} {material_name} {material_version}"
        existing_files = [f for f in os.listdir(dir_path) if f.startswith(base_filename) and os.path.isfile(os.path.join(dir_path, f))]
        
        if existing_files:
            # 提取现有文件的顺序码，找出最大值
            seq_nums = []
            for file in existing_files:
                # 尝试提取顺序码
                seq_part = file.split('-')
                if len(seq_part) > 1:
                    try:
                        seq_num_str = seq_part[1].split('.')[0]  # 提取扩展名前的部分
                        if seq_num_str.isdigit() and len(seq_num_str) == 2:
                            seq_nums.append(int(seq_num_str))
                    except (IndexError, ValueError):
                        pass
            
            # 如果找到了顺序码，增加最大值
            if seq_nums:
                max_seq = max(seq_nums)
                seq_num = f"{max_seq + 1:02d}"  # 格式化为两位数
        
        # 构建文件名
        filename = f"{material_no} {material_name} {material_version}-{seq_num}{file_ext}"
        full_path = f"{dir_path}/{filename}"
    
    elif file_type == 'datafolder':
        # 资料文件夹
        dir_path = f"{smb_base}/(05) 资料文件/({material_customer}) {material_customername}/({material_attr}) {material_attrname}/{material_no_suffix} {material_drawingno} {material_name}/{material_version}/"
        full_path = dir_path  # 对于资料文件夹，路径就是目标路径
    else:
        raise ValueError(f"未知的文件类型: {file_type}")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(full_path), exist_ok=True)
    
    return full_path


def view_file(request, file_type, material_id):
    """
    在新窗口打开文件预览
    
    参数:
        file_type: 文件类型，可选值: 'customer', 'finished', 'workmanship', 'testingform'
        material_id: 物料ID
    """
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return redirect('login')
        
    try:
        # 获取物料信息
        material = Material.objects.get(material_id=material_id)
        
        # 根据文件类型获取相应的文件路径
        file_path = None
        if file_type == 'customer':
            file_path = material.material_drawing_customer
        elif file_type == 'finished':
            file_path = material.material_drawing_finished
        elif file_type == 'workmanship':
            file_path = material.material_drawing_workmanship
        elif file_type == 'testingform':
            file_path = material.material_drawing_testingform
        else:
            return HttpResponse("未知的文件类型", status=400)
            
        if not file_path:
            return HttpResponse("文件不存在", status=404)
            
        # 获取SMB基础路径
        smb_base = get_smb_base_path()
        
        # 如果是相对路径，拼接基础路径
        if not os.path.isabs(file_path) and not file_path.startswith('http'):
            full_path = os.path.join(smb_base, file_path)
        else:
            full_path = file_path
            
        # 检查文件是否存在
        if not os.path.exists(full_path):
            return HttpResponse("文件不存在或无法访问", status=404)
            
        # 读取文件内容
        file_content = None
        content_type = None
        
        # 获取文件名
        filename = os.path.basename(full_path)
        
        # 根据文件扩展名确定内容类型
        file_ext = os.path.splitext(full_path)[1].lower()
        if file_ext == '.pdf':
            content_type = 'application/pdf'
            # PDF文件使用内联方式预览
            encoded_filename = urllib.parse.quote(filename)
            disposition = f'inline; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'
        elif file_ext in ['.xls', '.xlsx']:
            # Excel文件设置为下载方式
            if file_ext == '.xlsx':
                content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            else:
                content_type = 'application/vnd.ms-excel'
            # 设置为下载模式，使用从路径中提取的文件名
            # 对于中文文件名，需要特殊处理
            # 1. 提供一个简单的ASCII文件名作为后备
            ascii_filename = f"download{file_ext}"
            # 2. 使用RFC 5987编码提供UTF-8文件名
            encoded_filename = urllib.parse.quote(filename)
            disposition = f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        else:
            content_type = 'application/octet-stream'
            encoded_filename = urllib.parse.quote(filename)
            disposition = f'inline; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'
            
        # 读取文件
        with open(full_path, 'rb') as f:
            file_content = f.read()
            
        # 返回文件内容
        response = HttpResponse(file_content, content_type=content_type)
        response['Content-Disposition'] = disposition
        return response
        
    except Material.DoesNotExist:
        return HttpResponse("物料不存在", status=404)
    except Exception as e:
        return HttpResponse(f"发生错误: {str(e)}", status=500)


def get_smb_base_path_api(request):
    """
    API接口：获取SMB基础路径
    返回JSON格式的SMB基础路径
    """
    smb_base = get_smb_base_path()
    return JsonResponse({'success': True, 'smb_base': smb_base})


def generate_datafolder_path_api(request):
    """
    API接口：生成资料文件夹路径并确保目录存在
    参数：
        material_customer: 客户代码
        material_customername: 客户名称
        material_attr: 物料属性代码
        material_attrname: 物料属性名称
        material_no: 物料编码
        material_drawingno: 图号
        material_name: 物料名称
        material_version: 版本
    返回：
        JSON格式的资料文件夹路径
    """
    try:
        # 获取请求参数
        material_customer = request.GET.get('material_customer', '')
        material_customername = request.GET.get('material_customername', '')
        material_attr = request.GET.get('material_attr', '')
        material_attrname = request.GET.get('material_attrname', '')
        material_no = request.GET.get('material_no', '')
        material_drawingno = request.GET.get('material_drawingno', '')
        material_name = request.GET.get('material_name', '')
        material_version = request.GET.get('material_version', '')
        
        # 检查必要参数
        if not all([material_customer, material_customername, material_attr, 
                   material_attrname, material_no, material_drawingno, 
                   material_name, material_version]):
            return JsonResponse({
                'success': False, 
                'message': '缺少必要参数'
            })
        
        # 获取SMB基础路径
        smb_base = get_smb_base_path()
        
        # 生成资料文件夹路径并确保目录存在
        folder_path = generate_file_path(
            smb_base=smb_base,
            material_customer=material_customer,
            material_customername=material_customername,
            material_attr=material_attr,
            material_attrname=material_attrname,
            material_no=material_no,
            material_drawingno=material_drawingno,
            material_name=material_name,
            material_version=material_version,
            file_type='datafolder'
        )
        
        # 构建universalLink格式的路径
        universal_link = f"universalLink://{folder_path}"
        
        return JsonResponse({
            'success': True, 
            'folder_path': folder_path,
            'universal_link': universal_link
        })
    except Exception as e:
        return JsonResponse({
            'success': False, 
            'message': str(e)
        })


class MaterialQueryView(View):
    """物料查询视图"""
    
    def get(self, request):
        """显示物料查询页面"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
            
        # 获取查询参数
        drawing_number = request.GET.get('drawing_number', '')
        material_name = request.GET.get('material_name', '')
        material_code = request.GET.get('material_code', '')
        material_category = request.GET.get('material_category', '')
        customer_id = request.GET.get('customer_id', '')
        semifinished_status = request.GET.get('semifinished_status', '')
        material_state = request.GET.get('material_state', '1')  # 默认查询启用状态的物料
        
        # 构建查询条件
        query = Q()
        if drawing_number:
            query &= Q(material_drawingno__icontains=drawing_number)
        if material_name:
            query &= Q(material_name__icontains=material_name)
        if material_code:
            query &= Q(material_no__icontains=material_code)
        if material_category:
            # 支持通过属性代码或属性名称搜索
            query &= (Q(material_attr=material_category) | Q(material_attrname__icontains=material_category))
        if customer_id:
            # 尝试通过客户代码查找
            query &= Q(material_customer=customer_id)
        if semifinished_status:
            query &= Q(material_semistate=semifinished_status)
        # 当material_state为"-1"时表示查询全部状态，不添加状态筛选条件
        if material_state and material_state != '-1':
            query &= Q(material_state=material_state)
        
        query &= Q(material_tempstate=0)
        # 执行查询
        materials = Material.objects.filter(query)
        
        # 定义版本号排序函数
        def get_version_sort_key(version):
            if not version:
                return (0, 0)  # 无版本号排在最前面
            
            if version.isalpha() and len(version) == 1:
                # 字母版本，使用ASCII码的负值表示降序
                return (-ord(version), 0)
            elif version.isdigit():
                # 数字版本，使用负值表示降序
                return (0, -int(version))
            else:
                # 其他格式的版本号
                return (0, 0)
        
        # 自定义排序：先按中缀（中间三位）、再按前缀（前两位）、再按后缀（后四位），最后按版本降序
        def material_sort_key(material):
            # 先按物料编码排序
            prefix = material.material_no[0:2] if material.material_no and len(material.material_no) >= 2 else ''
            middle = material.material_no[2:5] if material.material_no and len(material.material_no) >= 5 else ''
            suffix = material.material_no[5:9] if material.material_no and len(material.material_no) >= 9 else ''
            
            # 按照指定顺序返回排序键
            return (middle, prefix, suffix, get_version_sort_key(material.material_version))
        
        # 应用排序
        materials = sorted(materials, key=material_sort_key)
        
        # 分页处理
        page = request.GET.get('page', 1)
        per_page = request.GET.get('per_page', 10)  # 每页显示的记录数
        
        paginator = Paginator(materials, per_page)
        try:
            materials_page = paginator.page(page)
        except PageNotAnInteger:
            materials_page = paginator.page(1)
        except EmptyPage:
            materials_page = paginator.page(paginator.num_pages)
        
        # 获取客户列表，用于筛选
        customers = Customer.objects.all().order_by('customer_order')
            
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'materials': materials_page,
                'customers': customers,
                # 保存查询条件，用于分页时保持查询参数
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_category': material_category,
                    'customer_id': customer_id,
                    'semifinished_status': semifinished_status,
                    'material_state': material_state,
                    'per_page': per_page
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'materials': materials_page,
                'customers': customers,
                # 保存查询条件，用于分页时保持查询参数
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_category': material_category,
                    'customer_id': customer_id,
                    'semifinished_status': semifinished_status,
                    'material_state': material_state,
                    'per_page': per_page
                }
            }
        
        return render(request, 'bom_sys/material_query.html', context)


class BomListView(View):
    """BOM清单管理视图"""
    
    def get(self, request):
        """显示BOM清单管理页面"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
            
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有客户信息，按排序字段排序
        customers = Customer.objects.all().order_by('customer_order', 'customer_id')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'customers': customers,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'customers': customers,
                'is_iframe': is_iframe,
            }
        return render(request, 'bom_sys/bom_list.html', context)


def get_customer_bom(request):
    """获取客户BOM数据的API"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 获取客户代码参数
    customer_code = request.GET.get('customer_code', '')
    
    if not customer_code:
        return JsonResponse({'success': False, 'message': '缺少客户代码参数'}, status=400)
    
    # 判断是否为临时数据模式（导入BOM）
    is_temp = request.GET.get('is_temp', 'false').lower() == 'true'
    
    # 获取当前用户ID
    user_id = request.session.get('user_id')
    
    try:
        # 查询客户信息
        customer = Customer.objects.get(customer_code=customer_code)
        
        # 根据不同模式设置不同的查询条件
        # 临时数据模式使用临时数据(state>0)，正常模式使用正式数据(state=0)
        bom_state = None
        
        # 使用连接查询直接获取满足条件的顶级BOM记录
        top_boms_query = Bom.objects.filter(
            bom_pid=0,  # 顶级节点
            bom_material__isnull=False  # 确保有关联物料
        )
        
        # 根据模式设置不同的状态查询条件
        if is_temp:
            # 临时数据模式：查询状态为1(新增)或2(修改)的数据
            top_boms_query = top_boms_query.filter(bom_state__gt=0)
            # 如果有用户ID，则只查询当前用户导入的数据
            if user_id:
                top_boms_query = top_boms_query.filter(bom_ImportUser=user_id)
        else:
            # 正常模式：查询状态为0(正式)的数据
            top_boms_query = top_boms_query.filter(bom_state=0)
        
        top_boms = top_boms_query.order_by('bom_id')  # 添加排序保证结果一致性
        
        # 打印调试信息，查看获取的BOM记录
        print('customer_code:', customer_code)
        print('is_temp:', is_temp)
        print('user_id:', user_id)
        print('top_boms count:', top_boms.count())
        
        # 构建BOM树形结构
        bom_tree = []
        
        for top_bom in top_boms:
            # 检查关联的物料是否属于当前客户，如果不是则跳过
            try:
                # 根据模式选择查询条件
                material_query = Q(material_id=top_bom.bom_material)
                if is_temp:
                    # 临时数据模式：查询临时物料(material_tempstate != 0)
                    material_query &= ~Q(material_tempstate=0)
                    # 如果有用户ID，则只查询当前用户导入的临时物料
                    if user_id:
                        material_query &= Q(material_tempUserId=user_id)
                else:
                    # 正常模式：查询正式物料(material_tempstate = 0)
                    material_query &= Q(material_tempstate=0)
                
                material = Material.objects.get(material_query)
                
                # 如果物料的客户代码与当前客户代码不匹配，则跳过
                if material.material_customer != customer_code:
                    continue
                    
                # 构建顶级节点
                node = {
                    'id': f'bom_{top_bom.bom_id}',
                    'type': 'series',
                    'name': material.material_name if material else '',
                    'drawingNumber': material.material_drawingno if material else '',
                    'materialName': material.material_name if material else '',
                    'customerName': material.material_customername if material else '',
                    'customerCode': material.material_customer if material else '',
                    'materialCode': f'BOM-{top_bom.bom_id}',  # 默认编码
                    'version': material.material_version if material else 'A',
                    'materialCategory': material.material_attrname if material else '未分类',
                    'materialAttr': material.material_attr if material else '',
                    'materialId': top_bom.bom_material or '',
                    'materialNo': material.material_no if material else '',
                    'materialTempState': material.material_tempstate,  # 添加临时状态字段
                    'expanded': False,
                    'children': get_child_boms(top_bom.bom_id, is_temp, user_id if is_temp else None)
                }
                
                bom_tree.append(node)
            except Material.DoesNotExist:
                # 如果关联的物料不存在，则跳过
                continue
        
        # 返回JSON格式的BOM树形结构
        return JsonResponse({
            'success': True, 
            'customer': {
                'id': customer.customer_id,
                'name': customer.customer_name,
                'code': customer.customer_code
            },
            'bom_tree': bom_tree,
            'is_temp': is_temp
        })
        
    except Customer.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到客户代码为 {customer_code} 的客户'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)


def get_child_boms(parent_id, is_temp=False, user_id=None):
    """递归获取子BOM数据"""
    children = []
    
    # 根据不同模式设置不同的查询条件
    # 临时数据模式使用临时数据(state>0)，正常模式使用正式数据(state=0)
    bom_state = None  # 初始化为None，后面会根据is_temp设置具体的查询条件
    
    # 构建查询条件
    query = Q(bom_pid=parent_id)
    
    # 根据模式设置不同的状态查询条件
    if is_temp:
        # 临时数据模式：查询状态为1(新增)或2(修改)的数据
        query &= Q(bom_state__gt=0)
        # 如果有用户ID，则只查询当前用户导入的数据
        if user_id:
            query &= Q(bom_ImportUser=user_id)
    else:
        # 正常模式：查询状态为0(正式)的数据
        query &= Q(bom_state=0)
    
    child_boms = Bom.objects.filter(query)
    
    for child_bom in child_boms:
        # 获取关联的物料数据
        material = None
        if child_bom.bom_material:
            try:
                # 根据模式选择查询条件
                material_query = Q(material_id=child_bom.bom_material)
                if is_temp:
                    # 临时数据模式：查询临时物料(material_tempstate != 0)
                    material_query &= ~Q(material_tempstate=0)
                    # 如果有用户ID，则只查询当前用户导入的临时物料
                    if user_id:
                        material_query &= Q(material_tempUserId=user_id)
                else:
                    # 正常模式：查询正式物料(material_tempstate = 0)
                    material_query &= Q(material_tempstate=0)
                
                material = Material.objects.get(material_query)
            except Material.DoesNotExist:
                material = None
        
        if material:
            # 构建子节点
            node = {
                'id': f'bom_{child_bom.bom_id}',
                'type': 'material' if child_bom.bom_level > 1 else 'series',
                'name': material.material_name if material else '',
                'drawingNumber': material.material_drawingno if material else '',
                'materialName': material.material_name if material else '',
                'customerName': material.material_customername if material else '',
                'customerCode': material.material_customer if material else '',
                'materialCode': f'BOM-{child_bom.bom_id}',  # 默认编码
                'version': material.material_version if material else 'A',
                'materialCategory': material.material_attrname if material else '未分类',
                'materialAttr': material.material_attr if material else '',
                'materialId': child_bom.bom_material or '',
                'materialNo': material.material_no if material else '',
                'materialTempState': material.material_tempstate,  # 添加临时状态字段
                'expanded': False,
            }
            
            # 递归获取子节点
            # 构建子节点查询条件
            has_children_query = Q(bom_pid=child_bom.bom_id)
            
            # 根据模式设置不同的状态查询条件
            if is_temp:
                # 临时数据模式：查询状态为1(新增)或2(修改)的数据
                has_children_query &= Q(bom_state__gt=0)
                # 如果有用户ID，则只查询当前用户导入的数据
                if user_id:
                    has_children_query &= Q(bom_ImportUser=user_id)
            else:
                # 正常模式：查询状态为0(正式)的数据
                has_children_query &= Q(bom_state=0)
            
            has_children = Bom.objects.filter(has_children_query).exists()
            if has_children:
                node['children'] = get_child_boms(child_bom.bom_id, is_temp, user_id)
            
            children.append(node)
    
    return children


@csrf_exempt
def delete_bom_node(request):
    """删除BOM节点及其所有子节点，但不删除关联的物料数据"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 检查是否为POST请求
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'}, status=405)
    
    # 获取BOM ID
    bom_id = request.POST.get('bom_id')
    if not bom_id:
        return JsonResponse({'success': False, 'message': '缺少BOM ID参数'}, status=400)
    
    try:
        # 查找要删除的BOM节点
        bom_node = Bom.objects.get(bom_id=bom_id)
        
        # 递归删除所有子节点
        def delete_children(parent_id):
            children = Bom.objects.filter(bom_pid=parent_id, bom_state=0)  # 只查询状态为正式的BOM
            for child in children:
                # 递归删除子节点的子节点
                delete_children(child.bom_id)
                # 删除子节点
                child.delete()
        
        # 执行递归删除
        delete_children(bom_id)
        
        # 最后删除当前节点
        bom_node.delete()
        
        return JsonResponse({'success': True, 'message': 'BOM节点及其子节点已成功删除'})
    
    except Bom.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到ID为 {bom_id} 的BOM节点'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'删除BOM节点失败: {str(e)}'}, status=500)


@csrf_exempt
def search_materials_by_customer(request):
    """根据客户代码和关键词搜索物料"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 获取参数
    customer_code = request.GET.get('customer_code', '')
    keyword = request.GET.get('keyword', '')
    page = int(request.GET.get('page', '1'))
    page_size = int(request.GET.get('page_size', '10'))
    
    try:
        # 构建查询条件
        query = Q(material_customer=customer_code)&Q(material_tempstate=0)
        
        # 如果有关键词，添加模糊搜索条件
        if keyword:
            query &= (
                Q(material_no__icontains=keyword) | 
                Q(material_drawingno__icontains=keyword) | 
                Q(material_name__icontains=keyword)
            )
        
        query &= Q(material_tempstate=0)
        # 查询物料数据
        materials_query = Material.objects.filter(query).order_by('material_no')
        
        # 计算总记录数
        total_count = materials_query.count()
        
        # 计算分页信息
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        
        # 获取当前页的数据
        materials = materials_query[start_index:end_index].values(
            'material_id', 'material_no', 'material_drawingno', 
            'material_name', 'material_version', 'material_state'
        )
        
        # 转换为列表
        materials_list = list(materials)
        
        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size
        
        return JsonResponse({
            'success': True, 
            'materials': materials_list,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': total_pages
            }
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)


@csrf_exempt
def bind_material_to_bom(request):
    """将物料绑定到BOM节点"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 检查是否为POST请求
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'}, status=405)
    
    # 获取参数
    bom_id = request.POST.get('bom_id')
    material_id = request.POST.get('material_id')
    
    # 参数验证
    if not bom_id:
        return JsonResponse({'success': False, 'message': '缺少BOM ID参数'}, status=400)
    if not material_id:
        return JsonResponse({'success': False, 'message': '缺少物料ID参数'}, status=400)
    
    try:
        # 查询BOM记录
        bom = Bom.objects.get(bom_id=bom_id)
        
        # 查询物料记录
        material = Material.objects.get(material_id=material_id)
        
        # 更新BOM记录的物料关联
        bom.bom_material = material.material_id
        # 更新BOM修改时间
        bom.bom_updateTime = get_local_now()
        # 保存更新
        bom.save()
        
        return JsonResponse({'success': True, 'message': '物料绑定成功'})
        
    except Bom.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到ID为 {bom_id} 的BOM记录'}, status=404)
    except Material.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到ID为 {material_id} 的物料记录'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'绑定物料失败: {str(e)}'}, status=500)


class ImportBOMView(TemplateView):
    """
    导入BOM视图
    """
    template_name = 'bom_sys/import_bom.html'
    
    def get(self, request, *args, **kwargs):
        """
        重写get方法，检查当前用户是否有临时数据
        如果有临时数据，直接跳转到预览页面
        """
        # 检查是否已登录
        if not request.session.get('is_login', False):
            messages.error(request, '请先登录')
            return redirect('login')
            
        # 获取当前用户ID
        user_id = request.session.get('user_id')
        
        if user_id:
            # 检查是否存在当前用户的临时数据
            from .models import Bom, Material, Customer
            
            # 检查是否有临时BOM数据
            has_temp_bom = Bom.objects.filter(bom_state__gt=0, bom_ImportUser=user_id).exists()
            
            # 检查是否有临时物料数据
            has_temp_material = Material.objects.filter(material_tempstate__gt=0, material_tempUserId=user_id).exists()
            
            if has_temp_bom or has_temp_material:
                # 如果有临时数据，需要找到对应的客户ID
                # 首先尝试从临时物料数据中获取客户代码
                temp_material = Material.objects.filter(material_tempstate__gt=0, material_tempUserId=user_id).first()
                
                if temp_material and temp_material.material_customer:
                    try:
                        # 根据客户代码查找客户ID
                        customer = Customer.objects.get(customer_code=temp_material.material_customer)
                        customer_id = customer.customer_id
                        
                        # 重定向到预览页面
                        return redirect('bom_import_preview', customer_id=customer_id)
                    except Customer.DoesNotExist:
                        # 如果找不到客户，显示错误信息
                        messages.warning(request, '发现临时数据，但无法确定对应的客户信息')
                else:
                    # 如果没有物料数据或物料数据没有客户信息，显示警告
                    messages.warning(request, '发现临时数据，但无法确定对应的客户信息')
        
        # 如果没有临时数据或无法确定客户信息，正常显示导入页面
        return super().get(request, *args, **kwargs)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .models import Customer
        context['customers'] = Customer.objects.all().order_by('customer_order', 'customer_name')

        # 清除之前可能存在的导入错误信息，避免在新的导入操作中显示旧的错误
        if 'import_errors' in self.request.session:
            del self.request.session['import_errors']

        return context
    
    def post(self, request, *args, **kwargs):
        from .models import Customer
        import os
        import uuid
        from django.conf import settings
        from django.shortcuts import redirect
        from .import_utils import parse_bom_excel, delete_temp_bom_data
        from django.db import connection
        
        # 检查是否已登录
        if not request.session.get('is_login', False):
            messages.error(request, '请先登录')
            return redirect('login')
            
        # 获取当前用户ID
        user_id = request.session.get('user_id')
        
        # 获取客户ID
        customer_id = request.POST.get('customer')
        if not customer_id:
            messages.error(request, '请选择客户')
            context = self.get_context_data()
            context['close_loading'] = True  # 添加关闭等待框的标记
            return self.render_to_response(context)
        
        # 获取BOM文件
        bom_file = request.FILES.get('bom_file')
        if not bom_file:
            messages.error(request, '请选择BOM文件')
            context = self.get_context_data()
            context['close_loading'] = True  # 添加关闭等待框的标记
            return self.render_to_response(context)
        
        # 检查文件扩展名
        file_ext = os.path.splitext(bom_file.name)[1].lower()
        if file_ext not in ['.xls', '.xlsx']:
            messages.error(request, '请上传Excel格式(.xls, .xlsx)的BOM文件')
            context = self.get_context_data()
            context['close_loading'] = True  # 添加关闭等待框的标记
            return self.render_to_response(context)
        
        try:
            # 创建临时文件路径
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp_uploads')
            os.makedirs(temp_dir, exist_ok=True)
            
            # 生成唯一文件名
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            temp_file_path = os.path.join(temp_dir, unique_filename)
            
            # 保存上传的文件
            with open(temp_file_path, 'wb+') as destination:
                for chunk in bom_file.chunks():
                    destination.write(chunk)
            
            # 确保数据库连接是活跃的
            connection.close()
            connection.connect()
            
            # 清除之前的临时数据
            delete_temp_bom_data(user_id)
            
            # 解析Excel文件并导入数据
            success_count, fail_count, error_messages = parse_bom_excel(temp_file_path, customer_id, user_id)
            
            # 删除临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            
            # 确保数据库连接仍然活跃
            connection.close()
            connection.connect()
            
            if error_messages:
                # 直接将所有错误信息添加到session中，不再通过messages框架显示部分错误
                # 如果还有大量错误信息需要在页面上显示，应该通过专门的区域展示
                request.session['import_errors'] = error_messages

                # 在页面顶部只显示一条汇总错误消息
                messages.error(request, f'导入过程中发生 {len(error_messages)} 个错误，请查看详细错误信息。')

                # 如果所有行都失败，则返回导入页面
                if success_count == 0:
                    context = self.get_context_data()
                    context['close_loading'] = True  # 添加关闭等待框的标记
                    context['error_details'] = error_messages  # 添加详细错误信息
                    return self.render_to_response(context)
            else:
                # 如果没有错误信息，清除session中之前可能存在的错误信息
                if 'import_errors' in request.session:
                    del request.session['import_errors']

            # 记录导入结果
            request.session['import_results'] = {
                'success_count': success_count,
                'fail_count': fail_count,
                'total_count': success_count + fail_count
            }

            # 重定向到导入预览页面
            return redirect('bom_import_preview', customer_id=customer_id)
            
        except Exception as e:
            # 确保数据库连接仍然活跃
            try:
                connection.close()
                connection.connect()
            except:
                pass
                
            error_msg = f'导入过程发生错误: {str(e)}'
            messages.error(request, error_msg)
            context = self.get_context_data()
            context['close_loading'] = True  # 添加关闭等待框的标记
            context['error_details'] = [error_msg]  # 添加详细错误信息
            return self.render_to_response(context)


class BOMImportPreviewView(TemplateView):
    """
    导入BOM预览视图
    """
    template_name = 'bom_sys/bom_import_preview.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        customer_id = self.kwargs.get('customer_id')
        
        from .models import Customer, Bom, Material
        from django.db import connection
        
        try:
            # 确保数据库连接是活跃的
            connection.close()
            connection.connect()
            
            customer = Customer.objects.get(pk=customer_id)
            context['selected_customer'] = customer
            context['import_mode'] = True
            context['is_preview'] = True  # 标记为预览模式
            
            # 获取导入结果
            import_results = self.request.session.get('import_results', {})
            success_count = import_results.get('success_count', 0)
            fail_count = import_results.get('fail_count', 0)
            total_count = import_results.get('total_count', 0)
            
            # 如果session中没有导入结果（可能是直接进入预览页面），则根据数据库中的临时数据计算
            if not import_results:
                # 获取当前用户ID
                user_id = self.request.session.get('user_id')
                
                if user_id:
                    # 计算成功导入的数量（临时BOM数据的数量，包括新增和修改）
                    success_count = Bom.objects.filter(bom_state__gt=0, bom_ImportUser=user_id).count()
                    
                    # 从session中获取失败数量，如果没有则默认为0
                    fail_count = self.request.session.get('import_errors', [])
                    if isinstance(fail_count, list):
                        fail_count = len(fail_count)
                    else:
                        fail_count = 0
                    
                    # 计算总数
                    total_count = success_count + fail_count
            
            # 确保这些变量始终存在，即使没有导入结果
            context['success_count'] = success_count if 'success_count' in locals() else 0
            context['fail_count'] = fail_count if 'fail_count' in locals() else 0
            context['total_count'] = total_count if 'total_count' in locals() else 0
            
            # 获取导入错误信息
            if 'import_errors' in self.request.session:
                context['error_details'] = self.request.session['import_errors']
                # 从session中删除错误信息，避免重复显示
                del self.request.session['import_errors']
            
            # 获取所有客户列表用于初始化树结构
            context['customers'] = Customer.objects.all().order_by('customer_order', 'customer_name')
            
            # 获取当前用户ID
            user_id = self.request.session.get('user_id')
            
            # 查询临时导入的顶级BOM节点（父ID为0，状态为临时数据，且为当前用户导入的数据）
            top_boms_query = Bom.objects.filter(bom_pid=0, bom_state__gt=0)
            if user_id:
                top_boms_query = top_boms_query.filter(bom_ImportUser=user_id)
                
            top_boms = top_boms_query
            if top_boms.exists():
                context['has_preview_data'] = True
                
                # 获取第一个顶级节点的物料信息
                top_bom = top_boms.first()
                if top_bom.bom_material:
                    try:
                        top_material = Material.objects.get(material_id=top_bom.bom_material)
                        context['top_material_name'] = top_material.material_name
                        context['top_material_no'] = top_material.material_no
                        context['top_material_version'] = top_material.material_version
                    except Material.DoesNotExist:
                        pass
            else:
                context['has_preview_data'] = False
            
            # 查询物料数据的统计信息，只统计当前用户导入的数据
            materials_query_new = Material.objects.filter(material_tempstate=1)
            materials_query_updated = Material.objects.filter(material_tempstate=2)
            
            if user_id:
                materials_query_new = materials_query_new.filter(material_tempUserId=user_id)
                materials_query_updated = materials_query_updated.filter(material_tempUserId=user_id)
            
            new_materials = materials_query_new.count()
            updated_materials = materials_query_updated.count()
            context['new_materials'] = new_materials
            context['updated_materials'] = updated_materials
            
            # 额外标记，在前端JS中使用
            context['is_import_preview'] = True
            context['preview_customer_code'] = customer.customer_code
            
        except Customer.DoesNotExist:
            messages.error(self.request, '客户不存在')
        except Exception as e:
            # 处理其他异常
            messages.error(self.request, f'加载预览数据时出错: {str(e)}')
            # 确保数据库连接仍然活跃
            try:
                connection.close()
                connection.connect()
            except:
                pass
        
        return context

    def post(self, request, *args, **kwargs):
        """处理导入预览页面的表单提交，例如确认导入"""
        customer_id = self.kwargs.get('customer_id')
        action = request.POST.get('action')
        
        if action == 'confirm_import':
            # 确认导入，将临时数据标记为正式数据
            try:
                from django.db import connection
                
                # 确保数据库连接是活跃的
                connection.close()
                connection.connect()
                
                # 获取当前用户ID
                user_id = request.session.get('user_id')
                
                with connection.cursor() as cursor:
                    # 处理BOM数据的导入逻辑
                    if user_id:
                        # 1. 获取所有当前用户导入的临时顶级BOM记录及其相关的物料信息
                        cursor.execute("""
                            SELECT b.bom_id, b.bom_material, m.material_drawingno, m.material_version, m.material_customer
                            FROM bom b
                            JOIN Material m ON b.bom_material = m.material_id
                            WHERE b.bom_pid = 0 AND b.bom_State > 0 AND b.bom_ImportUser = %s
                        """, [user_id])
                        
                        temp_top_boms = cursor.fetchall()
                        
                        # 2. 对于每个临时顶级BOM，检查是否存在相同图号、版本、客户的正式顶级BOM
                        for bom_id, material_id, drawing_no, version, customer_code in temp_top_boms:
                            if drawing_no and version and customer_code:
                                # 查找匹配的正式顶级BOM
                                cursor.execute("""
                                    SELECT b.bom_id
                                    FROM bom b
                                    JOIN Material m ON b.bom_material = m.material_id
                                    WHERE b.bom_pid = 0 
                                      AND b.bom_State = 0 
                                      AND m.material_drawingno = %s
                                      AND m.material_version = %s
                                      AND m.material_customer = %s
                                """, [drawing_no, version, customer_code])
                                
                                matching_boms = cursor.fetchall()
                                
                                # 如果找到匹配的正式BOM，删除它及其所有子项
                                for (matching_bom_id,) in matching_boms:
                                    # 递归删除所有子项
                                    def delete_bom_tree(parent_id):
                                        # 先查找所有子项
                                        cursor.execute("SELECT bom_id FROM bom WHERE bom_pid = %s", [parent_id])
                                        children = cursor.fetchall()
                                        
                                        # 递归删除每个子项
                                        for (child_id,) in children:
                                            delete_bom_tree(child_id)
                                        
                                        # 删除当前节点
                                        cursor.execute("DELETE FROM bom WHERE bom_id = %s", [parent_id])
                                    
                                    # 删除整个BOM树
                                    delete_bom_tree(matching_bom_id)
                        
                        # 3. 将临时BOM数据转换为正式数据，同时更新BOM的更新时间
                        cursor.execute("UPDATE bom SET bom_State=0, bom_UpdateTime=NOW() WHERE bom_State>0 AND bom_ImportUser=%s", [user_id])
                        
                        # 4. 将临时物料数据更改为正式数据，仅限当前用户导入的数据
                        cursor.execute("UPDATE Material SET Material_tempState=0 WHERE Material_tempState=1 AND Material_tempUserId=%s", [user_id])  # 新增的物料
                    else:
                        # 如果没有用户ID，使用简化逻辑（不检查重复，直接更新状态）
                        cursor.execute("UPDATE bom SET bom_State=0 WHERE bom_State>0")
                        cursor.execute("UPDATE Material SET Material_tempState=0 WHERE Material_tempState=1")  # 新增的物料
                    
                    # 对于要更新的物料，我们需要替换原有记录的数据
                    # 只更新当前用户导入的临时数据
                    if user_id:
                        # 首先获取所有Material_tempState=2的记录，只处理当前用户的数据
                        cursor.execute("""
                            UPDATE Material m1
                            JOIN Material m2 ON m1.Material_Id = m2.Material_tempTrueId
                            SET
                                m1.material_drawingno = m2.material_drawingno,
                                m1.material_name = m2.material_name,
                                m1.material_quality = m2.material_quality,
                                m1.material_spec = m2.material_spec,
                                m1.material_version = m2.material_version,
                                m1.material_unit = m2.material_unit,
                                m1.material_attr = m2.material_attr,
                                m1.material_attrname = m2.material_attrname,
                                m1.material_inventory = m2.material_inventory,
                                m1.material_supp_casting = m2.material_supp_casting,
                                m1.material_supp_machining = m2.material_supp_machining,
                                m1.material_supp_sheetmetal = m2.material_supp_sheetmetal,
                                m1.material_supp_purchase = m2.material_supp_purchase,
                                m1.material_supp_prochasemanager = m2.material_supp_prochasemanager,
                                m1.material_uptime = m2.material_uptime,
                                m1.Material_UpdateTime = NOW()
                            WHERE m2.Material_tempState = 2 AND m2.Material_tempUserId = %s
                        """, [user_id])
                        
                        # 重要：更新BOM表中引用临时物料ID的记录，使其指向原始物料ID
                        cursor.execute("""
                            UPDATE bom b
                            JOIN Material m ON b.bom_material = m.material_id
                            SET b.bom_material = m.material_tempTrueId
                            WHERE m.Material_tempState = 2 AND m.Material_tempUserId = %s
                        """, [user_id])
                        
                        # 删除临时更新记录，只删除当前用户的数据
                        cursor.execute("DELETE FROM Material WHERE Material_tempState = 2 AND Material_tempUserId = %s", [user_id])
                  
                messages.success(request, '导入成功！临时数据已转为正式数据。')
                return redirect('import_bom')
            
            except Exception as e:
                # 确保数据库连接仍然活跃
                try:
                    connection.close()
                    connection.connect()
                except:
                    pass
                    
                messages.error(request, f'确认导入失败：{str(e)}')
        
        elif action == 'cancel_import':
            # 取消导入，删除所有临时数据
            try:
                from django.db import connection
                # 确保数据库连接是活跃的
                connection.close()
                connection.connect()
                
                from .import_utils import delete_temp_bom_data
                # 获取当前用户ID
                user_id = request.session.get('user_id')
                delete_temp_bom_data(user_id)
                messages.info(request, '已取消导入并删除临时数据。')
                return redirect('import_bom')
            except Exception as e:
                # 确保数据库连接仍然活跃
                try:
                    connection.close()
                    connection.connect()
                except:
                    pass
                    
                messages.error(request, f'取消导入失败：{str(e)}')
        
        # 如果有其他操作或发生错误，返回预览页面
        return self.get(request, *args, **kwargs)


@csrf_exempt
def confirm_bom_import(request):
    """确认导入BOM，将临时数据转为正式数据"""
    # 检查是否已登录和是否为POST请求
    if not request.session.get('is_login', False) or request.method != 'POST':
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 获取当前用户ID
    user_id = request.session.get('user_id')
    
    try:
        # 将状态为2的BOM数据更改为状态1（仅限当前用户导入的数据）
        from django.db import connection
        with connection.cursor() as cursor:
            if user_id:
                # 只更新当前用户导入的临时BOM数据
                cursor.execute("UPDATE bom SET bom_State=1 WHERE bom_State=2 AND bom_ImportUser=%s", [user_id])
                
                # 只更新当前用户导入的临时物料数据
                cursor.execute("UPDATE Material SET Material_tempState=0 WHERE Material_tempState!=0 AND Material_tempUserId=%s", [user_id])
            else:
                # 如果没有用户ID，更新所有临时数据（保留原有行为）
                cursor.execute("UPDATE bom SET bom_State=1 WHERE bom_State=2")
                cursor.execute("UPDATE Material SET Material_tempState=0 WHERE Material_tempState!=0")
        
        # 记录导入成功日志
        log_system_action(
            request=request,
            action='确认导入BOM',
            module='BOM管理',
            description='将临时BOM数据转为正式数据'
        )

        return JsonResponse({'success': True, 'message': '导入成功！临时数据已转为正式数据。'})
    except Exception as e:
        # 记录导入失败日志
        log_system_action(
            request=request,
            action='确认导入BOM失败',
            module='BOM管理',
            description=f'导入失败：{str(e)}'
        )
        return JsonResponse({'success': False, 'message': f'确认导入失败：{str(e)}'}, status=500)


# 预览功能已合并到get_customer_bom函数中，通过is_preview参数控制

class BomListNewView(View):
    """BOM清单(新)管理视图"""
    
    def get(self, request):
        """显示BOM清单(新)管理页面，展示bom_State=0且bom_Level=0的记录"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取查询参数
        drawing_number = request.GET.get('drawing_number', '')
        material_name = request.GET.get('material_name', '')
        material_code = request.GET.get('material_code', '')
        material_version = request.GET.get('material_version', '')
        customer_name = request.GET.get('customer_name', '')
        
        # 构建查询条件
        query = Q(bom_state=0) & Q(bom_level=0)
        
        # 获取所有符合条件的BOM记录
        bom_records = Bom.objects.filter(query).order_by('bom_order', 'bom_id')  # 添加bom_order排序
        
        # 关联Material表并应用过滤条件
        materials_data = []
        for bom in bom_records:
            try:
                material = Material.objects.get(material_id=bom.bom_material)
                
                # 应用过滤条件
                if drawing_number and drawing_number.lower() not in (material.material_drawingno or '').lower():
                    continue
                if material_name and material_name.lower() not in (material.material_name or '').lower():
                    continue
                if material_code and material_code.lower() not in (material.material_no or '').lower():
                    continue
                if material_version and material_version.lower() not in (material.material_version or '').lower():
                    continue
                if customer_name and customer_name.lower() not in (material.material_customername or '').lower():
                    continue
                
                # 添加到结果列表
                materials_data.append({
                    'bom_id': bom.bom_id,
                    'material_id': material.material_id,
                    'material_drawingno': material.material_drawingno or '',
                    'material_no': material.material_no or '',
                    'material_name': material.material_name or '',
                    'material_version': material.material_version or '',
                    'customer_name': material.material_customername or '',
                    'customer_code': material.material_customer or ''
                })
            except Material.DoesNotExist:
                # 如果找不到对应物料，跳过
                continue
        
        # 分页处理
        page = request.GET.get('page', 1)
        per_page = request.GET.get('per_page', 20)  # 默认每页显示20条
        
        paginator = Paginator(materials_data, per_page)
        try:
            materials_page = paginator.page(page)
        except PageNotAnInteger:
            materials_page = paginator.page(1)
        except EmptyPage:
            materials_page = paginator.page(paginator.num_pages)
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有客户信息，用于下拉选择框
        customers = Customer.objects.all().order_by('customer_order', 'customer_id')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'materials': materials_page,
                'customers': customers,
                'is_iframe': is_iframe,
                # 保存查询条件，用于分页时保持查询参数
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_version': material_version,
                    'customer_name': customer_name,
                    'per_page': per_page
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'materials': materials_page,
                'customers': customers,
                'is_iframe': is_iframe,
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_version': material_version,
                    'customer_name': customer_name,
                    'per_page': per_page
                }
            }
        
        return render(request, 'bom_sys/bom_list_new.html', context)


class BomTreeView(View):
    """BOM树形视图，列表形式显示层级结构"""
    
    def get(self, request, bom_id):
        """显示BOM树形视图"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            
            # 获取请求参数
            is_iframe = request.GET.get('iframe', '0') == '1'
            
            # 判断是否为临时BOM导入预览
            is_import_preview = request.path.find('bom_import_preview') != -1
            
            try:
                # 获取指定的BOM节点
                bom = Bom.objects.get(bom_id=bom_id, bom_state=0)
                
                # 获取关联的物料信息
                if bom.bom_material:
                    material = Material.objects.get(material_id=bom.bom_material, material_tempstate=0)
                    customer_code = material.material_customer
                    
                    # 查询客户信息
                    customer = Customer.objects.get(customer_code=customer_code)
                    
                    context = {
                        'username': current_user.user_name,
                        'user_nick': current_user.user_nick,
                        'user_role': current_user.user_role,
                        'bom_id': bom_id,
                        'material': material,
                        'customer': customer,
                        'is_iframe': is_iframe,
                        'is_import_preview': is_import_preview,
                    }
                    
                    return render(request, 'bom_sys/bom_tree_view.html', context)
                else:
                    messages.error(request, '该BOM节点未关联物料信息')
                    return redirect('bom_list_new')
            except (Bom.DoesNotExist, Material.DoesNotExist, Customer.DoesNotExist):
                messages.error(request, f'未找到ID为{bom_id}的有效BOM节点')
                return redirect('bom_list_new')
            
        except User.DoesNotExist:
            return redirect('login')

def get_bom_tree_data(request):
    """获取BOM树状数据API"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 获取BOM ID参数
    bom_id = request.GET.get('bom_id', '')
    
    if not bom_id:
        return JsonResponse({'success': False, 'message': '缺少BOM ID参数'}, status=400)
    
    # 判断是否为临时数据模式（导入BOM）
    is_temp = request.GET.get('is_temp', 'false').lower() == 'true'
    
    # 获取当前用户ID
    user_id = request.session.get('user_id')
    
    try:
        # 获取指定的BOM节点
        if is_temp:
            bom_node = Bom.objects.get(bom_id=bom_id, bom_state__gt=0, bom_ImportUser=user_id)
        else:
            bom_node = Bom.objects.get(bom_id=bom_id, bom_state=0)

        # 找到根节点（向上遍历直到找到没有父节点的节点）
        root_bom = bom_node
        while root_bom.bom_pid != 0:
            try:
                if is_temp:
                    parent_bom = Bom.objects.get(bom_id=root_bom.bom_pid, bom_state__gt=0, bom_ImportUser=user_id)
                else:
                    parent_bom = Bom.objects.get(bom_id=root_bom.bom_pid, bom_state=0)
                root_bom = parent_bom
            except Bom.DoesNotExist:
                # 如果找不到父节点，当前节点就是根节点
                break

        # 构建树形结构
        bom_tree = []

        # 获取根节点的物料信息
        if root_bom.bom_material:
            try:
                # 根据模式选择查询条件
                material_query = Q(material_id=root_bom.bom_material)
                if is_temp:
                    material_query &= ~Q(material_tempstate=0)
                    if user_id:
                        material_query &= Q(material_tempUserId=user_id)
                else:
                    material_query &= Q(material_tempstate=0)

                material = Material.objects.get(material_query)
                customer_code = material.material_customer

                # 构建从根节点开始的完整层级结构
                nodes = get_bom_hierarchy(root_bom, is_temp, user_id)
                
                # 返回JSON格式的BOM树形结构
                return JsonResponse({
                    'success': True, 
                    'bom_tree': nodes,
                    'is_temp': is_temp
                })
            except Material.DoesNotExist:
                return JsonResponse({'success': False, 'message': '未找到关联的物料信息'}, status=404)
        else:
            return JsonResponse({'success': False, 'message': '该BOM节点未关联物料信息'}, status=404)
    except Bom.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到ID为 {bom_id} 的BOM节点'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)

def get_bom_hierarchy(root_bom, is_temp=False, user_id=None):
    """递归构建BOM层级结构"""
    nodes = []
    
    # 添加根节点
    try:
        # 获取根节点物料信息
        material_query = Q(material_id=root_bom.bom_material)
        if is_temp:
            material_query &= ~Q(material_tempstate=0)
            if user_id:
                material_query &= Q(material_tempUserId=user_id)
        else:
            material_query &= Q(material_tempstate=0)
        
        material = Material.objects.get(material_query)
        # 解析存货属性JSON
        inventory_attrs = {}
        if material.material_inventory:
            try:
                inventory_attrs = json.loads(material.material_inventory)
            except json.JSONDecodeError:
                # 处理JSON解析错误
                inventory_attrs = {}
        
        # 创建根节点数据
        root_node = {
            'id': root_bom.bom_id,
            'parent_id': root_bom.bom_pid,
            'level': root_bom.bom_level,
            'material_id': material.material_id,
            'material_name': material.material_name,
            'material_no': material.material_no,
            'material_drawingno': material.material_drawingno,
            'material_version': material.material_version,
            'material_unit': material.material_unit,
            'material_customer': material.material_customer,
            'material_customername': material.material_customername,
            'material_attr': material.material_attr,
            'material_attrname': material.material_attrname,
            'material_spec': material.material_spec,
            'material_quality': material.material_quality,
            'material_size': material.material_size,
            'quantity': root_bom.bom_num if root_bom.bom_num else 1,
            'part_count': root_bom.bom_partcount,
            'loss_rate': root_bom.bom_lossrate,
            'produce_count': root_bom.bom_producecount,
            'inventory_attrs': inventory_attrs,
            'children': []
        }
        
        
        # 查询子节点
        bom_query = Q(bom_pid=root_bom.bom_id)
        if is_temp:
            bom_query &= Q(bom_state__gt=0)
            if user_id:
                bom_query &= Q(bom_ImportUser=user_id)
        else:
            bom_query &= Q(bom_state=0)
        
        child_boms = Bom.objects.filter(bom_query).order_by('bom_order', 'bom_id')  # 添加bom_order排序
        
        # 如果有子节点，递归获取子节点数据
        for child_bom in child_boms:
            # 递归获取子节点的子节点
            child_nodes = get_bom_hierarchy(child_bom, is_temp, user_id)
            # 将子节点添加到根节点的children列表中
            if child_nodes:
                root_node['children'].extend(child_nodes)
        
        # 将根节点添加到结果列表中
        nodes.append(root_node)
    
    except Material.DoesNotExist:
        # 如果没有关联的物料，则返回空列表
        pass
    
    return nodes




@method_decorator(csrf_exempt, name='dispatch')
class PropertiesTypeListView(View):
    """物料属性列表视图"""
    
    def get(self, request):
        """显示物料属性列表"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取所有物料属性
        properties_types = PropertiesType.objects.all().order_by('propertiestype_order')
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有客户信息用于中缀规则选择
        customers = Customer.objects.all().order_by('customer_order')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'properties_types': properties_types,
                'customers': customers,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'properties_types': properties_types,
                'customers': customers,
                'is_iframe': is_iframe,
            }
        
        return render(request, 'bom_sys/properties_type_list.html', context)
    
    def post(self, request):
        """处理物料属性批量保存请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return JsonResponse({'status': 'error', 'message': '未登录'}, status=401)
        
        try:
            # 解析JSON数据
            data = json.loads(request.body)
            properties_data = data.get('properties', [])
            
            # 验证前缀编码唯一性和格式
            prefix_codes = []
            for prop in properties_data:
                prefix_code = prop.get('code')
                
                # 验证前缀编码是否为两位数字
                if not re.match(r'^\d{2}$', prefix_code):
                    return JsonResponse({
                        'status': 'error', 
                        'message': f'前缀编码 "{prefix_code}" 必须是两位数字'
                    }, status=400)
                
                # 检查前缀编码是否重复
                if prefix_code in prefix_codes:
                    return JsonResponse({
                        'status': 'error', 
                        'message': f'前缀编码 "{prefix_code}" 重复'
                    }, status=400)
                
                prefix_codes.append(prefix_code)
                
                # 验证中缀规则
                rule = prop.get('rule')
                if rule != '客户码' and not (rule.isdigit() and len(rule) == 3):
                    return JsonResponse({
                        'status': 'error', 
                        'message': f'中缀规则必须是"客户码"或3位数字，当前值: {rule}'
                    }, status=400)
            
            # 保存所有数据
            for prop in properties_data:
                prop_id = prop.get('id')
                prop_name = prop.get('name')
                prop_code = prop.get('code')
                prop_rule = prop.get('rule')
                prop_order = prop.get('order', 0)
                
                if prop_id:  # 更新现有记录
                    property_type = PropertiesType.objects.get(propertiestype_id=prop_id)
                    property_type.propertiestype_name = prop_name
                    property_type.propertiestype_code = prop_code
                    property_type.propertiesType_Rule = prop_rule
                    property_type.propertiestype_order = prop_order
                    property_type.save()
                else:  # 创建新记录
                    PropertiesType.objects.create(
                        propertiestype_name=prop_name,
                        propertiestype_code=prop_code,
                        propertiesType_Rule=prop_rule,
                        propertiestype_order=prop_order
                    )
            
            # 处理删除的记录
            deleted_ids = data.get('deleted_ids', [])
            for del_id in deleted_ids:
                try:
                    prop = PropertiesType.objects.get(propertiestype_id=del_id)
                    prop.delete()
                except PropertiesType.DoesNotExist:
                    pass
            
            return JsonResponse({'status': 'success', 'message': '物料属性保存成功'})
        
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'保存失败: {str(e)}'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class PropertiesTypeDeleteView(View):
    """删除物料属性视图"""
    
    def post(self, request, prop_id):
        """处理删除物料属性请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return JsonResponse({'status': 'error', 'message': '未登录'}, status=401)
        
        try:
            # 获取要删除的物料属性
            prop = get_object_or_404(PropertiesType, propertiestype_id=prop_id)
            
            # 删除物料属性
            prop.delete()
            
            return JsonResponse({'status': 'success', 'message': '物料属性删除成功'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'删除失败: {str(e)}'}, status=500)





# 获取SMB基础路径
def get_smb_base_path():
    try:
        # 尝试从Config表获取共享路径
        config = Config.objects.first()
        if config and config.config_smbpath:
            smb_path = config.config_smbpath.rstrip('/')  # 去除末尾可能存在的斜杠
            return smb_path
    except Exception as e:
        print(f"获取SMB路径出错: {str(e)}")
    
    return ""


# 实用函数：生成文件路径并确保目录存在
def generate_file_path(smb_base, material_customer, material_customername, material_attr, material_attrname, 
                      material_no, material_drawingno, material_name, material_version, file_type, file_ext=None):
    """
    根据规则生成文件路径，并确保目录存在
    
    参数:
        smb_base: SMB基础路径
        material_customer: 客户代码
        material_customername: 客户名称
        material_attr: 物料属性代码
        material_attrname: 物料属性名称
        material_no: 物料编码
        material_drawingno: 图号
        material_name: 物料名称
        material_version: 版本
        file_type: 文件类型，可选值: 'customer', 'finished', 'workmanship', 'testingform', 'datafolder'
        file_ext: 文件扩展名，如 '.pdf', '.xlsx'，对于资料文件夹不需要
        
    返回:
        完整的文件路径
    """
    # 定义清理文件名的函数，移除不允许的特殊字符
    def clean_filename(name):
        if name is None:
            return ""
        # 替换 Windows 文件系统不允许的特殊字符
        invalid_chars = r'"*<>?\\|/:+'
        for char in invalid_chars:
            name = name.replace(char, "_")
        return name if name else "_"
    
    # 清理所有路径组件
    material_customer = clean_filename(material_customer)
    material_customername = clean_filename(material_customername)
    material_attr = clean_filename(material_attr)
    material_attrname = clean_filename(material_attrname)
    material_no = clean_filename(material_no)
    material_drawingno = clean_filename(material_drawingno)
    material_name = clean_filename(material_name)
    material_version = clean_filename(material_version)
    
 
    
    # 物料编码后四位
    material_no_suffix = material_no[-4:] if len(material_no) >= 4 else material_no
    
    # 根据文件类型确定路径
    if file_type == 'customer':
        # 客户图纸
        dir_path = f"{smb_base}/(01) 客户图纸/({material_customer}) {material_customername}/({material_attr}) {material_attrname}"
        filename = f"{material_no_suffix} {material_drawingno} {material_name} {material_version}{file_ext}"
        full_path = f"{dir_path}/{filename}"
    elif file_type == 'finished':
        # 成品图纸
        dir_path = f"{smb_base}/(02) 成品图纸"
        filename = f"{material_no} {material_name} {material_version}{file_ext}"
        full_path = f"{dir_path}/{filename}"
    elif file_type in ['workmanship', 'testingform']:
        # 工艺图纸或检测表单
        if file_type == 'workmanship':
            dir_path = f"{smb_base}/(03) 工艺图纸"
        else:
            dir_path = f"{smb_base}/(04) 检测表单"
        
        # 检查是否有同名文件，并确定顺序码
        seq_num = "01"  # 默认顺序码
        
        # 确保目录存在，以便我们可以检查其中的文件
        os.makedirs(dir_path, exist_ok=True)
        
        # 获取目录中的文件列表
        base_filename = f"{material_no} {material_name} {material_version}"
        existing_files = [f for f in os.listdir(dir_path) if f.startswith(base_filename) and os.path.isfile(os.path.join(dir_path, f))]
        
        if existing_files:
            # 提取现有文件的顺序码，找出最大值
            seq_nums = []
            for file in existing_files:
                # 尝试提取顺序码
                seq_part = file.split('-')
                if len(seq_part) > 1:
                    try:
                        seq_num_str = seq_part[1].split('.')[0]  # 提取扩展名前的部分
                        if seq_num_str.isdigit() and len(seq_num_str) == 2:
                            seq_nums.append(int(seq_num_str))
                    except (IndexError, ValueError):
                        pass
            
            # 如果找到了顺序码，增加最大值
            if seq_nums:
                max_seq = max(seq_nums)
                seq_num = f"{max_seq + 1:02d}"  # 格式化为两位数
        
        # 构建文件名
        filename = f"{material_no} {material_name} {material_version}-{seq_num}{file_ext}"
        full_path = f"{dir_path}/{filename}"
    
    elif file_type == 'datafolder':
        # 资料文件夹
        dir_path = f"{smb_base}/(05) 资料文件/({material_customer}) {material_customername}/({material_attr}) {material_attrname}/{material_no_suffix} {material_drawingno} {material_name}/{material_version}/"
        full_path = dir_path  # 对于资料文件夹，路径就是目标路径
    else:
        raise ValueError(f"未知的文件类型: {file_type}")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(full_path), exist_ok=True)
    
    return full_path


def view_file(request, file_type, material_id):
    """
    在新窗口打开文件预览
    
    参数:
        file_type: 文件类型，可选值: 'customer', 'finished', 'workmanship', 'testingform'
        material_id: 物料ID
    """
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return redirect('login')
        
    try:
        # 获取物料信息
        material = Material.objects.get(material_id=material_id)
        
        # 根据文件类型获取相应的文件路径
        file_path = None
        if file_type == 'customer':
            file_path = material.material_drawing_customer
        elif file_type == 'finished':
            file_path = material.material_drawing_finished
        elif file_type == 'workmanship':
            file_path = material.material_drawing_workmanship
        elif file_type == 'testingform':
            file_path = material.material_drawing_testingform
        else:
            return HttpResponse("未知的文件类型", status=400)
            
        if not file_path:
            return HttpResponse("文件不存在", status=404)
            
        # 获取SMB基础路径
        smb_base = get_smb_base_path()
        
        # 如果是相对路径，拼接基础路径
        if not os.path.isabs(file_path) and not file_path.startswith('http'):
            full_path = os.path.join(smb_base, file_path)
        else:
            full_path = file_path
            
        # 检查文件是否存在
        if not os.path.exists(full_path):
            return HttpResponse("文件不存在或无法访问", status=404)
            
        # 读取文件内容
        file_content = None
        content_type = None
        
        # 获取文件名
        filename = os.path.basename(full_path)
        
        # 根据文件扩展名确定内容类型
        file_ext = os.path.splitext(full_path)[1].lower()
        if file_ext == '.pdf':
            content_type = 'application/pdf'
            # PDF文件使用内联方式预览
            encoded_filename = urllib.parse.quote(filename)
            disposition = f'inline; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'
        elif file_ext in ['.xls', '.xlsx']:
            # Excel文件设置为下载方式
            if file_ext == '.xlsx':
                content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            else:
                content_type = 'application/vnd.ms-excel'
            # 设置为下载模式，使用从路径中提取的文件名
            # 对于中文文件名，需要特殊处理
            # 1. 提供一个简单的ASCII文件名作为后备
            ascii_filename = f"download{file_ext}"
            # 2. 使用RFC 5987编码提供UTF-8文件名
            encoded_filename = urllib.parse.quote(filename)
            disposition = f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        else:
            content_type = 'application/octet-stream'
            encoded_filename = urllib.parse.quote(filename)
            disposition = f'inline; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'
            
        # 读取文件
        with open(full_path, 'rb') as f:
            file_content = f.read()
            
        # 返回文件内容
        response = HttpResponse(file_content, content_type=content_type)
        response['Content-Disposition'] = disposition
        return response
        
    except Material.DoesNotExist:
        return HttpResponse("物料不存在", status=404)
    except Exception as e:
        return HttpResponse(f"发生错误: {str(e)}", status=500)


def get_smb_base_path_api(request):
    """
    API接口：获取SMB基础路径
    返回JSON格式的SMB基础路径
    """
    smb_base = get_smb_base_path()
    return JsonResponse({'success': True, 'smb_base': smb_base})


def generate_datafolder_path_api(request):
    """
    API接口：生成资料文件夹路径并确保目录存在
    参数：
        material_customer: 客户代码
        material_customername: 客户名称
        material_attr: 物料属性代码
        material_attrname: 物料属性名称
        material_no: 物料编码
        material_drawingno: 图号
        material_name: 物料名称
        material_version: 版本
    返回：
        JSON格式的资料文件夹路径
    """
    try:
        # 获取请求参数
        material_customer = request.GET.get('material_customer', '')
        material_customername = request.GET.get('material_customername', '')
        material_attr = request.GET.get('material_attr', '')
        material_attrname = request.GET.get('material_attrname', '')
        material_no = request.GET.get('material_no', '')
        material_drawingno = request.GET.get('material_drawingno', '')
        material_name = request.GET.get('material_name', '')
        material_version = request.GET.get('material_version', '')
        
        # 检查必要参数
        if not all([material_customer, material_customername, material_attr, 
                   material_attrname, material_no, material_drawingno, 
                   material_name, material_version]):
            return JsonResponse({
                'success': False, 
                'message': '缺少必要参数'
            })
        
        # 获取SMB基础路径
        smb_base = get_smb_base_path()
        
        # 生成资料文件夹路径并确保目录存在
        folder_path = generate_file_path(
            smb_base=smb_base,
            material_customer=material_customer,
            material_customername=material_customername,
            material_attr=material_attr,
            material_attrname=material_attrname,
            material_no=material_no,
            material_drawingno=material_drawingno,
            material_name=material_name,
            material_version=material_version,
            file_type='datafolder'
        )
        
        # 构建universalLink格式的路径
        universal_link = f"universalLink://{folder_path}"
        
        return JsonResponse({
            'success': True, 
            'folder_path': folder_path,
            'universal_link': universal_link
        })
    except Exception as e:
        return JsonResponse({
            'success': False, 
            'message': str(e)
        })


class MaterialQueryView(View):
    """物料查询视图"""
    
    def get(self, request):
        """显示物料查询页面"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
            
        # 获取查询参数
        drawing_number = request.GET.get('drawing_number', '')
        material_name = request.GET.get('material_name', '')
        material_code = request.GET.get('material_code', '')
        material_category = request.GET.get('material_category', '')
        customer_id = request.GET.get('customer_id', '')
        semifinished_status = request.GET.get('semifinished_status', '')
        material_state = request.GET.get('material_state', '1')  # 默认查询启用状态的物料
        
        # 构建查询条件
        query = Q()
        if drawing_number:
            query &= Q(material_drawingno__icontains=drawing_number)
        if material_name:
            query &= Q(material_name__icontains=material_name)
        if material_code:
            query &= Q(material_no__icontains=material_code)
        if material_category:
            # 支持通过属性代码或属性名称搜索
            query &= (Q(material_attr=material_category) | Q(material_attrname__icontains=material_category))
        if customer_id:
            # 尝试通过客户代码查找
            query &= Q(material_customer=customer_id)
        if semifinished_status:
            query &= Q(material_semistate=semifinished_status)
        # 当material_state为"-1"时表示查询全部状态，不添加状态筛选条件
        if material_state and material_state != '-1':
            query &= Q(material_state=material_state)
        
        query &= Q(material_tempstate=0)
        # 执行查询
        materials = Material.objects.filter(query)
        
        # 定义版本号排序函数
        def get_version_sort_key(version):
            if not version:
                return (0, 0)  # 无版本号排在最前面
            
            if version.isalpha() and len(version) == 1:
                # 字母版本，使用ASCII码的负值表示降序
                return (-ord(version), 0)
            elif version.isdigit():
                # 数字版本，使用负值表示降序
                return (0, -int(version))
            else:
                # 其他格式的版本号
                return (0, 0)
        
        # 自定义排序：先按中缀（中间三位）、再按前缀（前两位）、再按后缀（后四位），最后按版本降序
        def material_sort_key(material):
            # 先按物料编码排序
            prefix = material.material_no[0:2] if material.material_no and len(material.material_no) >= 2 else ''
            middle = material.material_no[2:5] if material.material_no and len(material.material_no) >= 5 else ''
            suffix = material.material_no[5:9] if material.material_no and len(material.material_no) >= 9 else ''
            
            # 按照指定顺序返回排序键
            return (middle, prefix, suffix, get_version_sort_key(material.material_version))
        
        # 应用排序
        materials = sorted(materials, key=material_sort_key)
        
        # 分页处理
        page = request.GET.get('page', 1)
        per_page = request.GET.get('per_page', 10)  # 每页显示的记录数
        
        paginator = Paginator(materials, per_page)
        try:
            materials_page = paginator.page(page)
        except PageNotAnInteger:
            materials_page = paginator.page(1)
        except EmptyPage:
            materials_page = paginator.page(paginator.num_pages)
        
        # 获取客户列表，用于筛选
        customers = Customer.objects.all().order_by('customer_order')
            
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'is_iframe': is_iframe,
                'materials': materials_page,
                'customers': customers,
                # 保存查询条件，用于分页时保持查询参数
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_category': material_category,
                    'customer_id': customer_id,
                    'semifinished_status': semifinished_status,
                    'material_state': material_state,
                    'per_page': per_page
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'is_iframe': is_iframe,
                'materials': materials_page,
                'customers': customers,
                # 保存查询条件，用于分页时保持查询参数
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_category': material_category,
                    'customer_id': customer_id,
                    'semifinished_status': semifinished_status,
                    'material_state': material_state,
                    'per_page': per_page
                }
            }
        
        return render(request, 'bom_sys/material_query.html', context)


class BomListView(View):
    """BOM清单管理视图"""
    
    def get(self, request):
        """显示BOM清单管理页面"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
            
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有客户信息，按排序字段排序
        customers = Customer.objects.all().order_by('customer_order', 'customer_id')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'customers': customers,
                'is_iframe': is_iframe,
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'customers': customers,
                'is_iframe': is_iframe,
            }
        return render(request, 'bom_sys/bom_list.html', context)


def get_customer_bom(request):
    """获取客户BOM数据的API"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 获取客户代码参数
    customer_code = request.GET.get('customer_code', '')
    
    if not customer_code:
        return JsonResponse({'success': False, 'message': '缺少客户代码参数'}, status=400)
    
    # 判断是否为临时数据模式（导入BOM）
    is_temp = request.GET.get('is_temp', 'false').lower() == 'true'
    
    # 获取当前用户ID
    user_id = request.session.get('user_id')
    
    try:
        # 查询客户信息
        customer = Customer.objects.get(customer_code=customer_code)
        
        # 根据不同模式设置不同的查询条件
        # 临时数据模式使用临时数据(state>0)，正常模式使用正式数据(state=0)
        bom_state = None
        
        # 使用连接查询直接获取满足条件的顶级BOM记录
        top_boms_query = Bom.objects.filter(
            bom_pid=0,  # 顶级节点
            bom_material__isnull=False  # 确保有关联物料
        )
        
        # 根据模式设置不同的状态查询条件
        if is_temp:
            # 临时数据模式：查询状态为1(新增)或2(修改)的数据
            top_boms_query = top_boms_query.filter(bom_state__gt=0)
            # 如果有用户ID，则只查询当前用户导入的数据
            if user_id:
                top_boms_query = top_boms_query.filter(bom_ImportUser=user_id)
        else:
            # 正常模式：查询状态为0(正式)的数据
            top_boms_query = top_boms_query.filter(bom_state=0)
        
        top_boms = top_boms_query.order_by('bom_id')  # 添加排序保证结果一致性
        
        # 打印调试信息，查看获取的BOM记录
        print('customer_code:', customer_code)
        print('is_temp:', is_temp)
        print('user_id:', user_id)
        print('top_boms count:', top_boms.count())
        
        # 构建BOM树形结构
        bom_tree = []
        
        for top_bom in top_boms:
            # 检查关联的物料是否属于当前客户，如果不是则跳过
            try:
                # 根据模式选择查询条件
                material_query = Q(material_id=top_bom.bom_material)
                if is_temp:
                    # 临时数据模式：查询临时物料(material_tempstate != 0)
                    material_query &= ~Q(material_tempstate=0)
                    # 如果有用户ID，则只查询当前用户导入的临时物料
                    if user_id:
                        material_query &= Q(material_tempUserId=user_id)
                else:
                    # 正常模式：查询正式物料(material_tempstate = 0)
                    material_query &= Q(material_tempstate=0)
                
                material = Material.objects.get(material_query)
                
                # 如果物料的客户代码与当前客户代码不匹配，则跳过
                if material.material_customer != customer_code:
                    continue
                    
                # 构建顶级节点
                node = {
                    'id': f'bom_{top_bom.bom_id}',
                    'type': 'series',
                    'name': material.material_name if material else '',
                    'drawingNumber': material.material_drawingno if material else '',
                    'materialName': material.material_name if material else '',
                    'customerName': material.material_customername if material else '',
                    'customerCode': material.material_customer if material else '',
                    'materialCode': f'BOM-{top_bom.bom_id}',  # 默认编码
                    'version': material.material_version if material else 'A',
                    'materialCategory': material.material_attrname if material else '未分类',
                    'materialAttr': material.material_attr if material else '',
                    'materialId': top_bom.bom_material or '',
                    'materialNo': material.material_no if material else '',
                    'materialTempState': material.material_tempstate,  # 添加临时状态字段
                    'expanded': False,
                    'children': get_child_boms(top_bom.bom_id, is_temp, user_id if is_temp else None)
                }
                
                bom_tree.append(node)
            except Material.DoesNotExist:
                # 如果关联的物料不存在，则跳过
                continue
        
        # 返回JSON格式的BOM树形结构
        return JsonResponse({
            'success': True, 
            'customer': {
                'id': customer.customer_id,
                'name': customer.customer_name,
                'code': customer.customer_code
            },
            'bom_tree': bom_tree,
            'is_temp': is_temp
        })
        
    except Customer.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到客户代码为 {customer_code} 的客户'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)


def get_child_boms(parent_id, is_temp=False, user_id=None):
    """递归获取子BOM数据"""
    children = []
    
    # 根据不同模式设置不同的查询条件
    # 临时数据模式使用临时数据(state>0)，正常模式使用正式数据(state=0)
    bom_state = None  # 初始化为None，后面会根据is_temp设置具体的查询条件
    
    # 构建查询条件
    query = Q(bom_pid=parent_id)
    
    # 根据模式设置不同的状态查询条件
    if is_temp:
        # 临时数据模式：查询状态为1(新增)或2(修改)的数据
        query &= Q(bom_state__gt=0)
        # 如果有用户ID，则只查询当前用户导入的数据
        if user_id:
            query &= Q(bom_ImportUser=user_id)
    else:
        # 正常模式：查询状态为0(正式)的数据
        query &= Q(bom_state=0)
    
    child_boms = Bom.objects.filter(query)
    
    for child_bom in child_boms:
        # 获取关联的物料数据
        material = None
        if child_bom.bom_material:
            try:
                # 根据模式选择查询条件
                material_query = Q(material_id=child_bom.bom_material)
                if is_temp:
                    # 临时数据模式：查询临时物料(material_tempstate != 0)
                    material_query &= ~Q(material_tempstate=0)
                    # 如果有用户ID，则只查询当前用户导入的临时物料
                    if user_id:
                        material_query &= Q(material_tempUserId=user_id)
                else:
                    # 正常模式：查询正式物料(material_tempstate = 0)
                    material_query &= Q(material_tempstate=0)
                
                material = Material.objects.get(material_query)
            except Material.DoesNotExist:
                material = None
        
        if material:
            # 构建子节点
            node = {
                'id': f'bom_{child_bom.bom_id}',
                'type': 'material' if child_bom.bom_level > 1 else 'series',
                'name': material.material_name if material else '',
                'drawingNumber': material.material_drawingno if material else '',
                'materialName': material.material_name if material else '',
                'customerName': material.material_customername if material else '',
                'customerCode': material.material_customer if material else '',
                'materialCode': f'BOM-{child_bom.bom_id}',  # 默认编码
                'version': material.material_version if material else 'A',
                'materialCategory': material.material_attrname if material else '未分类',
                'materialAttr': material.material_attr if material else '',
                'materialId': child_bom.bom_material or '',
                'materialNo': material.material_no if material else '',
                'materialTempState': material.material_tempstate,  # 添加临时状态字段
                'expanded': False,
            }
            
            # 递归获取子节点
            # 构建子节点查询条件
            has_children_query = Q(bom_pid=child_bom.bom_id)
            
            # 根据模式设置不同的状态查询条件
            if is_temp:
                # 临时数据模式：查询状态为1(新增)或2(修改)的数据
                has_children_query &= Q(bom_state__gt=0)
                # 如果有用户ID，则只查询当前用户导入的数据
                if user_id:
                    has_children_query &= Q(bom_ImportUser=user_id)
            else:
                # 正常模式：查询状态为0(正式)的数据
                has_children_query &= Q(bom_state=0)
            
            has_children = Bom.objects.filter(has_children_query).exists()
            if has_children:
                node['children'] = get_child_boms(child_bom.bom_id, is_temp, user_id)
            
            children.append(node)
    
    return children


@csrf_exempt
def delete_bom_node(request):
    """删除BOM节点及其所有子节点，但不删除关联的物料数据"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 检查是否为POST请求
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'}, status=405)
    
    # 获取BOM ID
    bom_id = request.POST.get('bom_id')
    if not bom_id:
        return JsonResponse({'success': False, 'message': '缺少BOM ID参数'}, status=400)
    
    try:
        # 查找要删除的BOM节点
        bom_node = Bom.objects.get(bom_id=bom_id)
        
        # 递归删除所有子节点
        def delete_children(parent_id):
            children = Bom.objects.filter(bom_pid=parent_id, bom_state=0)  # 只查询状态为正式的BOM
            for child in children:
                # 递归删除子节点的子节点
                delete_children(child.bom_id)
                # 删除子节点
                child.delete()
        
        # 执行递归删除
        delete_children(bom_id)
        
        # 最后删除当前节点
        bom_node.delete()
        
        return JsonResponse({'success': True, 'message': 'BOM节点及其子节点已成功删除'})
    
    except Bom.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到ID为 {bom_id} 的BOM节点'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'删除BOM节点失败: {str(e)}'}, status=500)


@csrf_exempt
def search_materials_by_customer(request):
    """根据客户代码和关键词搜索物料"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 获取参数
    customer_code = request.GET.get('customer_code', '')
    keyword = request.GET.get('keyword', '')
    page = int(request.GET.get('page', '1'))
    page_size = int(request.GET.get('page_size', '10'))
    
    try:
        # 构建查询条件
        query = Q(material_customer=customer_code)&Q(material_tempstate=0)
        
        # 如果有关键词，添加模糊搜索条件
        if keyword:
            query &= (
                Q(material_no__icontains=keyword) | 
                Q(material_drawingno__icontains=keyword) | 
                Q(material_name__icontains=keyword)
            )
        
        query &= Q(material_tempstate=0)
        # 查询物料数据
        materials_query = Material.objects.filter(query).order_by('material_no')
        
        # 计算总记录数
        total_count = materials_query.count()
        
        # 计算分页信息
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        
        # 获取当前页的数据
        materials = materials_query[start_index:end_index].values(
            'material_id', 'material_no', 'material_drawingno', 
            'material_name', 'material_version', 'material_state'
        )
        
        # 转换为列表
        materials_list = list(materials)
        
        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size
        
        return JsonResponse({
            'success': True, 
            'materials': materials_list,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': total_pages
            }
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)


@csrf_exempt
def bind_material_to_bom(request):
    """将物料绑定到BOM节点"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 检查是否为POST请求
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'}, status=405)
    
    # 获取参数
    bom_id = request.POST.get('bom_id')
    material_id = request.POST.get('material_id')
    
    # 参数验证
    if not bom_id:
        return JsonResponse({'success': False, 'message': '缺少BOM ID参数'}, status=400)
    if not material_id:
        return JsonResponse({'success': False, 'message': '缺少物料ID参数'}, status=400)
    
    try:
        # 查询BOM记录
        bom = Bom.objects.get(bom_id=bom_id)
        
        # 查询物料记录
        material = Material.objects.get(material_id=material_id)
        
        # 更新BOM记录的物料关联
        bom.bom_material = material.material_id
        # 保存更新
        bom.save()
        
        return JsonResponse({'success': True, 'message': '物料绑定成功'})
        
    except Bom.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到ID为 {bom_id} 的BOM记录'}, status=404)
    except Material.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到ID为 {material_id} 的物料记录'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'绑定物料失败: {str(e)}'}, status=500)


class ImportBOMView(TemplateView):
    """
    导入BOM视图
    """
    template_name = 'bom_sys/import_bom.html'
    
    def get(self, request, *args, **kwargs):
        """
        重写get方法，检查当前用户是否有临时数据
        如果有临时数据，直接跳转到预览页面
        """
        # 检查是否已登录
        if not request.session.get('is_login', False):
            messages.error(request, '请先登录')
            return redirect('login')
            
        # 获取当前用户ID
        user_id = request.session.get('user_id')
        
        if user_id:
            # 检查是否存在当前用户的临时数据
            from .models import Bom, Material, Customer
            
            # 检查是否有临时BOM数据
            has_temp_bom = Bom.objects.filter(bom_state__gt=0, bom_ImportUser=user_id).exists()
            
            # 检查是否有临时物料数据
            has_temp_material = Material.objects.filter(material_tempstate__gt=0, material_tempUserId=user_id).exists()
            
            if has_temp_bom or has_temp_material:
                # 如果有临时数据，需要找到对应的客户ID
                # 首先尝试从临时物料数据中获取客户代码
                temp_material = Material.objects.filter(material_tempstate__gt=0, material_tempUserId=user_id).first()
                
                if temp_material and temp_material.material_customer:
                    try:
                        # 根据客户代码查找客户ID
                        customer = Customer.objects.get(customer_code=temp_material.material_customer)
                        customer_id = customer.customer_id
                        
                        # 重定向到预览页面
                        return redirect('bom_import_preview', customer_id=customer_id)
                    except Customer.DoesNotExist:
                        # 如果找不到客户，显示错误信息
                        messages.warning(request, '发现临时数据，但无法确定对应的客户信息')
                else:
                    # 如果没有物料数据或物料数据没有客户信息，显示警告
                    messages.warning(request, '发现临时数据，但无法确定对应的客户信息')
        
        # 如果没有临时数据或无法确定客户信息，正常显示导入页面
        return super().get(request, *args, **kwargs)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .models import Customer
        context['customers'] = Customer.objects.all().order_by('customer_order', 'customer_name')

        # 清除之前可能存在的导入错误信息，避免在新的导入操作中显示旧的错误
        if 'import_errors' in self.request.session:
            del self.request.session['import_errors']

        return context
    
    def post(self, request, *args, **kwargs):
        from .models import Customer
        import os
        import uuid
        from django.conf import settings
        from django.shortcuts import redirect
        from .import_utils import parse_bom_excel, delete_temp_bom_data
        from django.db import connection
        
        # 检查是否已登录
        if not request.session.get('is_login', False):
            messages.error(request, '请先登录')
            return redirect('login')
            
        # 获取当前用户ID
        user_id = request.session.get('user_id')
        
        # 获取客户ID
        customer_id = request.POST.get('customer')
        if not customer_id:
            messages.error(request, '请选择客户')
            context = self.get_context_data()
            context['close_loading'] = True  # 添加关闭等待框的标记
            return self.render_to_response(context)
        
        # 获取BOM文件
        bom_file = request.FILES.get('bom_file')
        if not bom_file:
            messages.error(request, '请选择BOM文件')
            context = self.get_context_data()
            context['close_loading'] = True  # 添加关闭等待框的标记
            return self.render_to_response(context)
        
        # 检查文件扩展名
        file_ext = os.path.splitext(bom_file.name)[1].lower()
        if file_ext not in ['.xls', '.xlsx']:
            messages.error(request, '请上传Excel格式(.xls, .xlsx)的BOM文件')
            context = self.get_context_data()
            context['close_loading'] = True  # 添加关闭等待框的标记
            return self.render_to_response(context)
        
        try:
            # 创建临时文件路径
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp_uploads')
            os.makedirs(temp_dir, exist_ok=True)
            
            # 生成唯一文件名
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            temp_file_path = os.path.join(temp_dir, unique_filename)
            
            # 保存上传的文件
            with open(temp_file_path, 'wb+') as destination:
                for chunk in bom_file.chunks():
                    destination.write(chunk)
            
            # 确保数据库连接是活跃的
            connection.close()
            connection.connect()
            
            # 清除之前的临时数据
            delete_temp_bom_data(user_id)
            
            # 解析Excel文件并导入数据
            success_count, fail_count, error_messages = parse_bom_excel(temp_file_path, customer_id, user_id)
            
            # 删除临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            
            # 确保数据库连接仍然活跃
            connection.close()
            connection.connect()
            
            if error_messages:
                # 直接将所有错误信息添加到session中，不再通过messages框架显示部分错误
                # 如果还有大量错误信息需要在页面上显示，应该通过专门的区域展示
                request.session['import_errors'] = error_messages

                # 在页面顶部只显示一条汇总错误消息
                messages.error(request, f'导入过程中发生 {len(error_messages)} 个错误，请查看详细错误信息。')

                # 如果所有行都失败，则返回导入页面
                if success_count == 0:
                    context = self.get_context_data()
                    context['close_loading'] = True  # 添加关闭等待框的标记
                    context['error_details'] = error_messages  # 添加详细错误信息
                    return self.render_to_response(context)
            else:
                # 如果没有错误信息，清除session中之前可能存在的错误信息
                if 'import_errors' in request.session:
                    del request.session['import_errors']

            # 记录导入结果
            request.session['import_results'] = {
                'success_count': success_count,
                'fail_count': fail_count,
                'total_count': success_count + fail_count
            }

            # 重定向到导入预览页面
            return redirect('bom_import_preview', customer_id=customer_id)
            
        except Exception as e:
            # 确保数据库连接仍然活跃
            try:
                connection.close()
                connection.connect()
            except:
                pass
                
            error_msg = f'导入过程发生错误: {str(e)}'
            messages.error(request, error_msg)
            context = self.get_context_data()
            context['close_loading'] = True  # 添加关闭等待框的标记
            context['error_details'] = [error_msg]  # 添加详细错误信息
            return self.render_to_response(context)


class BOMImportPreviewView(TemplateView):
    """
    导入BOM预览视图
    """
    template_name = 'bom_sys/bom_import_preview.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        customer_id = self.kwargs.get('customer_id')
        
        from .models import Customer, Bom, Material
        from django.db import connection
        
        try:
            # 确保数据库连接是活跃的
            connection.close()
            connection.connect()
            
            customer = Customer.objects.get(pk=customer_id)
            context['selected_customer'] = customer
            context['import_mode'] = True
            context['is_preview'] = True  # 标记为预览模式
            
            # 获取导入结果
            import_results = self.request.session.get('import_results', {})
            success_count = import_results.get('success_count', 0)
            fail_count = import_results.get('fail_count', 0)
            total_count = import_results.get('total_count', 0)
            
            # 如果session中没有导入结果（可能是直接进入预览页面），则根据数据库中的临时数据计算
            if not import_results:
                # 获取当前用户ID
                user_id = self.request.session.get('user_id')
                
                if user_id:
                    # 计算成功导入的数量（临时BOM数据的数量，包括新增和修改）
                    success_count = Bom.objects.filter(bom_state__gt=0, bom_ImportUser=user_id).count()
                    
                    # 从session中获取失败数量，如果没有则默认为0
                    fail_count = self.request.session.get('import_errors', [])
                    if isinstance(fail_count, list):
                        fail_count = len(fail_count)
                    else:
                        fail_count = 0
                    
                    # 计算总数
                    total_count = success_count + fail_count
            
            # 确保这些变量始终存在，即使没有导入结果
            context['success_count'] = success_count if 'success_count' in locals() else 0
            context['fail_count'] = fail_count if 'fail_count' in locals() else 0
            context['total_count'] = total_count if 'total_count' in locals() else 0
            
            # 获取导入错误信息
            if 'import_errors' in self.request.session:
                context['error_details'] = self.request.session['import_errors']
                # 从session中删除错误信息，避免重复显示
                del self.request.session['import_errors']
            
            # 获取所有客户列表用于初始化树结构
            context['customers'] = Customer.objects.all().order_by('customer_order', 'customer_name')
            
            # 获取当前用户ID
            user_id = self.request.session.get('user_id')
            
            # 查询临时导入的顶级BOM节点（父ID为0，状态为临时数据，且为当前用户导入的数据）
            top_boms_query = Bom.objects.filter(bom_pid=0, bom_state__gt=0)
            if user_id:
                top_boms_query = top_boms_query.filter(bom_ImportUser=user_id)
                
            top_boms = top_boms_query
            if top_boms.exists():
                context['has_preview_data'] = True
                
                # 获取第一个顶级节点的物料信息
                top_bom = top_boms.first()
                if top_bom.bom_material:
                    try:
                        top_material = Material.objects.get(material_id=top_bom.bom_material)
                        context['top_material_name'] = top_material.material_name
                        context['top_material_no'] = top_material.material_no
                        context['top_material_version'] = top_material.material_version
                    except Material.DoesNotExist:
                        pass
            else:
                context['has_preview_data'] = False
            
            # 查询物料数据的统计信息，只统计当前用户导入的数据
            materials_query_new = Material.objects.filter(material_tempstate=1)
            materials_query_updated = Material.objects.filter(material_tempstate=2)
            
            if user_id:
                materials_query_new = materials_query_new.filter(material_tempUserId=user_id)
                materials_query_updated = materials_query_updated.filter(material_tempUserId=user_id)
            
            new_materials = materials_query_new.count()
            updated_materials = materials_query_updated.count()
            context['new_materials'] = new_materials
            context['updated_materials'] = updated_materials
            
            # 额外标记，在前端JS中使用
            context['is_import_preview'] = True
            context['preview_customer_code'] = customer.customer_code

            # 查询导入的BOM数据列表（bom_level=0 AND bom_state!=0 AND bom_ImportUser=当前登录用户）
            imported_materials = []
            if user_id:
                # 查询顶级BOM节点（bom_level=0），状态不为0（bom_state!=0），且为当前用户导入的数据
                imported_boms = Bom.objects.filter(
                    bom_level=0,
                    bom_state__gt=0,  # bom_state!=0
                    bom_ImportUser=user_id
                )

                # 构建材料列表数据
                for bom in imported_boms:
                    if bom.bom_material:
                        try:
                            material = Material.objects.get(material_id=bom.bom_material)
                            material_data = {
                                'bom_id': bom.bom_id,
                                'material_id': material.material_id,
                                'material_drawingno': material.material_drawingno or '',
                                'material_no': material.material_no or '',
                                'material_version': material.material_version or '',
                                'material_name': material.material_name or '',
                                'customer_name': customer.customer_name,
                                'customer_code': customer.customer_code,
                            }
                            imported_materials.append(material_data)
                        except Material.DoesNotExist:
                            # 如果物料不存在，跳过
                            continue

            context['imported_materials'] = imported_materials

        except Customer.DoesNotExist:
            messages.error(self.request, '客户不存在')
        except Exception as e:
            # 处理其他异常
            messages.error(self.request, f'加载预览数据时出错: {str(e)}')
            # 确保数据库连接仍然活跃
            try:
                connection.close()
                connection.connect()
            except:
                pass

        return context

    def post(self, request, *args, **kwargs):
        """处理导入预览页面的表单提交，例如确认导入"""
        customer_id = self.kwargs.get('customer_id')
        action = request.POST.get('action')
        
        if action == 'confirm_import':
            # 确认导入，将临时数据标记为正式数据
            try:
                from django.db import connection
                
                # 确保数据库连接是活跃的
                connection.close()
                connection.connect()
                
                # 获取当前用户ID
                user_id = request.session.get('user_id')
                
                with connection.cursor() as cursor:
                    # 处理BOM数据的导入逻辑
                    if user_id:
                        # 1. 获取所有当前用户导入的临时顶级BOM记录及其相关的物料信息
                        cursor.execute("""
                            SELECT b.bom_id, b.bom_material, m.material_drawingno, m.material_version, m.material_customer
                            FROM bom b
                            JOIN Material m ON b.bom_material = m.material_id
                            WHERE b.bom_pid = 0 AND b.bom_State > 0 AND b.bom_ImportUser = %s
                        """, [user_id])
                        
                        temp_top_boms = cursor.fetchall()
                        
                        # 2. 对于每个临时顶级BOM，检查是否存在相同图号、版本、客户的正式顶级BOM
                        for bom_id, material_id, drawing_no, version, customer_code in temp_top_boms:
                            if drawing_no and version and customer_code:
                                # 查找匹配的正式顶级BOM
                                cursor.execute("""
                                    SELECT b.bom_id
                                    FROM bom b
                                    JOIN Material m ON b.bom_material = m.material_id
                                    WHERE b.bom_pid = 0 
                                      AND b.bom_State = 0 
                                      AND m.material_drawingno = %s
                                      AND m.material_version = %s
                                      AND m.material_customer = %s
                                """, [drawing_no, version, customer_code])
                                
                                matching_boms = cursor.fetchall()
                                
                                # 如果找到匹配的正式BOM，删除它及其所有子项
                                for (matching_bom_id,) in matching_boms:
                                    # 递归删除所有子项
                                    def delete_bom_tree(parent_id):
                                        # 先查找所有子项
                                        cursor.execute("SELECT bom_id FROM bom WHERE bom_pid = %s", [parent_id])
                                        children = cursor.fetchall()
                                        
                                        # 递归删除每个子项
                                        for (child_id,) in children:
                                            delete_bom_tree(child_id)
                                        
                                        # 删除当前节点
                                        cursor.execute("DELETE FROM bom WHERE bom_id = %s", [parent_id])
                                    
                                    # 删除整个BOM树
                                    delete_bom_tree(matching_bom_id)
                        
                        # 3. 将临时BOM数据转换为正式数据，同时更新BOM的更新时间
                        cursor.execute("UPDATE bom SET bom_State=0, bom_UpdateTime=NOW() WHERE bom_State>0 AND bom_ImportUser=%s", [user_id])
                        
                        # 4. 将临时物料数据更改为正式数据，仅限当前用户导入的数据
                        cursor.execute("UPDATE Material SET Material_tempState=0 WHERE Material_tempState=1 AND Material_tempUserId=%s", [user_id])  # 新增的物料
                    else:
                        # 如果没有用户ID，使用简化逻辑（不检查重复，直接更新状态）
                        cursor.execute("UPDATE bom SET bom_State=0 WHERE bom_State>0")
                        cursor.execute("UPDATE Material SET Material_tempState=0 WHERE Material_tempState=1")  # 新增的物料
                    
                    # 对于要更新的物料，我们需要替换原有记录的数据
                    # 只更新当前用户导入的临时数据
                    if user_id:
                        # 首先获取所有Material_tempState=2的记录，只处理当前用户的数据
                        cursor.execute("""
                            UPDATE Material m1
                            JOIN Material m2 ON m1.Material_Id = m2.Material_tempTrueId
                            SET
                                m1.material_drawingno = m2.material_drawingno,
                                m1.material_name = m2.material_name,
                                m1.material_quality = m2.material_quality,
                                m1.material_spec = m2.material_spec,
                                m1.material_version = m2.material_version,
                                m1.material_unit = m2.material_unit,
                                m1.material_attr = m2.material_attr,
                                m1.material_attrname = m2.material_attrname,
                                m1.material_inventory = m2.material_inventory,
                                m1.material_supp_casting = m2.material_supp_casting,
                                m1.material_supp_machining = m2.material_supp_machining,
                                m1.material_supp_sheetmetal = m2.material_supp_sheetmetal,
                                m1.material_supp_purchase = m2.material_supp_purchase,
                                m1.material_supp_prochasemanager = m2.material_supp_prochasemanager,
                                m1.material_uptime = m2.material_uptime,
                                m1.Material_UpdateTime = NOW()
                            WHERE m2.Material_tempState = 2 AND m2.Material_tempUserId = %s
                        """, [user_id])
                        
                        # 重要：更新BOM表中引用临时物料ID的记录，使其指向原始物料ID
                        cursor.execute("""
                            UPDATE bom b
                            JOIN Material m ON b.bom_material = m.material_id
                            SET b.bom_material = m.material_tempTrueId
                            WHERE m.Material_tempState = 2 AND m.Material_tempUserId = %s
                        """, [user_id])
                        
                        # 删除临时更新记录，只删除当前用户的数据
                        cursor.execute("DELETE FROM Material WHERE Material_tempState = 2 AND Material_tempUserId = %s", [user_id])
                  
                messages.success(request, '导入成功！临时数据已转为正式数据。')
                return redirect('import_bom')
            
            except Exception as e:
                # 确保数据库连接仍然活跃
                try:
                    connection.close()
                    connection.connect()
                except:
                    pass
                    
                messages.error(request, f'确认导入失败：{str(e)}')
        
        elif action == 'cancel_import':
            # 取消导入，删除所有临时数据
            try:
                from django.db import connection
                # 确保数据库连接是活跃的
                connection.close()
                connection.connect()
                
                from .import_utils import delete_temp_bom_data
                # 获取当前用户ID
                user_id = request.session.get('user_id')
                delete_temp_bom_data(user_id)
                messages.info(request, '已取消导入并删除临时数据。')
                return redirect('import_bom')
            except Exception as e:
                # 确保数据库连接仍然活跃
                try:
                    connection.close()
                    connection.connect()
                except:
                    pass
                    
                messages.error(request, f'取消导入失败：{str(e)}')
        
        # 如果有其他操作或发生错误，返回预览页面
        return self.get(request, *args, **kwargs)


@csrf_exempt
def confirm_bom_import(request):
    """确认导入BOM，将临时数据转为正式数据"""
    # 检查是否已登录和是否为POST请求
    if not request.session.get('is_login', False) or request.method != 'POST':
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 获取当前用户ID
    user_id = request.session.get('user_id')
    
    try:
        # 将状态为2的BOM数据更改为状态1（仅限当前用户导入的数据）
        from django.db import connection
        with connection.cursor() as cursor:
            if user_id:
                # 只更新当前用户导入的临时BOM数据
                cursor.execute("UPDATE bom SET bom_State=1 WHERE bom_State=2 AND bom_ImportUser=%s", [user_id])
                
                # 只更新当前用户导入的临时物料数据
                cursor.execute("UPDATE Material SET Material_tempState=0 WHERE Material_tempState!=0 AND Material_tempUserId=%s", [user_id])
            else:
                # 如果没有用户ID，更新所有临时数据（保留原有行为）
                cursor.execute("UPDATE bom SET bom_State=1 WHERE bom_State=2")
                cursor.execute("UPDATE Material SET Material_tempState=0 WHERE Material_tempState!=0")
        
        return JsonResponse({'success': True, 'message': '导入成功！临时数据已转为正式数据。'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'确认导入失败：{str(e)}'}, status=500)


# 预览功能已合并到get_customer_bom函数中，通过is_preview参数控制

class BomListNewView(View):
    """BOM清单(新)管理视图"""
    
    def get(self, request):
        """显示BOM清单(新)管理页面，展示bom_State=0且bom_Level=0的记录"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取查询参数
        drawing_number = request.GET.get('drawing_number', '')
        material_name = request.GET.get('material_name', '')
        material_code = request.GET.get('material_code', '')
        material_version = request.GET.get('material_version', '')
        customer_name = request.GET.get('customer_name', '')
        
        # 构建查询条件
        query = Q(bom_state=0) & Q(bom_level=0)
        
        # 获取所有符合条件的BOM记录
        bom_records = Bom.objects.filter(query).order_by('bom_order', 'bom_id')  # 添加bom_order排序
        
        # 关联Material表并应用过滤条件
        materials_data = []
        for bom in bom_records:
            try:
                material = Material.objects.get(material_id=bom.bom_material)
                
                # 应用过滤条件
                if drawing_number and drawing_number.lower() not in (material.material_drawingno or '').lower():
                    continue
                if material_name and material_name.lower() not in (material.material_name or '').lower():
                    continue
                if material_code and material_code.lower() not in (material.material_no or '').lower():
                    continue
                if material_version and material_version.lower() not in (material.material_version or '').lower():
                    continue
                if customer_name and customer_name.lower() not in (material.material_customername or '').lower():
                    continue
                
                # 添加到结果列表
                materials_data.append({
                    'bom_id': bom.bom_id,
                    'material_id': material.material_id,
                    'material_drawingno': material.material_drawingno or '',
                    'material_no': material.material_no or '',
                    'material_name': material.material_name or '',
                    'material_version': material.material_version or '',
                    'customer_name': material.material_customername or '',
                    'customer_code': material.material_customer or ''
                })
            except Material.DoesNotExist:
                # 如果找不到对应物料，跳过
                continue
        
        # 分页处理
        page = request.GET.get('page', 1)
        per_page = request.GET.get('per_page', 20)  # 默认每页显示20条
        
        paginator = Paginator(materials_data, per_page)
        try:
            materials_page = paginator.page(page)
        except PageNotAnInteger:
            materials_page = paginator.page(1)
        except EmptyPage:
            materials_page = paginator.page(paginator.num_pages)
        
        # 检查是否为iframe请求
        is_iframe = request.GET.get('iframe', '0') == '1'
        
        # 获取所有客户信息，用于下拉选择框
        customers = Customer.objects.all().order_by('customer_order', 'customer_id')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            context = {
                'username': current_user.user_name,
                'user_nick': current_user.user_nick,
                'user_role': current_user.user_role,
                'materials': materials_page,
                'customers': customers,
                'is_iframe': is_iframe,
                # 保存查询条件，用于分页时保持查询参数
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_version': material_version,
                    'customer_name': customer_name,
                    'per_page': per_page
                }
            }
        except User.DoesNotExist:
            context = {
                'username': request.session.get('username', ''),
                'materials': materials_page,
                'customers': customers,
                'is_iframe': is_iframe,
                'query_params': {
                    'drawing_number': drawing_number,
                    'material_name': material_name,
                    'material_code': material_code,
                    'material_version': material_version,
                    'customer_name': customer_name,
                    'per_page': per_page
                }
            }
        
        return render(request, 'bom_sys/bom_list_new.html', context)


class BomTreeView(View):
    """BOM树形视图，列表形式显示层级结构"""
    
    def get(self, request, bom_id):
        """显示BOM树形视图"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')
        
        # 获取当前用户信息
        user_id = request.session.get('user_id', 0)
        try:
            current_user = User.objects.get(user_id=user_id)
            
            # 获取请求参数
            is_iframe = request.GET.get('iframe', '0') == '1'
            
            # 判断是否为临时BOM导入预览
            is_import_preview = request.path.find('bom_import_preview') != -1
            
            try:
                # 获取指定的BOM节点
                bom = Bom.objects.get(bom_id=bom_id, bom_state=0)
                
                # 获取关联的物料信息
                if bom.bom_material:
                    material = Material.objects.get(material_id=bom.bom_material, material_tempstate=0)
                    customer_code = material.material_customer
                    
                    # 查询客户信息
                    customer = Customer.objects.get(customer_code=customer_code)
                    
                    context = {
                        'username': current_user.user_name,
                        'user_nick': current_user.user_nick,
                        'user_role': current_user.user_role,
                        'bom_id': bom_id,
                        'material': material,
                        'customer': customer,
                        'is_iframe': is_iframe,
                        'is_import_preview': is_import_preview,
                    }
                    
                    return render(request, 'bom_sys/bom_tree_view.html', context)
                else:
                    messages.error(request, '该BOM节点未关联物料信息')
                    return redirect('bom_list_new')
            except (Bom.DoesNotExist, Material.DoesNotExist, Customer.DoesNotExist):
                messages.error(request, f'未找到ID为{bom_id}的有效BOM节点')
                return redirect('bom_list_new')
            
        except User.DoesNotExist:
            return redirect('login')

def get_bom_tree_data(request):
    """获取BOM树状数据API"""
    # 检查是否已登录
    if not request.session.get('is_login', False):
        return JsonResponse({'success': False, 'message': '请先登录'}, status=401)
    
    # 获取BOM ID参数
    bom_id = request.GET.get('bom_id', '')
    
    if not bom_id:
        return JsonResponse({'success': False, 'message': '缺少BOM ID参数'}, status=400)
    
    # 判断是否为临时数据模式（导入BOM）
    is_temp = request.GET.get('is_temp', 'false').lower() == 'true'
    
    # 获取当前用户ID
    user_id = request.session.get('user_id')
    
    try:
        print(f"查询BOM节点: bom_id={bom_id}, is_temp={is_temp}, user_id={user_id}")

        # 获取指定的BOM节点
        if is_temp:
            # 先检查是否存在符合条件的BOM记录
            bom_query = Bom.objects.filter(bom_id=bom_id)
            print(f"BOM ID {bom_id} 的所有记录: {list(bom_query.values('bom_id', 'bom_state', 'bom_ImportUser'))}")

            bom_node = Bom.objects.get(bom_id=bom_id, bom_state__gt=0, bom_ImportUser=user_id)
        else:
            bom_node = Bom.objects.get(bom_id=bom_id, bom_state=0)

        print(f"找到BOM节点: {bom_node.bom_id}, 状态: {bom_node.bom_state}, 导入用户: {getattr(bom_node, 'bom_ImportUser', 'N/A')}")
        
        # 构建树形结构
        bom_tree = []
        
        # 获取该BOM节点的物料信息
        if bom_node.bom_material:
            try:
                # 根据模式选择查询条件
                material_query = Q(material_id=bom_node.bom_material)
                if is_temp:
                    material_query &= ~Q(material_tempstate=0)
                    if user_id:
                        material_query &= Q(material_tempUserId=user_id)
                else:
                    material_query &= Q(material_tempstate=0)
                
                material = Material.objects.get(material_query)
                customer_code = material.material_customer

                # 获取客户信息
                customer = None
                try:
                    customer = Customer.objects.get(customer_code=customer_code)
                except Customer.DoesNotExist:
                    pass

                # 构建当前节点及子节点的层级结构
                nodes = get_bom_hierarchy(bom_node, is_temp, user_id)

                # 返回JSON格式的BOM树形结构
                response_data = {
                    'success': True,
                    'bom_tree': nodes,
                    'is_temp': is_temp,
                    'material': {
                        'material_id': material.material_id,
                        'material_name': material.material_name,
                        'material_drawingno': material.material_drawingno,
                        'material_version': material.material_version,
                        'material_no': material.material_no
                    }
                }

                # 添加客户信息
                if customer:
                    response_data['customer'] = {
                        'id': customer.customer_id,
                        'name': customer.customer_name,
                        'code': customer.customer_code
                    }

                return JsonResponse(response_data)
            except Material.DoesNotExist:
                return JsonResponse({'success': False, 'message': '未找到关联的物料信息'}, status=404)
        else:
            return JsonResponse({'success': False, 'message': '该BOM节点未关联物料信息'}, status=404)
    except Bom.DoesNotExist:
        return JsonResponse({'success': False, 'message': f'未找到ID为 {bom_id} 的BOM节点'}, status=404)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"get_bom_tree_data 错误详情: {error_details}")
        return JsonResponse({'success': False, 'message': f'服务器错误: {str(e)}'}, status=500)

def get_bom_hierarchy(root_bom, is_temp=False, user_id=None):
    """递归构建BOM层级结构"""
    nodes = []

    # 添加根节点
    try:
        print(f"处理BOM节点: {root_bom.bom_id}, 物料ID: {root_bom.bom_material}, 临时模式: {is_temp}")
        # 获取根节点物料信息
        material_query = Q(material_id=root_bom.bom_material)
        if is_temp:
            material_query &= ~Q(material_tempstate=0)
            if user_id:
                material_query &= Q(material_tempUserId=user_id)
        else:
            material_query &= Q(material_tempstate=0)
        
        material = Material.objects.get(material_query)
        print(f"找到物料: {material.material_id}, 名称: {material.material_name}, 临时状态: {material.material_tempstate}")
        # 解析存货属性JSON
        inventory_attrs = {}
        if material.material_inventory:
            try:
                inventory_attrs = json.loads(material.material_inventory)
            except json.JSONDecodeError:
                # 处理JSON解析错误
                inventory_attrs = {}
        
        # 创建根节点数据，添加安全的字段访问
        root_node = {
            'id': root_bom.bom_id,
            'bom_id': root_bom.bom_id,  # 添加bom_id字段以兼容前端
            'parent_id': getattr(root_bom, 'bom_pid', 0),
            'level': getattr(root_bom, 'bom_level', 0),
            'material_id': material.material_id,
            'material_name': getattr(material, 'material_name', '') or '',
            'material_no': getattr(material, 'material_no', '') or '',
            'material_drawingno': getattr(material, 'material_drawingno', '') or '',
            'material_version': getattr(material, 'material_version', '') or '',
            'material_unit': getattr(material, 'material_unit', '') or '',
            'material_customer': getattr(material, 'material_customer', '') or '',
            'material_customername': getattr(material, 'material_customername', '') or '',
            'material_attr': getattr(material, 'material_attr', '') or '',
            'material_attrname': getattr(material, 'material_attrname', '') or '',
            'material_spec': getattr(material, 'material_spec', '') or '',
            'material_quality': getattr(material, 'material_quality', '') or '',
            'material_size': getattr(material, 'material_size', '') or '',
            'material_tempstate': getattr(material, 'material_tempstate', 0),  # 添加物料临时状态
            'material_customersupply': getattr(material, 'material_customersupply', False),
            'material_outsourcing': getattr(material, 'material_outsourcing', False),
            'material_sales': getattr(material, 'material_sales', False),
            'material_selfmade': getattr(material, 'material_selfmade', False),
            'material_subcontract': getattr(material, 'material_subcontract', False),
            'material_productionconsumption': getattr(material, 'material_productionconsumption', False),
            'material_batchmanagement': getattr(material, 'material_batchmanagement', False),
            'material_cuttingsize': getattr(material, 'material_size', '') or '',
            'quantity': getattr(root_bom, 'bom_num', 1) if getattr(root_bom, 'bom_num', None) else 1,
            'bom_num': getattr(root_bom, 'bom_num', 1),  # 添加bom_num字段以兼容前端
            'bom_state': getattr(root_bom, 'bom_state', 0),  # 添加BOM状态
            'part_count': getattr(root_bom, 'bom_partcount', 0),
            'bom_partcount': getattr(root_bom, 'bom_partcount', 0),  # 添加bom_partcount字段以兼容前端
            'loss_rate': getattr(root_bom, 'bom_lossrate', 0),
            'bom_lossrate': getattr(root_bom, 'bom_lossrate', 0),  # 添加bom_lossrate字段以兼容前端
            'produce_count': getattr(root_bom, 'bom_producecount', 0),
            'bom_producecount': getattr(root_bom, 'bom_producecount', 0),  # 添加bom_producecount字段以兼容前端
            'inventory_attrs': inventory_attrs,
            'children': []
        }
        
        
        # 查询子节点
        bom_query = Q(bom_pid=root_bom.bom_id)
        if is_temp:
            bom_query &= Q(bom_state__gt=0)
            if user_id:
                bom_query &= Q(bom_ImportUser=user_id)
        else:
            bom_query &= Q(bom_state=0)
        
        child_boms = Bom.objects.filter(bom_query).order_by('bom_order', 'bom_id')  # 添加bom_order排序
        
        # 如果有子节点，递归获取子节点数据
        for child_bom in child_boms:
            # 递归获取子节点的子节点
            child_nodes = get_bom_hierarchy(child_bom, is_temp, user_id)
            # 将子节点添加到根节点的children列表中
            if child_nodes:
                root_node['children'].extend(child_nodes)
        
        # 将根节点添加到结果列表中
        nodes.append(root_node)
    
    except Material.DoesNotExist:
        # 如果没有关联的物料，则返回空列表
        print(f"未找到物料ID为 {root_bom.bom_material} 的物料记录，临时模式: {is_temp}")
        pass
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"get_bom_hierarchy 错误详情: {error_details}")
        # 返回空列表而不是抛出异常
        pass
    
    return nodes


def search_materials_for_bind(request):
    """搜索可绑定的物料"""
    if request.method != 'GET':
        return JsonResponse({'success': False, 'error': '请求方法错误'})

    try:
        # 获取搜索参数
        customer = request.GET.get('customer', '')
        keyword = request.GET.get('keyword', '').strip()
        state = request.GET.get('state', '1')  # 默认只显示在用状态
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))

        print(f"搜索物料参数: customer={customer}, keyword={keyword}, state={state}, page={page}")

        # 构建查询条件
        query = Q(material_tempstate=0)  # 只查询非临时状态的物料

        # 客户筛选
        if customer:
            query &= Q(material_customer=customer)

        # 关键字搜索
        if keyword:
            query &= (Q(material_name__icontains=keyword) |
                     Q(material_no__icontains=keyword) |
                     Q(material_drawingno__icontains=keyword))

        # 状态筛选
        if state:
            state_list = [int(s.strip()) for s in state.split(',') if s.strip().isdigit()]
            if state_list:
                query &= Q(material_state__in=state_list)

        # 执行查询
        materials = Material.objects.filter(query).order_by('-material_uptime')

        # 分页
        paginator = Paginator(materials, page_size)
        try:
            page_obj = paginator.page(page)
        except PageNotAnInteger:
            page_obj = paginator.page(1)
        except EmptyPage:
            page_obj = paginator.page(paginator.num_pages)

        # 构建返回数据
        material_list = []
        for material in page_obj:
            material_list.append({
                'id': material.material_id,
                'no': material.material_no,
                'name': material.material_name,
                'drawing_no': material.material_drawingno,  # 客户图号
                'version': material.material_version,
                'attr_name': material.material_attrname,  # 物料分类
                'state': material.material_state
            })

        # 分页信息
        pagination_info = {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_count': paginator.count,
            'has_previous': page_obj.has_previous(),
            'has_next': page_obj.has_next(),
            'start': (page_obj.number - 1) * page_size + 1,
            'end': min(page_obj.number * page_size, paginator.count),
            'total': paginator.count
        }

        return JsonResponse({
            'success': True,
            'materials': material_list,
            'pagination': pagination_info
        })

    except Exception as e:
        print(f"搜索物料失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_protect
def bind_material_to_bom(request):
    """绑定物料到BOM"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '请求方法错误'})

    try:
        bom_id = request.POST.get('bom_id')
        material_id = request.POST.get('material_id')

        print(f"绑定物料参数: bom_id={bom_id}, material_id={material_id}")

        if not bom_id or not material_id:
            return JsonResponse({'success': False, 'error': '参数不完整'})

        # 查找BOM记录
        try:
            bom = Bom.objects.get(bom_id=bom_id)
        except Bom.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到指定的BOM记录'})

        # 查找物料记录
        try:
            material = Material.objects.get(material_id=material_id)
        except Material.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到指定的物料记录'})

        # 更新BOM记录
        bom.bom_material = material_id
        bom.save()

        print(f"成功绑定物料 {material_id} 到BOM {bom_id}")

        return JsonResponse({
            'success': True,
            'message': f'成功绑定物料 {material.material_name} 到BOM'
        })

    except Exception as e:
        print(f"绑定物料失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_protect
def bind_material_to_temp_bom(request):
    """绑定物料到临时BOM（用于导入预览页面）"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '请求方法错误'})

    try:
        bom_id = request.POST.get('bom_id')
        material_id = request.POST.get('material_id')

        print(f"绑定临时物料参数: bom_id={bom_id}, material_id={material_id}")

        if not bom_id or not material_id:
            return JsonResponse({'success': False, 'error': '参数不完整'})

        # 查找BOM记录
        try:
            bom = Bom.objects.get(bom_id=bom_id)
        except Bom.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到指定的BOM记录'})

        # 查找要绑定的源物料记录
        try:
            source_material = Material.objects.get(material_id=material_id)
        except Material.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到指定的物料记录'})

        # 查找当前BOM对应的临时物料记录
        try:
            temp_material = Material.objects.get(material_id=bom.bom_material)
        except Material.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到BOM对应的临时物料记录'})

        # 将源物料的字段值拷贝到临时物料中
        temp_material.material_no = source_material.material_no
        temp_material.material_name = source_material.material_name
        temp_material.material_drawingno = source_material.material_drawingno
        temp_material.material_version = source_material.material_version
        temp_material.material_attr = source_material.material_attr
        temp_material.material_attrname = source_material.material_attrname
        temp_material.material_spec = source_material.material_spec
        temp_material.material_quality = source_material.material_quality
        temp_material.material_state = source_material.material_state
        temp_material.material_customer = source_material.material_customer
        temp_material.material_customername = source_material.material_customername
        temp_material.material_img = source_material.material_img
        temp_material.material_count = source_material.material_count
        temp_material.material_semistate = source_material.material_semistate
        temp_material.material_producecount = source_material.material_producecount
        temp_material.material_partcount = source_material.material_partcount
        temp_material.material_unit = source_material.material_unit
        temp_material.material_lossrate = source_material.material_lossrate
        temp_material.material_workshop = source_material.material_workshop
        temp_material.material_inventory = source_material.material_inventory
        temp_material.material_size = source_material.material_size

        # 工艺相关字段
        temp_material.material_processroute = source_material.material_processroute
        temp_material.material_ProcessrouteTimes = source_material.material_ProcessrouteTimes
        temp_material.material_ProcessrouteDevice = source_material.material_ProcessrouteDevice
        temp_material.material_ProcessroutePrice = source_material.material_ProcessroutePrice
        temp_material.material_ProcessrouteType = source_material.material_ProcessrouteType
        temp_material.material_ProcessrouteOutSupplier = source_material.material_ProcessrouteOutSupplier
        temp_material.material_ProcessrouteOutPrice = source_material.material_ProcessrouteOutPrice
        temp_material.material_ProcessrouteImportant = source_material.material_ProcessrouteImportant
        temp_material.material_ProcessrouteCode = source_material.material_ProcessrouteCode

        # 供应商相关字段
        temp_material.material_supp_casting = source_material.material_supp_casting
        temp_material.material_supp_machining = source_material.material_supp_machining
        temp_material.material_supp_sheetmetal = source_material.material_supp_sheetmetal
        temp_material.material_supp_purchase = source_material.material_supp_purchase
        temp_material.material_supp_prochasemanager = source_material.material_supp_prochasemanager

        # 文档相关字段
        temp_material.material_drawing_customer = source_material.material_drawing_customer
        temp_material.material_drawing_finished = source_material.material_drawing_finished
        temp_material.material_drawing_workmanship = source_material.material_drawing_workmanship
        temp_material.material_drawing_testingform = source_material.material_drawing_testingform
        temp_material.material_drawing_datafolder = source_material.material_drawing_datafolder

        # 设置临时状态和真实ID
        temp_material.material_tempstate = 2  # 标记为绑定状态
        temp_material.material_tempTrueId = material_id  # 记录绑定的真实物料ID

        # 保存临时物料
        temp_material.save()

        print(f"成功将物料 {source_material.material_name} 的数据拷贝到临时物料 {temp_material.material_id}")

        return JsonResponse({
            'success': True,
            'message': f'成功绑定物料 {source_material.material_name}'
        })

    except Exception as e:
        print(f"绑定临时物料失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


# ==================== BOM结构操作API ====================

@csrf_protect
def move_bom_node(request):
    """移动BOM节点"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '请求方法错误'})

    try:
        bom_id = request.POST.get('bom_id')
        target_parent_id = request.POST.get('target_parent_id')
        position = request.POST.get('position', 'last')  # first, last

        print(f"移动BOM节点参数: bom_id={bom_id}, target_parent_id={target_parent_id}, position={position}")

        if not bom_id or not target_parent_id:
            return JsonResponse({'success': False, 'error': '参数不完整'})

        # 查找要移动的BOM节点
        try:
            bom_node = Bom.objects.get(bom_id=bom_id)
        except Bom.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到指定的BOM节点'})

        # 查找目标父节点
        try:
            target_parent = Bom.objects.get(bom_id=target_parent_id)
        except Bom.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到目标父节点'})

        # 检查是否会形成循环引用
        if is_circular_reference(bom_id, target_parent_id):
            return JsonResponse({'success': False, 'error': '不能移动到自己的子节点下，这会形成循环引用'})

        # 获取目标父节点下的子节点数量，用于确定新的排序值
        sibling_count = Bom.objects.filter(bom_pid=target_parent_id).count()

        if position == 'first':
            new_order = 1
            # 将其他子节点的排序值后移
            Bom.objects.filter(bom_pid=target_parent_id).update(bom_order=F('bom_order') + 1)
        else:  # last
            new_order = sibling_count + 1

        # 更新BOM节点的父ID和排序
        bom_node.bom_pid = target_parent_id
        bom_node.bom_order = new_order
        bom_node.save()

        print(f"成功移动BOM节点 {bom_id} 到父节点 {target_parent_id} 下")

        return JsonResponse({
            'success': True,
            'message': '节点移动成功'
        })

    except Exception as e:
        print(f"移动BOM节点失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


def is_circular_reference(node_id, target_parent_id):
    """检查是否会形成循环引用"""
    current_id = target_parent_id

    while current_id and current_id != 0:
        if str(current_id) == str(node_id):
            return True

        try:
            parent_bom = Bom.objects.get(bom_id=current_id)
            current_id = parent_bom.bom_pid
        except Bom.DoesNotExist:
            break

    return False





@csrf_protect
def delete_bom_node_new(request):
    """删除BOM节点（新版本，支持递归删除）"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '请求方法错误'})

    try:
        bom_id = request.POST.get('bom_id')

        print(f"删除BOM节点参数: bom_id={bom_id}")

        if not bom_id:
            return JsonResponse({'success': False, 'error': '缺少BOM ID参数'})

        # 查找要删除的BOM节点
        try:
            bom_node = Bom.objects.get(bom_id=bom_id)
        except Bom.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到指定的BOM节点'})

        # 递归删除BOM节点及其所有子节点
        deleted_count = delete_bom_recursive(bom_node)

        print(f"成功删除BOM节点 {bom_id} 及其 {deleted_count} 个子节点")

        return JsonResponse({
            'success': True,
            'message': f'成功删除节点及其 {deleted_count} 个子节点'
        })

    except Exception as e:
        print(f"删除BOM节点失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


def delete_bom_recursive(bom_node):
    """递归删除BOM节点及其所有子节点"""
    deleted_count = 0

    # 先递归删除所有子节点
    child_boms = Bom.objects.filter(bom_pid=bom_node.bom_id)
    for child_bom in child_boms:
        deleted_count += delete_bom_recursive(child_bom)

    # 删除关联的物料（如果是临时物料）
    if bom_node.bom_material:
        try:
            material = Material.objects.get(material_id=bom_node.bom_material)
            # 只删除临时物料（tempstate > 0）
            if material.material_tempstate > 0:
                material.delete()
        except Material.DoesNotExist:
            pass

    # 删除BOM节点本身
    bom_node.delete()
    deleted_count += 1

    return deleted_count


@csrf_protect
def add_bom_child_node(request):
    """添加BOM子节点"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '请求方法错误'})

    try:
        parent_bom_id = request.POST.get('parent_bom_id')
        material_id = request.POST.get('material_id')

        # 获取BOM基本信息参数
        bom_num = request.POST.get('bom_num', 1)
        bom_lossrate = request.POST.get('bom_lossrate', 0)
        bom_partcount = request.POST.get('bom_partcount', 1)
        bom_producecount = request.POST.get('bom_producecount', 1)

        print(f"添加BOM子节点参数: parent_bom_id={parent_bom_id}, material_id={material_id}")
        print(f"BOM基本信息: num={bom_num}, lossrate={bom_lossrate}, partcount={bom_partcount}, producecount={bom_producecount}")

        if not parent_bom_id or not material_id:
            return JsonResponse({'success': False, 'error': '参数不完整'})

        # 查找父BOM节点
        try:
            parent_bom = Bom.objects.get(bom_id=parent_bom_id)
        except Bom.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到指定的父BOM节点'})

        # 查找物料记录
        try:
            material = Material.objects.get(material_id=material_id)
        except Material.DoesNotExist:
            return JsonResponse({'success': False, 'error': '未找到指定的物料记录'})

        # 获取父节点下的子节点数量，用于确定新的排序值
        sibling_count = Bom.objects.filter(bom_pid=parent_bom_id).count()

        # 计算BOM层级
        bom_level = 1  # 默认为顶级节点
        try:
            # 确保parent_bom.bom_level不为None
            parent_level = parent_bom.bom_level if parent_bom.bom_level is not None else 0
            bom_level = parent_level + 1
        except:
            # 如果出现任何错误，则默认为顶级节点
            bom_level = 1

        # 转换参数类型
        try:
            bom_num = int(bom_num)
            bom_lossrate = float(bom_lossrate)
            bom_partcount = int(bom_partcount)
            bom_producecount = int(bom_producecount)
        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'BOM参数格式错误'})

        # 创建新的BOM子节点
        new_bom = Bom(
            bom_pid=parent_bom_id,
            bom_material=material_id,
            bom_num=bom_num,
            bom_partcount=bom_partcount,
            bom_producecount=bom_producecount,
            bom_lossrate=bom_lossrate,
            bom_level=bom_level,  # 添加层级
            bom_order=sibling_count + 1,
            bom_state=0,  # 默认状态为正式
            bom_ImportUser=request.session.get('user_id'),
            # 设置BOM时间字段
            bom_addTime=get_local_now(),
            bom_updateTime=get_local_now()
        )
        new_bom.save()

        print(f"成功创建BOM子节点 {new_bom.bom_id}，父节点: {parent_bom_id}，物料: {material_id}")

        return JsonResponse({
            'success': True,
            'message': f'成功添加子件 {material.material_name}',
            'new_bom_id': new_bom.bom_id
        })

    except Exception as e:
        print(f"添加BOM子节点失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


class BomExportExcelView(View):
    """BOM导出Excel视图"""

    def post(self, request):
        """处理导出Excel请求"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return JsonResponse({'success': False, 'error': '请先登录'})

        try:
            import json
            import os
            from django.http import HttpResponse
            from openpyxl import load_workbook
            from io import BytesIO
            from .models import Bom, Material

            # 获取选中的物料数据
            selected_materials_json = request.POST.get('selected_materials', '[]')
            selected_materials = json.loads(selected_materials_json)

            print(f"导出Excel - 选中的物料数量: {len(selected_materials)}")
            print(f"导出Excel - 选中的物料数据: {selected_materials}")

            if not selected_materials:
                return JsonResponse({'success': False, 'error': '没有选择要导出的物料'})

            # 模板文件路径 - 从项目根目录开始
            from django.conf import settings
            project_root = os.path.dirname(settings.BASE_DIR)  # 获取项目根目录
            template_path = os.path.join(project_root, 'docs', 'temp_export.xlsx')

            print(f"模板文件路径: {template_path}")
            print(f"模板文件是否存在: {os.path.exists(template_path)}")

            if not os.path.exists(template_path):
                return JsonResponse({'success': False, 'error': f'模板文件不存在: {template_path}'})

            # 加载Excel模板
            workbook = load_workbook(template_path)

            # 准备物料清单数据
            material_list_data = []
            bom_details_data = []
            process_details_data = []

            for material in selected_materials:
                bom_id = material.get('bom_id')
                material_id = material.get('material_id')

                # 检查是否为level=0的顶级物料
                try:
                    bom_obj = Bom.objects.get(bom_id=bom_id, bom_state=0)
                    if bom_obj.bom_level != 0:
                        print(f"跳过非顶级物料: bom_id={bom_id}, level={bom_obj.bom_level}")
                        continue
                except Bom.DoesNotExist:
                    print(f"BOM不存在: bom_id={bom_id}")
                    continue

                # 从数据库获取完整的物料信息
                try:
                    material_obj = Material.objects.get(material_id=material_id, material_tempstate=0)

                    # 根据您的字段映射规则填充数据
                    material_info = {
                        '父件编码': material_obj.material_no or '',                    # 第一列：Material_No
                        '父件名称': material_obj.material_name or '',                  # 第二列：Material_Name
                        '规格型号': material_obj.material_spec or '',                  # 第三列：Material_Spec
                        '下料尺寸': material_obj.material_size or '',                  # 第四列：Material_Size
                        '版本号': material_obj.material_version or 'A',               # 第五列：Material_Version
                        '工艺路线编码': '',                                            # 第六列：空
                        '工艺路线': '',                                                # 第七列：空
                        '计量单位': material_obj.material_unit or '',              # 第八列：Material_Unit
                        '生产数量': material_obj.material_producecount or 1,          # 第九列：Material_ProduceCount
                        '生产车间编码': '',                                            # 第十列：空
                        '生产车间': '',                                                # 第十一列：空
                        '预入仓库编码': '',                                            # 第十二列：空
                        '预入仓库': '',                                                # 第十三列：空
                        '默认BOM': 1,                                                 # 第十四列：默认为1
                        '成品率%': 100,                                               # 第十五列：默认为100%
                        '停用': 1 if material_obj.material_state == 2 else 0,        # 第十六列：根据Material_State判断
                        '创建时间': material_obj.material_uptime.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3] if material_obj.material_uptime else ''  # 第十七列：Material_UpTime
                    }
                    material_list_data.append(material_info)

                    print(f"处理物料: {material_obj.material_no} - {material_obj.material_name}")

                except Material.DoesNotExist:
                    error_msg = f"物料不存在: material_id={material_id}, bom_id={bom_id}"
                    print(error_msg)
                    return JsonResponse({'success': False, 'error': error_msg})

                # 递归获取所有子件数据
                if bom_id:
                    self._get_all_children_recursive(bom_id, bom_details_data)

            # 获取所有物料的工序明细数据（包括顶级物料和所有子件）
            all_materials = []
            # 添加顶级物料
            for material in selected_materials:
                material_id = material.get('material_id')
                bom_id = material.get('bom_id')
                try:
                    bom_obj = Bom.objects.get(bom_id=bom_id, bom_state=0)
                    if bom_obj.bom_level == 0:  # 只处理顶级物料
                        material_obj = Material.objects.get(material_id=material_id, material_tempstate=0)
                        all_materials.append(material_obj)
                except (Bom.DoesNotExist, Material.DoesNotExist):
                    continue

            # 递归获取所有子件物料
            for material in selected_materials:
                bom_id = material.get('bom_id')
                if bom_id:
                    self._get_all_materials_recursive(bom_id, all_materials)

            # 生成工序明细数据
            self._generate_process_details(all_materials, process_details_data)

            # 填充数据到模板
            self._fill_template_data(workbook, material_list_data, bom_details_data, process_details_data)

            # 保存到内存
            output = BytesIO()
            workbook.save(output)
            output.seek(0)

            output.seek(0)

            # 生成文件名
            timestamp = get_local_now().strftime('%Y%m%d_%H%M%S')
            filename = f'物料清单导出_{timestamp}.xlsx'

            # 创建HTTP响应
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            print(f"导出Excel失败: {str(e)}")
            return JsonResponse({'success': False, 'error': f'导出失败: {str(e)}'})

    def _fill_template_data(self, workbook, material_list_data, bom_details_data, process_details_data):
        """填充数据到Excel模板"""
        try:
            # 填充物料清单工作表
            if '物料清单' in workbook.sheetnames:
                ws_material = workbook['物料清单']

                # 从第2行开始填充数据（第1行是表头）
                for row_idx, material_data in enumerate(material_list_data, start=2):
                    ws_material.cell(row=row_idx, column=1, value=material_data.get('父件编码', ''))
                    ws_material.cell(row=row_idx, column=2, value=material_data.get('父件名称', ''))
                    ws_material.cell(row=row_idx, column=3, value=material_data.get('规格型号', ''))
                    ws_material.cell(row=row_idx, column=4, value=material_data.get('下料尺寸', ''))
                    ws_material.cell(row=row_idx, column=5, value=material_data.get('版本号', 'A'))
                    ws_material.cell(row=row_idx, column=6, value=material_data.get('工艺路线编码', ''))
                    ws_material.cell(row=row_idx, column=7, value=material_data.get('工艺路线', ''))
                    ws_material.cell(row=row_idx, column=8, value=material_data.get('计量单位', ''))
                    ws_material.cell(row=row_idx, column=9, value=material_data.get('生产数量', 1))
                    ws_material.cell(row=row_idx, column=10, value=material_data.get('生产车间编码', ''))
                    ws_material.cell(row=row_idx, column=11, value=material_data.get('生产车间', ''))
                    ws_material.cell(row=row_idx, column=12, value=material_data.get('预入仓库编码', ''))
                    ws_material.cell(row=row_idx, column=13, value=material_data.get('预入仓库', ''))
                    ws_material.cell(row=row_idx, column=14, value=material_data.get('默认BOM', 1))
                    ws_material.cell(row=row_idx, column=15, value=material_data.get('成品率%', 1))
                    ws_material.cell(row=row_idx, column=16, value=material_data.get('停用', 0))
                    ws_material.cell(row=row_idx, column=17, value=material_data.get('创建时间', ''))

                print(f"填充物料清单数据: {len(material_list_data)} 行")

            # 填充子件明细工作表
            if '子件明细' in workbook.sheetnames and bom_details_data:
                ws_bom = workbook['子件明细']

                # 从第2行开始填充数据
                for row_idx, bom_data in enumerate(bom_details_data, start=2):
                    ws_bom.cell(row=row_idx, column=1, value=bom_data.get('版本号', 'A'))
                    ws_bom.cell(row=row_idx, column=2, value=bom_data.get('父件编码', ''))
                    ws_bom.cell(row=row_idx, column=3, value=bom_data.get('子件编码', ''))
                    ws_bom.cell(row=row_idx, column=4, value=bom_data.get('子件名称', ''))
                    ws_bom.cell(row=row_idx, column=5, value=bom_data.get('规格型号', ''))
                    ws_bom.cell(row=row_idx, column=6, value=bom_data.get('子件BOM', ''))
                    ws_bom.cell(row=row_idx, column=7, value=bom_data.get('子件默认BOM', 0))
                    ws_bom.cell(row=row_idx, column=8, value=bom_data.get('计量单位', ''))
                    ws_bom.cell(row=row_idx, column=9, value=bom_data.get('生产数量', 1))
                    ws_bom.cell(row=row_idx, column=10, value=bom_data.get('需用数量', 1))
                    ws_bom.cell(row=row_idx, column=11, value=bom_data.get('损耗率%', 0))
                    ws_bom.cell(row=row_idx, column=12, value=bom_data.get('预出仓库编码', ''))
                    ws_bom.cell(row=row_idx, column=13, value=bom_data.get('预出仓库', ''))
                    ws_bom.cell(row=row_idx, column=14, value=bom_data.get('领料工序编码', ''))
                    ws_bom.cell(row=row_idx, column=15, value=bom_data.get('领料工序', ''))
                    ws_bom.cell(row=row_idx, column=16, value=bom_data.get('备注', ''))
                    ws_bom.cell(row=row_idx, column=17, value=bom_data.get('表体备注', ''))

                print(f"填充子件明细数据: {len(bom_details_data)} 行")

            # 填充工序明细工作表
            if '工序明细' in workbook.sheetnames and process_details_data:
                ws_process = workbook['工序明细']

                # 从第2行开始填充数据
                for row_idx, process_data in enumerate(process_details_data, start=2):
                    ws_process.cell(row=row_idx, column=1, value=process_data.get('版本号', 'A'))
                    ws_process.cell(row=row_idx, column=2, value=process_data.get('父件编码', ''))
                    ws_process.cell(row=row_idx, column=3, value=process_data.get('加工顺序', 1))
                    ws_process.cell(row=row_idx, column=4, value=process_data.get('工序编码', ''))
                    ws_process.cell(row=row_idx, column=5, value=process_data.get('工序名称', ''))
                    ws_process.cell(row=row_idx, column=6, value=process_data.get('标准工时', ''))
                    ws_process.cell(row=row_idx, column=7, value=process_data.get('设备编码', ''))
                    ws_process.cell(row=row_idx, column=8, value=process_data.get('设备', ''))
                    ws_process.cell(row=row_idx, column=9, value=process_data.get('工价', ''))
                    ws_process.cell(row=row_idx, column=10, value=process_data.get('加工方式', ''))
                    ws_process.cell(row=row_idx, column=11, value=process_data.get('委外供应商编码', ''))
                    ws_process.cell(row=row_idx, column=12, value=process_data.get('委外供应商', ''))
                    ws_process.cell(row=row_idx, column=13, value=process_data.get('委外单价', ''))
                    ws_process.cell(row=row_idx, column=14, value=process_data.get('关键工序', '0'))

                print(f"填充工序明细数据: {len(process_details_data)} 行")

        except Exception as e:
            print(f"填充模板数据时出错: {str(e)}")
            raise e

    def _get_all_children_recursive(self, parent_bom_id, bom_details_data):
        """递归获取所有子件数据"""
        try:
            from .models import Bom, Material

            # 获取直接子件
            child_boms = Bom.objects.filter(bom_pid=parent_bom_id, bom_state=0)

            for child_bom in child_boms:
                if child_bom.bom_material:
                    try:
                        # 获取子件物料信息
                        child_material = Material.objects.get(
                            material_id=child_bom.bom_material,
                            material_tempstate=0
                        )

                        # 获取父件信息（根据bom_pid查找）
                        parent_material_no = ''
                        parent_material_version = ''
                        if child_bom.bom_pid:
                            try:
                                parent_bom = Bom.objects.get(bom_id=child_bom.bom_pid, bom_state=0)
                                if parent_bom.bom_material:
                                    parent_material = Material.objects.get(
                                        material_id=parent_bom.bom_material,
                                        material_tempstate=0
                                    )
                                    parent_material_no = parent_material.material_no or ''
                                    parent_material_version = parent_material.material_version or 'A'
                            except (Bom.DoesNotExist, Material.DoesNotExist):
                                pass

                        # 根据您的字段映射规则填充子件明细数据
                        child_detail = {
                            '版本号': parent_material_version,                                   # 第一列：根据bom_Pid查找父件Material_Version
                            '父件编码': parent_material_no,                                      # 第二列：根据bom_Pid查找父件Material_No
                            '子件编码': child_material.material_no or '',                        # 第三列：Material_No
                            '子件名称': child_material.material_name or '',                      # 第四列：Material_Name
                            '规格型号': child_material.material_spec or '',                      # 第五列：Material_Spec
                            '子件BOM':  child_material.material_version or '',                                                      # 第六列：空
                            '子件默认BOM': 0,                                                   # 第七列：默认为1
                            '计量单位': child_material.material_unit or '',                  # 第八列：Material_Unit
                            '生产数量': child_bom.bom_producecount or 1,                       # 第九列：bom_ProduceCount
                            '需用数量': child_bom.bom_partcount or 1,                          # 第十列：bom_PartCount
                            '损耗率%': child_bom.bom_lossrate or 0,                            # 第十一列：Bom_Lossrate
                            '预出仓库编码': '',                                                  # 第十二列：空
                            '预出仓库': '',                                                      # 第十三列：空
                            '领料工序编码': '',                                                  # 第十四列：空
                            '领料工序': '',                                                      # 第十五列：空
                            '备注': '',                                                         # 第十六列：空
                            '表体备注': ''                                                      # 第十七列：空
                        }

                        bom_details_data.append(child_detail)
                        print(f"添加子件: {parent_material_no} -> {child_material.material_no} ({child_material.material_name})")

                        # 递归获取子件的子件
                        self._get_all_children_recursive(child_bom.bom_id, bom_details_data)

                    except Material.DoesNotExist:
                        print(f"子件物料不存在: material_id={child_bom.bom_material}")
                        continue

        except Exception as e:
            print(f"递归获取子件数据时出错: {str(e)}")

    def _get_all_materials_recursive(self, parent_bom_id, all_materials):
        """递归获取所有子件物料对象"""
        try:
            from .models import Bom, Material

            # 获取直接子件
            child_boms = Bom.objects.filter(bom_pid=parent_bom_id, bom_state=0)

            for child_bom in child_boms:
                if child_bom.bom_material:
                    try:
                        # 获取子件物料信息
                        child_material = Material.objects.get(
                            material_id=child_bom.bom_material,
                            material_tempstate=0
                        )

                        # 避免重复添加
                        if child_material not in all_materials:
                            all_materials.append(child_material)
                            print(f"添加子件物料: {child_material.material_no} ({child_material.material_name})")

                        # 递归获取子件的子件
                        self._get_all_materials_recursive(child_bom.bom_id, all_materials)

                    except Material.DoesNotExist:
                        print(f"子件物料不存在: material_id={child_bom.bom_material}")
                        continue

        except Exception as e:
            print(f"递归获取子件物料时出错: {str(e)}")

    def _generate_process_details(self, all_materials, process_details_data):
        """生成工序明细数据"""
        try:
            from .models import Equipment

            for material in all_materials:
                # 获取父件信息（通过BOM查找）
                # parent_material_no = ''
                # parent_material_version = 'A'

                # try:
                #     # 查找该物料对应的BOM记录
                #     bom = Bom.objects.filter(bom_material=material.material_id, bom_state=0).first()
                #     if bom and bom.bom_pid > 0:
                #         # 如果有父级BOM，获取父级物料信息
                #         parent_bom = Bom.objects.get(bom_id=bom.bom_pid, bom_state=0)
                #         if parent_bom.bom_material:
                #             parent_material = Material.objects.get(
                #                 material_id=parent_bom.bom_material,
                #                 material_tempstate=0
                #             )
                #             parent_material_no = parent_material.material_no or ''
                #             parent_material_version = parent_material.material_version or 'A'
                #     else:
                #         # 如果是顶级物料，父件编码就是自己
                #         parent_material_no = material.material_no or ''
                #         parent_material_version = material.material_version or 'A'
                # except (Bom.DoesNotExist, Material.DoesNotExist):
                #     parent_material_no = material.material_no or ''
                #     parent_material_version = material.material_version or 'A'



                process_materialno = material.material_no or ''
                process_materialversion= material.material_version or 'A'
                # 获取工序相关数据
                process_route = material.material_processroute or ''
                process_times = material.material_ProcessrouteTimes or ''
                process_device = material.material_ProcessrouteDevice or ''
                process_price = material.material_ProcessroutePrice or ''
                process_type = material.material_ProcessrouteType or ''
                process_outsupplier = material.material_ProcessrouteOutSupplier or ''
                process_outprice = material.material_ProcessrouteOutPrice or ''
                process_important = material.material_ProcessrouteImportant or ''

                # 按"|"分割各个字段
                process_list = [item.strip() for item in process_route.split('|') if item.strip()]
                times_list = [item.strip() for item in process_times.split('|') if item.strip()]
                device_list = [item.strip() for item in process_device.split('|') if item.strip()]
                price_list = [item.strip() for item in process_price.split('|') if item.strip()]
                type_list = [item.strip() for item in process_type.split('|') if item.strip()]
                outsupplier_list = [item.strip() for item in process_outsupplier.split('|') if item.strip()]
                outprice_list = [item.strip() for item in process_outprice.split('|') if item.strip()]
                important_list = [item.strip() for item in process_important.split('|') if item.strip()]

                # 如果没有工序数据，跳过
                if not process_list:
                    continue

                # 以Material_Processroute的数量为准，生成对应数量的工序明细记录
                for i, process_name in enumerate(process_list):
                    # 获取工序编码（通过数据字典查找）
                    process_code = ''
                    try:
                        processes_dict = get_dictionary_items('processes')
                        for item in processes_dict:
                            if item['name'] == process_name:
                                process_code = item['code']
                                break
                    except:
                        pass

                    # 获取设备信息
                    device_code = ''
                    device_name = ''
                    if i < len(device_list):
                        device_id = device_list[i]
                        if device_id and device_id.isdigit():
                            try:
                                equipment = Equipment.objects.get(equipment_id=int(device_id))
                                device_code = equipment.equipment_no or ''
                                device_name = equipment.equipment_name or ''
                            except Equipment.DoesNotExist:
                                pass

                    # 构建工序明细记录
                    process_detail = {
                        '版本号': process_materialversion,                                    # 第一列
                        '父件编码': process_materialno,                                       # 第二列
                        '加工顺序': i + 1,                                                   # 第三列：从1开始的序号
                        '工序编码': process_code,                                            # 第四列
                        '工序名称': process_name,                                            # 第五列
                        '标准工时': times_list[i] if i < len(times_list) else '',            # 第六列
                        '设备编码': device_code,                                             # 第七列
                        '设备': device_name,                                                 # 第八列
                        '工价': price_list[i] if i < len(price_list) else '',                # 第九列
                        '加工方式': type_list[i] if i < len(type_list) else '',              # 第十列
                        '委外供应商编码': '',                                                # 第十一列：为空
                        '委外供应商': outsupplier_list[i] if i < len(outsupplier_list) else '',  # 第十二列
                        '委外单价': outprice_list[i] if i < len(outprice_list) else '',      # 第十三列
                        '关键工序': important_list[i] if i < len(important_list) else '0'    # 第十四列
                    }

                    process_details_data.append(process_detail)
                    print(f"添加工序明细: {process_materialno} - {process_name} (序号: {i + 1})")

        except Exception as e:
            print(f"生成工序明细数据时出错: {str(e)}")


class SystemLogView(View):
    """系统日志管理视图"""

    def get(self, request):
        """显示日志列表"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')

        # 获取用户信息
        user_id = request.session.get('user_id')
        username = request.session.get('username', '未知用户')

        try:
            user = User.objects.get(user_id=user_id)
            user_nick = user.user_nick or username
        except User.DoesNotExist:
            user_nick = username

        # 获取查询参数
        action = request.GET.get('action', '')
        module = request.GET.get('module', '')
        user_name = request.GET.get('user_name', '')
        start_date = request.GET.get('start_date', '')
        end_date = request.GET.get('end_date', '')
        page = request.GET.get('page', 1)
        page_size = int(request.GET.get('page_size', 20))

        # 构建查询条件
        query = Q()

        if action:
            query &= Q(log_action__icontains=action)

        if module:
            query &= Q(log_module__icontains=module)

        if user_name:
            query &= Q(log_user_name__icontains=user_name)

        if start_date:
            try:
                from datetime import datetime
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                query &= Q(log_time__gte=start_datetime)
            except ValueError:
                pass

        if end_date:
            try:
                from datetime import datetime
                end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                query &= Q(log_time__lte=end_datetime)
            except ValueError:
                pass

        # 执行查询
        logs = SystemLog.objects.filter(query).order_by('-log_time')

        # 分页
        paginator = Paginator(logs, page_size)
        try:
            page_obj = paginator.page(page)
        except PageNotAnInteger:
            page_obj = paginator.page(1)
        except EmptyPage:
            page_obj = paginator.page(paginator.num_pages)

        # 获取统计信息
        total_logs = logs.count()

        # 获取操作类型和模块类型的选项（用于筛选下拉框）
        # 定义系统中的标准操作类型
        standard_actions = [
            '用户登录', '新增物料', '修改物料', '删除物料', '新增版本',
            '导入BOM', '确认导入BOM', '导出Excel', '清除日志'
        ]

        # 定义系统中的标准模块类型
        standard_modules = [
            '物料管理', 'BOM管理', '系统管理'
        ]

        # 获取数据库中实际存在的操作类型，只保留标准类型
        db_actions = SystemLog.objects.values_list('log_action', flat=True).distinct()
        action_choices = [action for action in standard_actions if action in db_actions]

        # 获取数据库中实际存在的模块类型，只保留标准类型
        db_modules = SystemLog.objects.values_list('log_module', flat=True).distinct()
        module_choices = [module for module in standard_modules if module in db_modules]

        context = {
            'username': username,
            'user_nick': user_nick,
            'logs': page_obj,
            'total_logs': total_logs,
            'action_choices': action_choices,
            'module_choices': module_choices,
            'query_params': {
                'action': action,
                'module': module,
                'user_name': user_name,
                'start_date': start_date,
                'end_date': end_date,
                'page_size': page_size,
            }
        }

        return render(request, 'bom_sys/system_log.html', context)

    def post(self, request):
        """处理日志清除操作"""
        # 检查是否已登录
        if not request.session.get('is_login', False):
            return redirect('login')

        action = request.POST.get('action')

        if action == 'clear_logs':
            days = request.POST.get('days', '30')
            try:
                days = int(days)
                if days <= 0:
                    messages.error(request, '天数必须大于0')
                    return redirect('system_log')

                # 计算删除日期
                from datetime import datetime, timedelta
                delete_before = datetime.now() - timedelta(days=days)

                # 删除指定天数前的日志
                deleted_count = SystemLog.objects.filter(log_time__lt=delete_before).count()
                SystemLog.objects.filter(log_time__lt=delete_before).delete()

                # 记录清除日志的操作
                log_system_action(
                    request=request,
                    action='清除日志',
                    module='系统管理',
                    description=f'清除了{days}天前的{deleted_count}条日志记录'
                )

                messages.success(request, f'成功清除了{days}天前的{deleted_count}条日志记录')

            except ValueError:
                messages.error(request, '天数格式不正确')
            except Exception as e:
                messages.error(request, f'清除日志失败: {str(e)}')

        return redirect('system_log')


@csrf_protect
def bom_export_excel_template2(request):
    """BOM导出Excel模板二"""
    import json
    import openpyxl
    import os
    from django.conf import settings
    import tempfile

    print("=== 模板二导出开始 ===")

    if request.method != 'POST':
        print("请求方法错误")
        return JsonResponse({'success': False, 'error': '请求方法错误'})

    try:
        # 检查是否已登录
        if not request.session.get('is_login', False):
            print("用户未登录")
            return JsonResponse({'success': False, 'error': '请先登录'})

        # 获取选中的物料数据
        selected_materials_json = request.POST.get('selected_materials', '[]')
        selected_materials = json.loads(selected_materials_json)

        print(f"选中的物料数量: {len(selected_materials)}")

        if not selected_materials:
            print("没有选中的物料")
            return JsonResponse({'success': False, 'error': '没有选中的物料'})



        # 加载模板文件
        project_root = os.path.dirname(settings.BASE_DIR)  # 获取项目根目录
        template_path = os.path.join(project_root, 'docs', 'temp_export2.xlsx')
        print(f"模板文件路径: {template_path}")

        if not os.path.exists(template_path):
            print("模板文件不存在")
            return JsonResponse({'success': False, 'error': '模板文件不存在'})

        print("开始加载模板文件")
        # 打开模板文件
        wb = openpyxl.load_workbook(template_path)
        ws = wb.active
        print("模板文件加载成功")

        # 收集所有需要导出的BOM数据
        all_bom_data = []

        print("开始收集BOM数据")
        for material_data in selected_materials:
            bom_id = material_data.get('bom_id')
            print(f"处理BOM ID: {bom_id}")

            if not bom_id:
                print("BOM ID为空，跳过")
                continue

            try:
                # 获取顶级BOM
                root_bom = Bom.objects.get(bom_id=bom_id)
                print(f"找到顶级BOM: {root_bom.bom_id}")

                # 递归获取所有子BOM
                bom_tree_data = []
                collect_bom_tree_data(root_bom, bom_tree_data, 0)
                print(f"收集到 {len(bom_tree_data)} 条BOM数据")
                all_bom_data.extend(bom_tree_data)

            except Bom.DoesNotExist:
                print(f"BOM ID {bom_id} 不存在")
                continue

        print(f"总共收集到 {len(all_bom_data)} 条BOM数据")

        # 写入数据到Excel
        print("开始写入Excel数据")
        write_bom_data_to_excel_template2(ws, all_bom_data)
        print("Excel数据写入完成")

        # 保存到临时文件
        print("开始保存临时文件")
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        wb.save(temp_file.name)
        temp_file.close()
        print(f"临时文件保存完成: {temp_file.name}")

        # 读取文件内容
        print("读取文件内容")
        with open(temp_file.name, 'rb') as f:
            file_content = f.read()
        print(f"文件大小: {len(file_content)} 字节")

        # 删除临时文件
        os.unlink(temp_file.name)
        print("临时文件已删除")

        # 返回文件
        print("返回文件响应")
        response = HttpResponse(
            file_content,
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="BOM清单导出_模板二.xlsx"'
        print("=== 模板二导出完成 ===")
        return response

    except Exception as e:
        import traceback
        print(f"模板二导出失败: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
        return JsonResponse({'success': False, 'error': str(e)})


def collect_bom_tree_data(bom_node, result_list, level):
    """递归收集BOM树数据"""
    import json
    try:
        # 获取物料信息
        material = None
        if bom_node.bom_material:
            try:
                material = Material.objects.get(material_id=bom_node.bom_material)
            except Material.DoesNotExist:
                pass

        # 解析存货属性
        inventory_attrs = {}
        if material and material.material_inventory:
            try:
                inventory_attrs = json.loads(material.material_inventory)
            except json.JSONDecodeError:
                pass

        # 构建BOM数据
        bom_data = {
            'level': level,
            'bom_id': bom_node.bom_id,
            'bom_num': bom_node.bom_num or 1,
            'bom_produce_count': bom_node.bom_producecount or '',
            'bom_part_count': bom_node.bom_partcount or '',
            'bom_loss_rate': bom_node.bom_lossrate or '',
            'material_no': material.material_no if material else '',
            'material_drawing_no': material.material_drawingno if material else '',
            'material_name': material.material_name if material else '',
            'material_quality': material.material_quality if material else '',
            'material_spec': material.material_spec if material else '',
            'material_version': material.material_version if material else '',
            'material_unit': material.material_unit if material else '',
            'material_customer_name': material.material_customername if material else '',
            'material_attr_name': material.material_attrname if material else '',
            'material_img': material.material_img if material else '',
            'material_supp_casting': material.material_supp_casting if material else '',
            'material_supp_machining': material.material_supp_machining if material else '',
            'material_supp_sheetmetal': material.material_supp_sheetmetal if material else '',
            'material_supp_purchase': material.material_supp_purchase if material else '',
            'material_supp_prochasemanager': material.material_supp_prochasemanager if material else '',
            'material_processroute': material.material_processroute if material else '',
            'inventory_attrs': inventory_attrs
        }

        result_list.append(bom_data)

        # 递归处理子节点
        child_boms = Bom.objects.filter(bom_pid=bom_node.bom_id).order_by('bom_id')
        for child_bom in child_boms:
            collect_bom_tree_data(child_bom, result_list, level + 1)

    except Exception as e:
        print(f"收集BOM数据失败: {str(e)}")


def write_bom_data_to_excel_template2(ws, bom_data_list):
    """将BOM数据写入Excel模板二"""
    try:
        # 从第2行开始写入数据（第1行是表头）
        row_num = 2

        for index, bom_data in enumerate(bom_data_list):
            # 第1列：序号
            ws.cell(row=row_num, column=1, value=index + 1)

            # 第2-9列：BOM层级（只在对应层级列填入数值）
            level = bom_data['level']
            for col in range(2, 10):  # 列2-9
                if col - 2 == level:  # 如果是当前层级对应的列
                    ws.cell(row=row_num, column=col, value=level)


            # 第10列：存货编码
            ws.cell(row=row_num, column=10, value=bom_data['material_no'])

            # 第11列：客户图号
            ws.cell(row=row_num, column=11, value=bom_data['material_drawing_no'])

            # 第12列：数量
            ws.cell(row=row_num, column=12, value=bom_data['bom_num'])

            # 第13列：图片（插入实际图片）
            insert_image_to_cell(ws, row_num, 13, bom_data['material_img'])

            # 第14列：物料名称
            ws.cell(row=row_num, column=14, value=bom_data['material_name'])

            # 第15列：材质
            ws.cell(row=row_num, column=15, value=bom_data['material_quality'])

            # 第16列：规格
            ws.cell(row=row_num, column=16, value=bom_data['material_spec'])

            # 第17列：版本
            ws.cell(row=row_num, column=17, value=bom_data['material_version'])

            # 第18列：生产数量
            ws.cell(row=row_num, column=18, value=bom_data['bom_produce_count'])

            # # 第19列：零件用量
            # ws.cell(row=row_num, column=19, value=bom_data['bom_part_count'])

            # 第20列：计量单位
            ws.cell(row=row_num, column=20, value=bom_data['material_unit'])

            # 第21列：损耗率
            ws.cell(row=row_num, column=21, value=bom_data['bom_loss_rate'])

            # 第22列：客户
            ws.cell(row=row_num, column=22, value=bom_data['material_customer_name'])

            # 第23列：自制时填写（为空）
            ws.cell(row=row_num, column=23, value='')

            # 第24列：物料属性
            ws.cell(row=row_num, column=24, value=bom_data['material_attr_name'])

            # 第25-31列：存货属性（"是"显示为"Y"，"否"显示为"—"）
            inventory_attrs = bom_data['inventory_attrs']

            def format_inventory_value(value):
                """格式化存货属性值：是→Y，否→—"""
                return "Y" if value == "是" else "—"

            ws.cell(row=row_num, column=25, value=format_inventory_value(inventory_attrs.get('customer_supply', '否')))  # 客供
            ws.cell(row=row_num, column=26, value=format_inventory_value(inventory_attrs.get('outsourcing', '否')))  # 外购
            ws.cell(row=row_num, column=27, value=format_inventory_value(inventory_attrs.get('sales', '否')))  # 销售
            ws.cell(row=row_num, column=28, value=format_inventory_value(inventory_attrs.get('self_made', '否')))  # 自制
            ws.cell(row=row_num, column=29, value=format_inventory_value(inventory_attrs.get('production_consumption', '否')))  # 生产耗用
            ws.cell(row=row_num, column=30, value=format_inventory_value(inventory_attrs.get('virtual_item', '否')))  # 虚拟件
            ws.cell(row=row_num, column=31, value=format_inventory_value(inventory_attrs.get('batch_management', '否')))  # 批号管理

            # 第32列：下料尺寸
            ws.cell(row=row_num, column=32, value=inventory_attrs.get('cutting_size', ''))

            # 第33-37列：供应商信息
            ws.cell(row=row_num, column=33, value=bom_data['material_supp_casting'])  # 铸造
            ws.cell(row=row_num, column=34, value=bom_data['material_supp_machining'])  # 机加
            ws.cell(row=row_num, column=35, value=bom_data['material_supp_sheetmetal'])  # 钣金
            ws.cell(row=row_num, column=36, value=bom_data['material_supp_purchase'])  # 采购
            ws.cell(row=row_num, column=37, value=bom_data['material_supp_prochasemanager'])  # 采购负责人

            # 第38列：工艺路线编号（为空）
            ws.cell(row=row_num, column=38, value='')

            # 第39列开始：工艺路线详情
            processroute = bom_data['material_processroute']
            if processroute:
                # 按"|"分割工艺路线
                process_steps = processroute.split('|')
                for i, step in enumerate(process_steps):
                    if step.strip():  # 如果步骤不为空
                        col_index = 39 + i  # 从AM列（第39列）开始
                        if col_index <= 100:  # 限制最大列数，避免超出Excel限制
                            ws.cell(row=row_num, column=col_index, value=step.strip())

            row_num += 1

    except Exception as e:
        print(f"写入Excel数据失败: {str(e)}")


def insert_image_to_cell(ws, row, col, image_path):
    """将图片插入到Excel单元格中"""
    try:
        if not image_path or image_path.strip() == '':
            # 如果没有图片路径，跳过
            return

        import os
        from django.conf import settings

        # 构建完整的图片路径
        if image_path.startswith('/'):
            # 如果是绝对路径（从media开始）
            full_image_path = os.path.join(settings.BASE_DIR, image_path.lstrip('/'))
        else:
            # 如果是相对路径
            full_image_path = os.path.join(settings.MEDIA_ROOT, image_path)

        print(f"尝试插入图片: {full_image_path}")

        # 检查文件是否存在
        if not os.path.exists(full_image_path):
            print(f"图片文件不存在: {full_image_path}")
            # 在单元格中填入"无图片"
            ws.cell(row=row, column=col, value="无图片")
            return

        # 尝试导入openpyxl的Image类
        try:
            from openpyxl.drawing.image import Image
        except ImportError:
            try:
                from openpyxl.drawing import Image
            except ImportError:
                print("无法导入openpyxl Image类，跳过图片插入")
                ws.cell(row=row, column=col, value=image_path)
                return

        # 创建图片对象
        try:
            img = Image(full_image_path)

            # 设置图片大小（可以根据需要调整）
            img.width = 80  # 设置宽度为80像素
            img.height = 60  # 设置高度为60像素

            # 计算单元格位置
            from openpyxl.utils import get_column_letter
            cell_address = f"{get_column_letter(col)}{row}"

            # 将图片锚定到单元格
            img.anchor = cell_address

            # 添加图片到工作表
            ws.add_image(img)

            print(f"图片插入成功: {cell_address}")

        except Exception as img_error:
            print(f"图片处理失败: {str(img_error)}")
            # 如果图片处理失败，在单元格中填入路径
            ws.cell(row=row, column=col, value=image_path)

    except Exception as e:
        print(f"插入图片到单元格失败: {str(e)}")
        # 如果出现任何错误，在单元格中填入路径
        ws.cell(row=row, column=col, value=image_path or "")


@csrf_protect
def check_material_delete_impact(request):
    """检查物料删除的BOM影响"""
    if request.method != 'GET':
        return JsonResponse({'success': False, 'error': '请求方法错误'})

    try:
        material_id = request.GET.get('material_id')

        if not material_id:
            return JsonResponse({'success': False, 'error': '缺少物料ID参数'})

        # 查找使用该物料的BOM记录
        affected_boms = Bom.objects.filter(bom_material=material_id).select_related()

        bom_list = []
        for bom in affected_boms:
            # 获取父物料信息
            parent_material_name = '根节点'
            parent_material_code = ''
            parent_material_drawing = ''

            if bom.bom_pid and bom.bom_pid != 0:
                try:
                    parent_bom = Bom.objects.get(bom_id=bom.bom_pid)
                    if parent_bom.bom_material:
                        parent_material = Material.objects.get(material_id=parent_bom.bom_material)
                        parent_material_name = parent_material.material_name
                        parent_material_code = parent_material.material_no or ''
                        parent_material_drawing = parent_material.material_drawingno or ''
                except (Bom.DoesNotExist, Material.DoesNotExist):
                    parent_material_name = '未知'

            bom_info = {
                'bom_id': bom.bom_id,
                'parent_material_name': parent_material_name,
                'parent_material_code': parent_material_code,
                'parent_material_drawing': parent_material_drawing,
                'bom_level': bom.bom_level or 1,
                'bom_num': bom.bom_num or 1,
                'bom_state': '启用' if bom.bom_state == 0 else '停用'
            }
            bom_list.append(bom_info)

        return JsonResponse({
            'success': True,
            'has_impact': len(bom_list) > 0,
            'affected_boms': bom_list,
            'count': len(bom_list)
        })

    except Exception as e:
        print(f"检查物料删除影响失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


def download_import_template(request):
    """
    下载导入模板文件
    """
    import os
    from django.http import FileResponse, Http404
    from django.conf import settings

    try:
        # 模板文件路径 - 尝试多种可能的路径
        possible_paths = [
            os.path.join(settings.BASE_DIR.parent, 'docs', 'import_example.xlsx'),  # 项目根目录/docs
            os.path.join(settings.BASE_DIR, 'docs', 'import_example.xlsx'),         # bom_sys/docs
            os.path.join(settings.BASE_DIR, '..', 'docs', 'import_example.xlsx'),  # 相对路径
        ]

        template_path = None
        for path in possible_paths:
            if os.path.exists(path):
                template_path = path
                break

        print(f"[DEBUG] BASE_DIR: {settings.BASE_DIR}")
        print(f"[DEBUG] BASE_DIR.parent: {settings.BASE_DIR.parent}")
        print(f"[DEBUG] 模板文件路径: {template_path}")
        print(f"[DEBUG] 文件是否存在: {os.path.exists(template_path)}")

        # 检查文件是否存在
        if not os.path.exists(template_path):
            print(f"模板文件不存在: {template_path}")
            # 尝试列出docs目录的内容
            docs_dir = os.path.join(settings.BASE_DIR.parent, 'docs')
            if os.path.exists(docs_dir):
                print(f"docs目录存在，内容: {os.listdir(docs_dir)}")
            else:
                print(f"docs目录不存在: {docs_dir}")
            raise Http404("导入模板文件不存在")

        # 检查文件大小
        file_size = os.path.getsize(template_path)
        if file_size == 0:
            print(f"模板文件为空: {template_path}")
            raise Http404("导入模板文件损坏")

        print(f"开始下载模板文件: {template_path}, 大小: {file_size} 字节")

        # 返回文件响应
        response = FileResponse(
            open(template_path, 'rb'),
            as_attachment=True,
            filename='BOM导入模板.xlsx'
        )

        # 设置中文文件名和正确的Content-Type
        response['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response['Content-Disposition'] = 'attachment; filename="BOM导入模板.xlsx"; filename*=UTF-8\'\'BOM%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'

        return response

    except Http404:
        # 重新抛出Http404异常
        raise
    except Exception as e:
        print(f"下载导入模板失败: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        raise Http404("下载模板文件失败")



