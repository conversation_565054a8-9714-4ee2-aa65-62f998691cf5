/**
 * 物料清单列表页面的JavaScript功能
 */
$(document).ready(function() {
    // 初始化客户名称下拉框
    $('#customer_id').select2({
        theme: 'bootstrap-5',
        width: '100%',
        allowClear: true,
        tags: true, // 允许用户输入不在列表中的值
        placeholder: '请选择或输入客户名称',
        language: {
            noResults: function() {
                return "没有找到匹配的客户";
            },
            searching: function() {
                return "搜索中...";
            }
        },
        // 启用搜索功能
        minimumInputLength: 0,
        // 自定义匹配方法，同时匹配名称和代码
        matcher: function(params, data) {
            // 如果没有搜索词，返回所有数据
            if ($.trim(params.term) === '') {
                return data;
            }

            // 搜索词转为小写
            var term = params.term.toLowerCase();

            // 获取选项文本和代码
            var text = data.text.toLowerCase();
            var code = $(data.element).data('code') ? $(data.element).data('code').toLowerCase() : '';

            // 如果名称或代码包含搜索词，返回该数据
            if (text.indexOf(term) > -1 || code.indexOf(term) > -1) {
                return data;
            }

            // 否则返回null表示不匹配
            return null;
        }
    });

    // ==================== 保存相关功能（与bom_tree_view.html保持一致） ====================

    // 初始化遮罩层（动态创建，与bom_tree_view.html保持一致）
    function initSavingOverlay() {
        // 调试：检查遮罩层是否存在
        let overlayCheck = document.getElementById('savingOverlay');
        console.log('页面加载时遮罩层检查:', overlayCheck ? '存在' : '不存在');

        // 如果遮罩层不存在，动态创建一个
        if (!overlayCheck) {
            console.log('遮罩层不存在，动态创建...');
            const overlay = document.createElement('div');
            overlay.id = 'savingOverlay';
            overlay.className = 'saving-overlay';
            overlay.style.display = 'none';
            overlay.innerHTML = `
                <div class="saving-content">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">保存中...</span>
                    </div>
                    <div class="mt-3">
                        <h5>保存中，请稍候...</h5>
                        <p class="text-muted">正在保存物料信息</p>
                    </div>
                </div>
            `;
            document.body.appendChild(overlay);
            console.log('遮罩层已动态创建');
            overlayCheck = overlay;
        }

        if (overlayCheck) {
            console.log('遮罩层元素:', overlayCheck);
            console.log('遮罩层样式:', window.getComputedStyle(overlayCheck));
        }
    }

    // 页面加载时初始化遮罩层
    initSavingOverlay();

    // 保存物料按钮事件
    $("#saveMaterial").on("click", function() {
        const iframe = document.getElementById('materialIframe');
        if (iframe && iframe.contentWindow) {
            try {
                // 显示保存遮罩层
                console.log("显示保存遮罩层");
                showSavingOverlay();

                // 设置保存超时
                if (window.saveTimeout) {
                    clearTimeout(window.saveTimeout);
                }
                window.saveTimeout = setTimeout(function() {
                    hideSavingOverlay();
                    showErrorMessage('保存超时，请检查网络连接后重试');
                }, 30000);

                // 检查是否为新增模式
                let isAddMode = false;
                let currentBomId = null;

                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const formModeInput = iframeDoc.getElementById('formMode');
                    isAddMode = formModeInput && formModeInput.value === 'add';
                } catch (e) {
                    // 如果无法访问iframe内容，从URL判断
                    const iframeSrc = iframe.src;
                    isAddMode = !iframeSrc.includes('edit_id=');
                }

                // 使用postMessage发送保存命令，包含必要参数
                const messageData = {
                    action: 'save',
                    bomId: currentBomId,
                    isAddMode: isAddMode
                };

                iframe.contentWindow.postMessage(messageData, '*');
                console.log("发送保存命令到iframe:", messageData);

                // 启动iframe监听（关键：添加URL检测机制）
                monitorIframeForSuccess();
            } catch (e) {
                console.error('保存失败:', e);
                hideSavingOverlay();
                showErrorMessage('保存失败：' + e.message);
            }
        } else {
            showErrorMessage("请先选择一个物料进行编辑");
        }
    });

    // 监听来自iframe的消息（与bom_tree_view.html保持一致）
    window.addEventListener('message', function(event) {
        console.log('收到iframe消息:', event.data);
        if (event.data && event.data.action === 'refreshBomData') {
            console.log('处理刷新消息');

            // 清除保存超时
            if (window.saveTimeout) {
                clearTimeout(window.saveTimeout);
                window.saveTimeout = null;
            }

            // 隐藏保存遮罩层
            hideSavingOverlay();

            if (event.data.success) {
                // 重新加载物料列表数据
                console.log('物料保存成功，刷新列表');

                // 延迟刷新页面，让用户看到成功提示
                setTimeout(function() {
                    window.location.reload();
                }, 500);
            } else {
                // 显示错误消息
                showErrorMessage('保存失败：' + (event.data.error || event.data.message || '未知错误'));
            }
        }
    });

    // 显示保存遮罩层（与bom_tree_view.html保持一致）
    function showSavingOverlay() {
        console.log("显示保存遮罩层");
        const overlay = document.getElementById('savingOverlay');
        if (overlay) {
            overlay.style.display = 'flex';
            console.log("遮罩层已显示，display:", overlay.style.display);
        } else {
            console.error("未找到遮罩层元素");
            // 如果遮罩层不存在，尝试重新创建
            initSavingOverlay();
            const newOverlay = document.getElementById('savingOverlay');
            if (newOverlay) {
                newOverlay.style.display = 'flex';
                console.log("重新创建遮罩层并显示");
            }
        }
    }

    // 隐藏保存遮罩层（与bom_tree_view.html保持一致）
    function hideSavingOverlay() {
        const overlay = document.getElementById('savingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
            console.log("遮罩层已隐藏");
        }
    }

    // 添加iframe加载完成监听，用于检测保存成功（完全按照bom_tree_view.html的实现）
    function monitorIframeForSuccess() {
        const iframe = document.getElementById('materialIframe');
        if (iframe) {
            iframe.onload = function() {
                try {
                    // 检查iframe的URL是否包含success=true
                    const iframeUrl = iframe.contentWindow.location.href;
                    console.log('iframe加载完成，URL:', iframeUrl);

                    if (iframeUrl.includes('success=true')) {
                        console.log('检测到保存成功，手动触发刷新');

                        // 清除保存超时
                        if (window.saveTimeout) {
                            clearTimeout(window.saveTimeout);
                            window.saveTimeout = null;
                        }

                        // 隐藏保存遮罩层
                        hideSavingOverlay();

                        // 刷新物料列表
                        console.log('物料保存成功，刷新列表');
                        location.reload();
                    }
                } catch (e) {
                    console.log('无法访问iframe URL（跨域限制）');
                }
            };
        }
    }

    // 显示错误消息
    function showErrorMessage(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            alert(message);
        }
    }
    
    // 清空搜索条件按钮
    $('#clearSearch').on('click', function() {
        // 清空所有输入框和下拉框
        $('#material_code').val('');
        $('#drawing_number').val('');
        $('#customer_id').val('').trigger('change');
        $('#material_state').val('1'); // 默认设为启用状态
        
        // 保留每页显示数量和iframe参数
        const perPage = $('input[name="per_page"]').val();
        const isIframe = $('input[name="iframe"]').val();
        
        
        // 恢复每页显示数量
        $('input[name="per_page"]').val(perPage);
        
        // 如果有iframe参数，恢复它
        if(isIframe) {
            $('input[name="iframe"]').val(isIframe);
        }
        
        // 恢复状态为启用
        $('#material_state').val('1');
        
        // 提交表单
        $('#searchForm').submit();
    });

    // 确保页面第一次加载时默认状态为"启用"
    $(window).on('load', function() {
        if (!window.location.search || window.location.search === '?iframe=1') {
            // 如果没有查询参数或只有iframe参数，设置状态为启用并提交表单
            $('#material_state').val('1');
            if (!window.location.search) {
                $('#searchForm').submit();
            }
        }
    });
    
    // 修复分页时搜索条件丢失问题
    $('a.page-link').each(function() {
        var href = $(this).attr('href');
        if (href && !href.startsWith('#')) {
            // 确保URL中包含所有搜索参数
            var searchParams = new URLSearchParams(window.location.search);
            var pageParams = new URLSearchParams(href);
            
            // 保留page参数
            var page = pageParams.get('page');
            
            // 重建URL，确保包含所有当前参数
            var newUrl = '?';
            if (page) {
                newUrl += 'page=' + page;
            }
            
            // 添加所有其他参数
            searchParams.forEach(function(value, key) {
                if (key !== 'page') {
                    // 特殊处理material_state为空字符串的情况
                    if (key === 'material_state' && value === '') {
                        newUrl += '&' + key + '=';
                    } else if (value) {
                        newUrl += '&' + key + '=' + value;
                    }
                }
            });
            
            $(this).attr('href', newUrl);
        }
    });
});
