// jQuery已通过script标签引入，直接使用
const $ = window.jQuery // Declare the $ variable to fix lint/correctness/noUndeclaredVariables error

let selectedMaterial = null
let allMaterials = []
let currentView = "customer-list" // 'customer-list' 或 'material-tree'
let selectedCustomer = null
let mockTreeData = []

// 添加全局变量，用于标记是否刚添加了父件
let justAddedParentMaterial = false;
let expectedNewCount = 0;

// 初始化页面
function init(customersFromDjango = null) {
  // 如果从Django模板传递了客户数据，则使用它
  if (customersFromDjango && Array.isArray(customersFromDjango) && customersFromDjango.length > 0) {
    mockTreeData = customersFromDjango
  }

  // 此处后续会添加真实数据获取逻辑
  // 目前使用模拟数据
  extractAllMaterials()
  showCustomerList() // 默认显示客户列表
  bindEvents()
}

// 提取所有物料数据
function extractAllMaterials() {
  allMaterials = []
  function traverse(nodes) {
    nodes.forEach((node) => {
      // 将所有节点都添加到物料列表中，不仅仅是type === "material"的节点
      allMaterials.push(node)
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  traverse(mockTreeData)

}

// 提取客户信息
function extractCustomers() {
  return mockTreeData.map((customer) => ({
    id: customer.id,
    name: customer.Customer_Name || customer.name,
    code: customer.Customer_Code || customer.materialCode,
    order: customer.Customer_Order || 0
  })).sort((a, b) => a.order - b.order); // 按Customer_Order排序
}

// 显示客户列表
function showCustomerList() {
  currentView = "customer-list"
  selectedCustomer = null

  const container = $("#bomTree")
  container.empty()

  const customers = extractCustomers()

  customers.forEach((customer) => {
    const customerElement = $(`
      <div class="customer-item" data-id="${customer.id}">
        <div class="customer-content">
          <div class="customer-info">
            <i class="fas fa-building customer-icon"></i>
            <div class="customer-text">
              <div class="customer-name">${customer.name}</div>
              <div class="text-muted customer-code">${customer.code}</div>
            </div>
          </div>
          <div class="customer-meta">
            <i class="fas fa-chevron-right customer-arrow ml-2"></i>
          </div>
        </div>
      </div>
    `)
    container.append(customerElement)
  })

  // 更新面包屑
  updateBreadcrumb(["客户列表"])

  // 更新搜索框placeholder
  $("#searchInput").attr("placeholder", "输入客户名称或编码进行搜索...")
  


}

// 显示客户的物料树
function showCustomerMaterialTree(customerId) {
  const customer = mockTreeData.find((c) => c.id === customerId)
  if (!customer) return

  currentView = "material-tree"
  selectedCustomer = customer

  // 获取客户代码
  const customerCode = customer.Customer_Code || customer.code
  
  // 显示加载状态
  const container = $("#bomTree")
  container.html('<div class="text-center p-5"><i class="fas fa-spinner fa-spin mr-2"></i>正在加载BOM数据...</div>')
  
  // 记录重试次数
  let retryCount = 0;
  
  // 定义加载函数，以便可以重试
  function loadBomData() {
    // 检查当前页面URL，判断是否为导入预览页面
    const isImportPreview = window.location.pathname.includes('bom_import_preview');

    
    // 通过AJAX请求获取客户的BOM数据
    $.ajax({
      url: '/get_customer_bom/',
      type: 'GET',
      data: { 
        customer_code: customerCode,
        is_temp: isImportPreview // 如果是导入预览页面，则获取临时数据
      },
      dataType: 'json',
      success: function(response) {
        if (response.success && response.bom_tree) {
          // 检查是否刚添加了父件，且需要重试
          if (justAddedParentMaterial && response.bom_tree.length < expectedNewCount && retryCount < 3) {
            retryCount++;
            container.html(`<div class="text-center p-5"><i class="fas fa-spinner fa-spin mr-2"></i>正在同步数据，请稍候...(${retryCount}/3)</div>`);

            // 延迟1秒后重试
            setTimeout(loadBomData, 1000);
            return;
          }
          
          // 重置标记
          justAddedParentMaterial = false;
          expectedNewCount = 0;
          
          // 将BOM数据添加到客户节点的children属性中
          customer.children = response.bom_tree;

          
          // 直接渲染BOM树数据，而不是整个客户节点
          renderTree(response.bom_tree);
          expandAllNodes(true);
          
        } else {
          // 如果没有BOM数据，显示空树
          container.html(`<div class="alert alert-info">该客户暂无BOM数据</div>`);
        }
      },
      error: function(xhr, status, error) {
        console.error("获取BOM数据失败:", error);
        try {
          const errorData = JSON.parse(xhr.responseText);
          container.html(`<div class="alert alert-danger">获取BOM数据失败: ${errorData.message || error}</div>`);
        } catch(e) {
          container.html(`<div class="alert alert-danger">获取BOM数据失败: ${error}</div>`);
        }
      }
    });
  }
  
  // 开始加载数据
  loadBomData();

  // 更新面包屑
  updateBreadcrumb(["客户列表", customer.Customer_Name || customer.name])

  // 更新搜索框placeholder
  $("#searchInput").attr("placeholder", "输入客户图号、物料编码、物料名称等进行搜索...")


}

// 渲染树形结构
function renderTree(data) {
  const container = $("#bomTree")
  container.empty()
  

  
  // 计算最大层级深度
  let maxLevel = 0;
  function calculateMaxDepth(nodes, level) {
    if (!nodes || nodes.length === 0) return;
    maxLevel = Math.max(maxLevel, level);
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        calculateMaxDepth(node.children, level + 1);
      }
    });
  }
  calculateMaxDepth(data, 0);

  renderNodes(data, container, 0)
}

// 渲染节点
function renderNodes(nodes, container, level) {
  if (!nodes) return;
  
  nodes.forEach((node) => {
    const nodeElement = createNodeElement(node, level)
    container.append(nodeElement)

    if (node.children && node.children.length > 0) {
      
      if (node.expanded) {
        const childContainer = $('<div class="node-children"></div>')
        container.append(childContainer)
        // 递归渲染子节点，层级+1
        renderNodes(node.children, childContainer, level + 1)
      }
    }
  })
}

// 创建节点元素
function createNodeElement(node, level) {
  let nodeClass = "tree-node"
  if (level === 0) nodeClass += " parent"
  else if (level === 1) nodeClass += " child"
  else nodeClass += " grandchild level-" + level

  let nodeContent = ""
  
  // 确保显示正确的名称和编号，避免显示"N/A"
  const displayName = node.materialName || node.name || "";
  const displayDrawingNo = node.drawingNumber || "";
  const displayCode = node.materialCode || "";
  const displayVersion = node.version || "";

  // 检查是否有子节点
  const hasChildren = node.children && node.children.length > 0;
  
  // 添加层级指示，便于调试
  const levelIndicator = level > 2 ? `[L${level}] ` : '';
  
  if (node.type === "customer" || node.type === "series" || hasChildren) {
    // 有子节点的节点显示展开图标和物料信息
    nodeContent = `
    <div class="node-content ${node.materialTempState == '2' ? 'bg-warning' : ''}">
      <div class="node-info">
        <i class="expand-icon fas fa-chevron-right ${node.expanded ? "expanded" : ""}" data-id="${node.id}"></i>
        <div class="node-text">
          <div><strong>${levelIndicator}${displayDrawingNo}</strong> - ${node.materialCategory || ''}${node.materialAttr ? '(' + node.materialAttr + ')' : ''}</div>
          <div class="text-muted" style="font-size: 0.75rem;"> ${node.materialNo || ''} - ${displayName} (${displayVersion})</div>
        </div>
      </div>
    </div>
  `
  } else {
    // 无子节点的物料节点不显示展开图标
    nodeContent = `
    <div class="node-content ${node.materialTempState == '2' ? 'bg-warning' : ''}">
      <div class="node-info">
        <div class="node-text">
          <div><strong>${levelIndicator}${displayDrawingNo}</strong> - ${node.materialCategory || ''}${node.materialAttr ? '(' + node.materialAttr + ')' : ''}</div>
          <div class="text-muted" style="font-size: 0.75rem;">${node.materialNo || ''} - ${displayName} (${displayVersion})</div>
        </div>
      </div>
    </div>
  `
  }

  const nodeElement = $(
    `<div class="${nodeClass}" data-id="${node.id}" data-type="${node.type}">${nodeContent}</div>`,
  )

  return nodeElement
}

// 更新面包屑导航
function updateBreadcrumb(items) {
  const breadcrumbContainer = $("#breadcrumbNav")
  breadcrumbContainer.empty()

  items.forEach((item, index) => {
    if (index === items.length - 1) {
      // 最后一项，当前页面
      breadcrumbContainer.append(`<span class="breadcrumb-current">${item}</span>`)
    } else {
      // 可点击的面包屑项
      breadcrumbContainer.append(`<a href="#" class="breadcrumb-link" data-level="${index}">${item}</a>`)
      breadcrumbContainer.append(`<i class="fas fa-chevron-right breadcrumb-separator"></i>`)
    }
  })
}

// 渲染工艺步骤
function renderProcessSteps(steps) {
  const container = $("#detail_processSteps")
  container.empty()

  steps.forEach((step, index) => {
    const stepHtml = `
      <div class="col-sm-6 col-md-4 mb-2">
        <div class="form-group">
          <label class="form-label">工序${index + 1}</label>
          <input type="text" class="form-control" value="${step}" readonly>
        </div>
      </div>
    `
    container.append(stepHtml)
  })

  if (steps.length === 0) {
    container.html('<div class="col-12"><span class="text-muted">暂无工艺信息</span></div>')
  }


}

// 模拟搜索功能
function performSearch() {
  const keyword = $("#searchInput").val().trim().toLowerCase()
  if (!keyword) {
    clearSearch()
    return
  }
  
  if (currentView === "customer-list") {
    // 在客户列表中搜索
    searchCustomers(keyword)
  } else if (currentView === "material-tree" && selectedCustomer) {
    // 在物料树中搜索
    searchMaterials(keyword)
  }
}

// 搜索客户
function searchCustomers(keyword) {
  const container = $("#bomTree")
  container.empty()

  const customers = extractCustomers()
  const filteredCustomers = customers.filter(customer => 
    customer.name.toLowerCase().includes(keyword) || 
    customer.code.toLowerCase().includes(keyword)
  )

  if (filteredCustomers.length === 0) {
    container.html(`<div class="alert alert-info">未找到匹配"${keyword}"的客户</div>`)
    $("#totalCount").text(0)
  } else {
    filteredCustomers.forEach((customer) => {
      const customerElement = $(`
        <div class="customer-item" data-id="${customer.id}">
          <div class="customer-content">
            <div class="customer-info">
              <i class="fas fa-building customer-icon"></i>
              <div class="customer-text">
                <div class="customer-name">${customer.name}</div>
                <div class="text-muted customer-code">${customer.code}</div>
              </div>
            </div>
            <div class="customer-meta">
              <i class="fas fa-chevron-right customer-arrow ml-2"></i>
            </div>
          </div>
        </div>
      `)
      container.append(customerElement)
    })
    

  }
}

// 搜索物料
function searchMaterials(keyword) {
  if (!selectedCustomer) return
  
  // 获取存储在selectedCustomer中的BOM树数据
  const bomData = selectedCustomer.children
  
  if (!bomData || bomData.length === 0) {
    const container = $("#bomTree")
    container.html(`<div class="alert alert-info">该客户暂无BOM数据</div>`)
    $("#totalCount").text(0)
    return
  }
  
  // 创建一个深拷贝，避免修改原始数据
  const bomDataCopy = JSON.parse(JSON.stringify(bomData))
  
  // 保存当前选中节点的ID，以便在搜索结果中恢复选中状态
  const selectedNodeId = selectedMaterial ? selectedMaterial.id : null;
  
  // 递归搜索物料
  function filterNodes(nodes) {
    if (!nodes) return []
    
    return nodes.filter(node => {
      // 检查当前节点是否匹配
      const matchesKeyword = 
        (node.materialName && node.materialName.toLowerCase().includes(keyword)) ||
        (node.materialNo && node.materialNo.toLowerCase().includes(keyword)) ||
        (node.drawingNumber && node.drawingNumber.toLowerCase().includes(keyword))
      
      // 如果有子节点，递归过滤
      if (node.children && node.children.length > 0) {
        node.children = filterNodes(node.children)
        // 如果子节点有匹配项，保留父节点并展开
        if (node.children.length > 0) {
          node.expanded = true
          return true
        }
      }
      
      // 如果当前节点匹配或者有匹配的子节点，则保留
      return matchesKeyword
    })
  }
  
  // 过滤物料树
  const filteredBomData = filterNodes(bomDataCopy)
  
  // 渲染过滤后的树
  const container = $("#bomTree")
  container.empty()
  
  if (filteredBomData && filteredBomData.length > 0) {
    // 直接渲染过滤后的BOM树
    renderNodes(filteredBomData, container, 0)
    

    
    // 如果之前有选中的节点，尝试在搜索结果中找到并重新选中
    if (selectedNodeId) {
      setTimeout(() => {
        const selectedNode = $(`[data-id="${selectedNodeId}"]`);
        if (selectedNode.length > 0) {
          // 移除所有选中状态
          $(".tree-node").removeClass("selected");
          // 重新选中节点
          selectedNode.addClass("selected");
          
          // 确保节点在可视区域内
          const container = $(".tree-container");
          const nodeOffset = selectedNode.offset().top;
          const containerOffset = container.offset().top;
          const nodePosition = nodeOffset - containerOffset + container.scrollTop();
          
          // 如果节点不在可视区域，滚动到节点位置
          if (nodePosition < 50 || nodePosition > container.height() - 50) {
            container.animate({
              scrollTop: nodePosition - container.height() / 2
            }, 300);
          }
        }
      }, 100);
    }
  } else {
    container.html(`<div class="alert alert-info">未找到匹配"${keyword}"的物料</div>`)
    
    // 添加返回按钮
    const backBtn = $(`
      <button class="btn btn-outline-secondary btn-sm mt-3">
        <i class="fas fa-arrow-left mr-1"></i>返回完整物料树
      </button>
    `)
    backBtn.on('click', function() {
      clearSearch()
    })
    container.append(backBtn)
  }
}



// 删除BOM节点功能
function deleteMaterial(nodeId) {
  if (!nodeId || !nodeId.startsWith('bom_')) {
    alert("无法删除：不是有效的BOM节点");
    return;
  }
  
  // 提取BOM ID
  const bomId = nodeId.replace('bom_', '');
  
  // 检查是否有子项
  const childrenNames = getBomChildrenNames(nodeId);
  
  let confirmMessage = "确定要删除该BOM节点吗？此操作不可恢复。";
  
  if (childrenNames.length > 0) {
    confirmMessage = "该BOM节点包含以下子项，删除将同时删除所有子项：\n\n" + 
                     childrenNames.join('\n') + 
                     "\n\n确定要删除吗？";
  }
  
  if (confirm(confirmMessage)) {
    // 向后端发送删除请求
    $.ajax({
      url: '/delete_bom_node/',
      type: 'POST',
      data: {
        bom_id: bomId,
        csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
      },
      success: function(response) {
        if (response.success) {
          alert("删除成功");
          // 重新加载树形数据
          if (selectedCustomer) {
            showCustomerMaterialTree(selectedCustomer.id);
          }
          // 显示空状态
          showEmptyState();
        } else {
          alert("删除失败：" + (response.message || "未知错误"));
        }
      },
      error: function(xhr, status, error) {
        console.error("删除请求失败:", error);
      }
    });
  }
}

// 显示空状态
function showEmptyState() {
  selectedMaterial = null;
  $("#materialFrame").hide();
  $("#emptyState").show().html(`
    <i class="fas fa-mouse-pointer"></i>
    <h5>请选择左侧树形列表中的物料</h5>
    <p class="text-muted">点击左侧BOM树形列表中的任意物料节点查看详细信息</p>
  `);

}

// 绑定事件
function bindEvents() {


  // 客户列表项点击事件
  $(document).on("click", ".customer-item", function () {
    const customerId = $(this).data("id")


    // 移除之前的选中状态
    $(".customer-item").removeClass("selected")

    // 选中当前客户并添加动画效果
    $(this).addClass("selected")
    
    // 添加闪烁动画效果
    $(this).css('transition', 'background-color 0.3s ease')
    $(this).css('animation', 'highlight-pulse 0.6s ease')
    
    // 确保客户项在可视区域内
    const container = $(".tree-container");
    const itemOffset = $(this).offset().top;
    const containerOffset = container.offset().top;
    const itemPosition = itemOffset - containerOffset + container.scrollTop();
    
    // 如果客户项不在可视区域，滚动到客户项位置
    if (itemPosition < 50 || itemPosition > container.height() - 50) {
      container.animate({
        scrollTop: itemPosition - container.height() / 2
      }, 300);
    }

    // 显示该客户的物料树
    showCustomerMaterialTree(customerId)
  })

  // 客户列表项悬停事件
  $(document).on("mouseenter", ".customer-item", function () {
    if (!$(this).hasClass("selected")) {
      $(this).addClass("highlighted")
    }
  })

  $(document).on("mouseleave", ".customer-item", function () {
    $(this).removeClass("highlighted")
  })

  // 面包屑导航点击事件
  $(document).on("click", ".breadcrumb-link", function (e) {
    e.preventDefault()
    const level = $(this).data("level")

    if (level === 0) {
      // 返回客户列表
      showCustomerList()
      showEmptyState()
    }
  })

  // 展开/折叠图标点击 - 只处理展开折叠，不选中节点
  $(document).on("click", ".expand-icon", function (e) {
    e.stopPropagation()
    const nodeId = $(this).data("id")

    toggleNode(nodeId)
  })

  // 节点点击事件 - 只选中节点，不处理展开折叠
  $(document).on("click", ".tree-node", function (e) {
    // 如果点击的是展开图标，不处理节点选中
    if ($(e.target).hasClass("expand-icon")) {
      return
    }

    const nodeId = $(this).data("id")
    const nodeType = $(this).data("type")


    // 移除之前的选中状态
    $(".tree-node").removeClass("selected")

    // 选中当前节点并添加动画效果
    $(this).addClass("selected")
    
    // 添加闪烁动画效果
    $(this).css('transition', 'background-color 0.3s ease')
    $(this).css('animation', 'highlight-pulse 0.6s ease')
    
    // 确保节点在可视区域内
    const container = $(".tree-container");
    const nodeOffset = $(this).offset().top;
    const containerOffset = container.offset().top;
    const nodePosition = nodeOffset - containerOffset + container.scrollTop();
    
    // 如果节点不在可视区域，滚动到节点位置
    if (nodePosition < 50 || nodePosition > container.height() - 50) {
      container.animate({
        scrollTop: nodePosition - container.height() / 2
      }, 300);
    }

    // 查找节点数据并显示详情
    const nodeData = findNodeById(nodeId)
  
    if (nodeData) {

      selectMaterial(nodeData)
    }
  })

  // 节点悬停事件 - 添加高亮效果
  $(document).on("mouseenter", ".tree-node", function () {
    if (!$(this).hasClass("selected")) {
      $(this).addClass("highlighted")
    }
  })

  $(document).on("mouseleave", ".tree-node", function () {
    $(this).removeClass("highlighted")
  })

  // 其余事件绑定
  $("#searchBtn").on("click", () => {
    performSearch()
  })

  $("#searchInput").on("keypress", (e) => {
    if (e.which === 13) {
      performSearch()
    }
  })

  $("#searchInput").on("input", function() {
    // 如果搜索框为空，则清除搜索结果
    if ($(this).val().trim() === "") {
      clearSearch()
    }
  })

  $("#clearSearch").on("click", () => {
    clearSearch()
  })

  $("#expandAll").on("click", () => {
    expandAllNodes(true)
  })

  $("#collapseAll").on("click", () => {
    expandAllNodes(false)
  })

  $("#addMaterial").on("click", () => {
    // 如果选中了某个节点，传递父节点ID
    if (selectedMaterial && selectedMaterial.id) {
      // 获取父节点的BOM ID
      const bomId = selectedMaterial.id.replace('bom_', '');
      // 获取客户代码
      const customerCode = selectedMaterial.customerCode || '';

      // 检查当前页面是否为导入预览页面
      const isImportPreview = window.location.pathname.includes('bom_import_preview');
      // 设置iframe的src
      let iframeUrl = `/materials/add/?source_type=bom_list&bom_pid=${bomId}&customer_code=${customerCode}&iframe=1`;
      
      // 如果是导入预览页面，添加临时状态参数
      if (isImportPreview) {
        iframeUrl += '&temp_state=1&bom_state=1';  // 设置为临时新增状态
      }
      
      $("#addMaterialIframe").attr("src", iframeUrl);
      
      // 显示模态框
      $("#addMaterialModal").modal('show');
    } else {
      // 没有选中节点，直接提示
      alert("请先选中一个物料节点");
    }
  })
  
  // 新增父件按钮事件
  $("#addMaterialParent").on("click", () => {

    
    // 检查是否选中了客户
    if (selectedCustomer) {
      // 获取客户代码
      const customerCode = selectedCustomer.Customer_Code || selectedCustomer.code || '';
      if (!customerCode) {
        alert("无法获取客户代码，请重新选择客户");
        return;
      }
      
      // 检查当前页面是否为导入预览页面
      const isImportPreview = window.location.pathname.includes('bom_import_preview');
     
      // 设置iframe的src
      let iframeUrl = `/materials/add/?source_type=bom_list&bom_pid=0&customer_code=${customerCode}&iframe=1`;
      
      // 如果是导入预览页面，添加临时状态参数
      if (isImportPreview) {
        iframeUrl += '&temp_state=1&bom_state=1';  // 设置为临时新增状态
      }

      
      $("#addMaterialParentIframe").attr("src", iframeUrl);
      
      // 显示模态框
      $("#addMaterialParentModal").modal('show');
    } else {
      // 没有选中客户，提示用户
      alert("请先选择一个客户");
    }
  })

  $("#exportBom").on("click", () => {
    exportBomData()
  })

  $("#editMaterial").on("click", () => {
    if (selectedMaterial) {
      window.location.href = `/bom_sys/material/edit/${selectedMaterial.id}/`
    }
  })

  $("#deleteMaterial").on("click", () => {
    if (selectedMaterial) {
      deleteMaterial(selectedMaterial.id)
    }
  })

  // 面板收缩/展开按钮事件
  $("#togglePanel").on("click", () => {
    toggleLeftPanel()
  })

  // 浮动按钮事件
  $("#floatingToggle").on("click", () => {
    toggleLeftPanel()
  })

  // 新增刷新按钮事件
  $(document).on("click", "#refreshMaterial", function() {
    if (selectedMaterial && $("#materialIframe").attr("src")) {
      // 获取当前模式
      const currentMode = $("#editMaterial").data("mode");
      // 刷新iframe，根据当前模式决定使用预览模式还是编辑模式
      const materialId = selectedMaterial.bom_Material || selectedMaterial.material_id || null;
      if (materialId) {
        // 检查当前页面URL，判断是否为导入预览页面
        const isImportPreview = window.location.pathname.includes('bom_import_preview');
        
        let refreshUrl;
        if (currentMode === "preview") {
          // 如果当前是预览模式，刷新时仍保持预览模式
          refreshUrl = `/materials/add/?edit_id=${materialId}&iframe=1&preview=1&source_type=bom_list&t=${new Date().getTime()}`;
        } else {
          // 如果当前是编辑模式，刷新时仍保持编辑模式
          refreshUrl = `/materials/add/?edit_id=${materialId}&iframe=1&source_type=bom_list&t=${new Date().getTime()}`;
        }
        
        // 如果是导入预览页面，添加临时状态参数
        if (isImportPreview) {
          // 获取原始的临时状态和BOM状态值
          const tempState = selectedMaterial.materialTempState || 1;
          const bomState = selectedMaterial.bomState || 1;
          
          refreshUrl += `&temp_state=${tempState}&bom_state=${bomState}`;
          console.log(`导入预览模式刷新，添加临时状态参数: temp_state=${tempState}, bom_state=${bomState}`);
        }
        $("#materialIframe").attr("src", refreshUrl);
        console.log("刷新物料详情，URL:", refreshUrl);
      }
    }
  });

  // 删除BOM节点按钮事件
  $(document).on("click", "#deleteMaterial", function() {
    if (selectedMaterial) {
      // 检查是否是BOM节点
      if (!selectedMaterial.id || !selectedMaterial.id.startsWith('bom_')) {
        alert("只能删除BOM节点，当前选中的不是BOM节点");
        return;
      }
      
      // 调用删除函数
      deleteMaterial(selectedMaterial.id);
    }
  });

  // 添加保存按钮事件
  $(document).on("click", "#saveMaterial", function() {
    if (selectedMaterial && $("#materialIframe").attr("src")) {
      try {
        // 获取iframe内容窗口
        const iframe = document.getElementById("materialIframe");
        const iframeWindow = iframe.contentWindow;
        const iframeDocument = iframeWindow.document;
        
        // 获取物料ID
        const materialId = selectedMaterial.materialId;
        
        // 查找iframe中的表单
        const form = iframeDocument.querySelector('form');
        if (form && materialId) {
          // 检查是否已经存在material_id隐藏字段
          let materialIdField = form.querySelector('input[name="update_material_id"]');
          if (!materialIdField) {
            // 创建隐藏字段
            materialIdField = document.createElement('input');
            materialIdField.type = 'hidden';
            materialIdField.name = 'update_material_id';
            form.appendChild(materialIdField);
          }
          // 设置物料ID值
          materialIdField.value = materialId;
        }
        
        // 查找iframe中的表单提交按钮并触发点击
        const submitButton = iframeDocument.querySelector('button[type="submit"]');
        if (submitButton) {
          // 模拟点击提交按钮
          submitButton.click();

        } else {
          // 如果找不到提交按钮，则直接提交表单
          if (form) {
            form.submit();

          } else {

            alert("保存失败：无法找到表单");
          }
        }
      } catch (e) {
        // 如果出现跨域问题或其他错误

        alert("保存失败，可能是由于浏览器安全限制");
      }
    }
  });

  // 新增子件模态框中的保存按钮事件
  $(document).on("click", "#saveAddMaterial", function() {
    try {
      // 获取iframe内容窗口
      const iframe = document.getElementById("addMaterialIframe");
      const iframeWindow = iframe.contentWindow;
      const iframeDocument = iframeWindow.document;
      
      // 查找iframe中的表单
      const form = iframeDocument.querySelector('form');
      
      // 查找iframe中的表单提交按钮并触发点击
      const submitButton = iframeDocument.querySelector('button[type="submit"]');
      if (submitButton) {
        // 模拟点击提交按钮
        submitButton.click();

        
        // 监听iframe中的表单提交事件
        $(iframeDocument).find('form').on('submit', function() {
          // 表单提交成功后，关闭模态框并刷新树形列表
          setTimeout(function() {
            $("#addMaterialModal").modal('hide');
            if (selectedCustomer) {
              showCustomerMaterialTree(selectedCustomer.id);
            }
          }, 1000);
        });
      } else {
        // 如果找不到提交按钮，则直接提交表单
        if (form) {
          form.submit();

          
          // 设置延迟，等待后端处理完成后关闭模态框并刷新树形列表
          setTimeout(function() {
            $("#addMaterialModal").modal('hide');
            if (selectedCustomer) {
              showCustomerMaterialTree(selectedCustomer.id);
            }
          }, 1000);
        } else {
          console.error("无法找到iframe中的表单");
          alert("保存失败：无法找到表单");
        }
      }
    } catch (e) {
      // 如果出现跨域问题或其他错误
      console.error("保存失败:", e);
      alert("保存失败，可能是由于浏览器安全限制");
    }
  });
  
  // 新增父件模态框中的保存按钮事件
  $(document).on("click", "#saveAddMaterialParent", function() {
    try {
      // 获取iframe内容窗口
      const iframe = document.getElementById("addMaterialParentIframe");
      const iframeWindow = iframe.contentWindow;
      const iframeDocument = iframeWindow.document;
      
      // 查找iframe中的表单
      const form = iframeDocument.querySelector('form');
      
      // 查找iframe中的表单提交按钮并触发点击
      const submitButton = iframeDocument.querySelector('button[type="submit"]');
      if (submitButton) {
        // 模拟点击提交按钮
        submitButton.click();

        
        // 监听iframe中的表单提交事件
        $(iframeDocument).find('form').on('submit', function() {
          // 表单提交成功后，关闭模态框并刷新树形列表
          setTimeout(function() {
            $("#addMaterialParentModal").modal('hide');
            
            // 设置标记，指示刚刚添加了父件
            if (selectedCustomer && selectedCustomer.children) {
              // 预期的新数量应该是当前数量+1
              expectedNewCount = selectedCustomer.children.length + 1;

            }
            justAddedParentMaterial = true;
            
            if (selectedCustomer) {
              showCustomerMaterialTree(selectedCustomer.id);
            }
          }, 1000);
        });
      } else {
        // 如果找不到提交按钮，则直接提交表单
        if (form) {
          form.submit();

          
          // 设置延迟，等待后端处理完成后关闭模态框并刷新树形列表
          setTimeout(function() {
            $("#addMaterialParentModal").modal('hide');
            
            // 设置标记，指示刚刚添加了父件
            if (selectedCustomer && selectedCustomer.children) {
              // 预期的新数量应该是当前数量+1
              expectedNewCount = selectedCustomer.children.length + 1;

            }
            justAddedParentMaterial = true;
            
            if (selectedCustomer) {
              showCustomerMaterialTree(selectedCustomer.id);
            }
          }, 1000);
        } else {
          console.error("无法找到iframe中的表单");
          alert("保存失败：无法找到表单");
        }
      }
    } catch (e) {
      // 如果出现跨域问题或其他错误
      console.error("保存失败:", e);
      alert("保存失败，可能是由于浏览器安全限制");
    }
  });

  // 添加新增版本按钮事件
  $(document).on("click", "#newVersionMaterial", function() {
    if (selectedMaterial && $("#materialIframe").attr("src")) {
      try {
        // 获取iframe内容窗口
        const iframe = document.getElementById("materialIframe");
        const iframeWindow = iframe.contentWindow;
        const iframeDocument = iframeWindow.document;
        
        // 首先检查是否存在新增版本按钮
        let newVersionButton = iframeDocument.querySelector('button[id="addVersion"]');
        
        // 如果没有id为addVersion的按钮，尝试通过按钮文本查找
        if (!newVersionButton) {
          const buttons = iframeDocument.querySelectorAll('button');
          for (let i = 0; i < buttons.length; i++) {
            if (buttons[i].textContent.includes('新增版本')) {
              newVersionButton = buttons[i];
              break;
            }
          }
        }
        
        if (newVersionButton) {
          // 模拟点击新增版本按钮
          newVersionButton.click();

        } else {
          // 如果找不到新增版本按钮，尝试查找有关版本的input字段
          const versionInput = iframeDocument.querySelector('input[id="version"]');
          if (versionInput) {
            // 获取当前版本
            const currentVersion = versionInput.value;
            // 弹出提示，让用户输入新版本号
            const newVersion = prompt("请输入新版本号：", getNextVersion(currentVersion));
            if (newVersion) {
              // 修改版本号
              versionInput.value = newVersion;
              alert("已更改为新版本: " + newVersion + "\n请点击保存按钮保存更改");
            }
          } else {

            alert("无法执行新增版本操作");
          }
        }
      } catch (e) {
        // 如果出现跨域问题或其他错误

        alert("新增版本失败，可能是由于浏览器安全限制");
      }
    }
  });
  
  // 辅助函数：获取下一个版本号
  function getNextVersion(currentVersion) {
    // 判断版本号格式
    if (/^[A-Z]$/.test(currentVersion)) {
      // 如果是单个大写字母，则递增
      const code = currentVersion.charCodeAt(0);
      return String.fromCharCode(code + 1);
    } else if (/^\d+$/.test(currentVersion)) {
      // 如果是数字，则加1
      const num = parseInt(currentVersion);
      return (num + 1).toString().padStart(currentVersion.length, '0');
    } else {
      // 其他情况，返回原值
      return currentVersion;
    }
  }

  // 绑定物料按钮点击事件
  $(document).on("click", "#bindMaterial", function() {
    if (!selectedMaterial || !selectedMaterial.id || !selectedMaterial.id.startsWith('bom_')) {
      alert("请先选择一个BOM节点");
      return;
    }
    
    // 获取当前BOM节点的客户代码
    const customerCode = selectedMaterial.customerCode || '';
    
    // 打开模态框
    $("#bindMaterialModal").modal('show');
    
    // 清空之前的搜索结果
    $("#materialTableBody").empty();
    $("#materialSearchInput").val('');
    
    // 隐藏空状态、加载状态和分页
    $("#materialEmptyState").hide();
    $("#materialPagination").hide();
    
    // 显示加载状态
    $("#materialLoadingState").show();
    
    // 加载该客户的物料列表（默认第1页）
    loadMaterialsByCustomer(customerCode, '', 1);
  });
  
  // 物料搜索按钮点击事件
  $(document).on("click", "#materialSearchBtn", function() {
    const keyword = $("#materialSearchInput").val().trim();
    const customerCode = selectedMaterial ? (selectedMaterial.customerCode || '') : '';
    
    // 显示加载状态
    $("#materialTableBody").empty();
    $("#materialLoadingState").show();
    $("#materialEmptyState").hide();
    $("#materialPagination").hide();
    
    // 加载符合条件的物料（重置到第1页）
    loadMaterialsByCustomer(customerCode, keyword, 1);
  });
  
  // 物料搜索输入框回车事件
  $(document).on("keypress", "#materialSearchInput", function(e) {
    if (e.which === 13) {
      $("#materialSearchBtn").click();
    }
  });
  
  // 分页按钮点击事件
  $(document).on("click", ".pagination-page", function() {
    const page = $(this).data('page');
    const keyword = $("#materialSearchInput").val().trim();
    const customerCode = selectedMaterial ? (selectedMaterial.customerCode || '') : '';
    
    // 加载指定页的数据
    loadMaterialsByCustomer(customerCode, keyword, page);
  });
  
  // 首页按钮点击事件
  $(document).on("click", "#pagination-first", function() {
    const keyword = $("#materialSearchInput").val().trim();
    const customerCode = selectedMaterial ? (selectedMaterial.customerCode || '') : '';
    
    // 加载第一页数据
    loadMaterialsByCustomer(customerCode, keyword, 1);
  });
  
  // 上一页按钮点击事件
  $(document).on("click", "#pagination-prev", function() {
    const currentPage = parseInt($(".pagination .active").text());
    if (currentPage > 1) {
      const keyword = $("#materialSearchInput").val().trim();
      const customerCode = selectedMaterial ? (selectedMaterial.customerCode || '') : '';
      
      // 加载上一页数据
      loadMaterialsByCustomer(customerCode, keyword, currentPage - 1);
    }
  });
  
  // 下一页按钮点击事件
  $(document).on("click", "#pagination-next", function() {
    const currentPage = parseInt($(".pagination .active").text());
    const totalPages = parseInt($("#pagination-next").data('total-pages') || 1);
    
    if (currentPage < totalPages) {
      const keyword = $("#materialSearchInput").val().trim();
      const customerCode = selectedMaterial ? (selectedMaterial.customerCode || '') : '';
      
      // 加载下一页数据
      loadMaterialsByCustomer(customerCode, keyword, currentPage + 1);
    }
  });
  
  // 末页按钮点击事件
  $(document).on("click", "#pagination-last", function() {
    const totalPages = parseInt($("#pagination-last").data('total-pages') || 1);
    const keyword = $("#materialSearchInput").val().trim();
    const customerCode = selectedMaterial ? (selectedMaterial.customerCode || '') : '';
    
    // 加载末页数据
    loadMaterialsByCustomer(customerCode, keyword, totalPages);
  });
  
  // 加载指定客户的物料列表
  function loadMaterialsByCustomer(customerCode, keyword = '', page = 1, pageSize = 10) {
    
    $.ajax({
      url: '/search_materials_by_customer/',
      type: 'GET',
      data: {
        customer_code: customerCode,
        keyword: keyword,
        page: page,
        page_size: pageSize
      },
      dataType: 'json',
      success: function(response) {
        // 隐藏加载状态
        $("#materialLoadingState").hide();
        
        if (response.success) {
          const materials = response.materials;
          
          if (materials && materials.length > 0) {
            // 渲染物料列表
            renderMaterialTable(materials);
            
            // 更新分页信息
            if (response.pagination) {
              updatePagination(response.pagination);
            }
          } else {
            // 显示空状态
            $("#materialEmptyState").show();
            $("#materialPagination").hide();
          }
        } else {
          // 显示错误信息
          $("#materialTableBody").html(`<tr><td colspan="5" class="text-center text-danger">${response.message || '加载物料数据失败'}</td></tr>`);
          $("#materialPagination").hide();
        }
      },
      error: function(xhr, status, error) {
        // 隐藏加载状态
        $("#materialLoadingState").hide();
        $("#materialPagination").hide();
        
        // 显示错误信息
        $("#materialTableBody").html(`<tr><td colspan="5" class="text-center text-danger">请求失败: ${error}</td></tr>`);
      }
    });
  }
  
  // 更新分页控件
  function updatePagination(paginationData) {
    const { page, page_size, total_count, total_pages } = paginationData;
    const start = (page - 1) * page_size + 1;
    const end = Math.min(page * page_size, total_count);
    
    // 更新分页信息
    $("#pagination-start").text(start);
    $("#pagination-end").text(end);
    $("#pagination-total").text(total_count);
    
    // 存储总页数
    $("#pagination-next").data('total-pages', total_pages);
    $("#pagination-last").data('total-pages', total_pages);
    
    // 更新分页按钮状态
    const paginationEl = $(".pagination");
    
    // 清除现有页码按钮
    paginationEl.find("li:not(#pagination-first):not(#pagination-prev):not(#pagination-next):not(#pagination-last)").remove();
    
    // 禁用/启用首页和上一页
    if (page <= 1) {
      $("#pagination-first").addClass("disabled");
      $("#pagination-prev").addClass("disabled");
    } else {
      $("#pagination-first").removeClass("disabled");
      $("#pagination-prev").removeClass("disabled");
    }
    
    // 禁用/启用末页和下一页
    if (page >= total_pages) {
      $("#pagination-next").addClass("disabled");
      $("#pagination-last").addClass("disabled");
    } else {
      $("#pagination-next").removeClass("disabled");
      $("#pagination-last").removeClass("disabled");
    }
    
    // 生成页码按钮
    let startPage = Math.max(1, page - 2);
    let endPage = Math.min(total_pages, startPage + 4);
    
    // 调整起始页，确保总是显示5个页码或更少
    if (endPage - startPage < 4 && startPage > 1) {
      startPage = Math.max(1, endPage - 4);
    }
    
    // 创建页码按钮
    for (let i = startPage; i <= endPage; i++) {
      const pageItem = $(`<li class="page-item pagination-page ${i === page ? 'active' : ''}" data-page="${i}"><a class="page-link" href="javascript:void(0);">${i}</a></li>`);
      
      // 插入到下一页按钮前
      pageItem.insertBefore("#pagination-next");
    }
    
    // 显示分页组件
    $("#materialPagination").show();
  }
  
  // 渲染物料表格
  function renderMaterialTable(materials) {
    const tableBody = $("#materialTableBody");
    tableBody.empty();
    
    materials.forEach(material => {
      const row = $(`
        <tr>
          <td>${material.material_no || ''}</td>
          <td>${material.material_drawingno || ''}</td>
          <td>${material.material_name || ''}</td>
          <td>${material.material_version || ''}</td>
          <td>
            <button class="btn btn-sm btn-primary bind-material-btn" data-material-id="${material.material_id}">
              <i class="fas fa-link mr-1"></i>绑定
            </button>
          </td>
        </tr>
      `);
      
      tableBody.append(row);
    });
  }
  
  // 物料绑定按钮点击事件
  $(document).on("click", ".bind-material-btn", function() {
    const materialId = $(this).data("material-id");
    
    if (!selectedMaterial || !selectedMaterial.id) {
      alert("未选中BOM节点，无法绑定物料");
      return;
    }
    
    // 获取BOM ID
    const bomId = selectedMaterial.id.replace('bom_', '');
    
    // 确认绑定
    if (confirm("确定要将选中的物料绑定到当前BOM节点吗？")) {
      // 显示加载状态
      $(this).html('<i class="fas fa-spinner fa-spin"></i> 绑定中...');
      $(this).prop('disabled', true);
      
      // 发送绑定请求
      $.ajax({
        url: '/bind_material_to_bom/',
        type: 'POST',
        data: {
          bom_id: bomId,
          material_id: materialId,
          csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
        },
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            // 关闭模态框
            $("#bindMaterialModal").modal('hide');
            
            // 加载新的物料详情
            const iframeUrl = `/materials/add/?edit_id=${materialId}&iframe=1&preview=1&source_type=bom_list`;
            $("#materialIframe").attr("src", iframeUrl);
            
            // 刷新树形列表
            if (selectedCustomer) {
              showCustomerMaterialTree(selectedCustomer.id);
            }
            
            // 显示成功消息
            alert("物料绑定成功");
          } else {
            // 显示错误信息
            alert("物料绑定失败: " + (response.message || "未知错误"));
          }
        },
        error: function(xhr, status, error) {
          // 显示错误信息
          alert("请求失败: " + error);
        },
        complete: function() {
          // 恢复按钮状态
          $(".bind-material-btn").html('<i class="fas fa-link mr-1"></i>绑定');
          $(".bind-material-btn").prop('disabled', false);
        }
      });
    }
  });

  // iframe加载完成事件
  $("#addMaterialIframe").on("load", function() {
    try {

      const iframeWindow = this.contentWindow;
      const iframeDocument = iframeWindow.document;
      
      // 监听iframe内部的表单提交事件
      $(iframeDocument).find('form').on('submit', function() {

        
        // 表单提交成功后，设置延迟关闭模态框并刷新树形列表
        setTimeout(function() {
          $("#addMaterialModal").modal('hide');
          if (selectedCustomer) {
            showCustomerMaterialTree(selectedCustomer.id);
          }
        }, 1000);
      });
    } catch (e) {
      console.warn("无法访问iframe内容，可能是跨域限制:", e);
    }
  });
  
  $("#addMaterialParentIframe").on("load", function() {
    try {

      const iframeWindow = this.contentWindow;
      const iframeDocument = iframeWindow.document;
      
      // 监听iframe内部的表单提交事件
      $(iframeDocument).find('form').on('submit', function() {

        
        // 表单提交成功后，设置延迟关闭模态框并刷新树形列表
        setTimeout(function() {
          $("#addMaterialParentModal").modal('hide');
          
          // 设置标记，指示刚刚添加了父件
          if (selectedCustomer && selectedCustomer.children) {
            // 预期的新数量应该是当前数量+1
            expectedNewCount = selectedCustomer.children.length + 1;

          }
          justAddedParentMaterial = true;
          
          if (selectedCustomer) {
            showCustomerMaterialTree(selectedCustomer.id);
          }
        }, 1000);
      });
    } catch (e) {
      console.warn("无法访问iframe内容，可能是跨域限制:", e);
    }
  });


}

// 切换左侧面板显示状态
function toggleLeftPanel() {
  const leftPanel = $(".left-panel")
  const rightPanel = $(".right-panel")
  const toggleIcon = $("#toggleIcon")
  const floatingBtn = $("#floatingToggle")

  if (leftPanel.hasClass("collapsed")) {
    // 展开面板
    leftPanel.removeClass("collapsed")
    rightPanel.removeClass("expanded")
    leftPanel.removeClass("col-md-1").addClass("col-md-4")
    rightPanel.removeClass("col-md-11").addClass("col-md-8")
    toggleIcon.removeClass("fa-chevron-right").addClass("fa-chevron-left")
    floatingBtn.removeClass("show")
    floatingBtn.hide()

  } else {
    // 收缩面板
    leftPanel.addClass("collapsed")
    rightPanel.addClass("expanded")
    leftPanel.removeClass("col-md-4").addClass("col-md-1")
    rightPanel.removeClass("col-md-8").addClass("col-md-11")
    toggleIcon.removeClass("fa-chevron-left").addClass("fa-chevron-right")
    floatingBtn.addClass("show")
    floatingBtn.show()

  }
}

// 根据ID查找物料
function findMaterialById(id) {
  const material = allMaterials.find((material) => material.id === Number.parseInt(id))

  return material
}

// 根据ID查找任意节点
function findNodeById(id) {
  let foundNode = null

  function traverse(nodes) {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i]
      if (node.id == id) {
        // 使用==而不是===，因为有些ID可能是数字，有些是字符串
        foundNode = node
        return true
      }
      if (node.children && traverse(node.children)) {
        return true
      }
    }
    return false
  }

  traverse(mockTreeData)

  return foundNode
}

// 切换节点展开/折叠状态
function toggleNode(nodeId) {
  function traverse(nodes) {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i]
      if (node.id === nodeId) {
        node.expanded = !node.expanded
        return true
      }
      if (node.children && traverse(node.children)) {
        return true
      }
    }
    return false
  }
  
  // 遍历整个树形结构，包括客户数据
  traverse(mockTreeData)
  
  if (currentView === "material-tree" && selectedCustomer) {
    // 在material-tree模式下，直接渲染BOM数据，而不是客户节点
    renderTree(selectedCustomer.children)
  } else {
    showCustomerList()
  }
}

// 展开/折叠所有节点
function expandAllNodes(expand) {
  function traverse(nodes) {
    if (!nodes) return;

    nodes.forEach((node) => {
      // 对所有有子节点的节点设置展开状态，不再限制只有非物料节点
      if (node.children && node.children.length > 0) {
        node.expanded = expand
      }
      // 递归处理子节点
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  
  // 遍历整个树形结构，包括客户数据
  traverse(mockTreeData)
  
  if (currentView === "material-tree" && selectedCustomer) {
    // 在material-tree模式下，直接渲染BOM数据，而不是客户节点
    renderTree(selectedCustomer.children)
  } else {
    showCustomerList()
  }
}

// 清除搜索
function clearSearch() {
  $("#searchInput").val("")

  if (currentView === "customer-list") {
    // 恢复完整的客户列表
    showCustomerList()
  } else {
    // 恢复完整的物料树
    if (selectedCustomer && selectedCustomer.children) {
      // 直接渲染BOM树数据
      renderTree(selectedCustomer.children)
    }
  }


}

// 选择物料
function selectMaterial(material) {
  selectedMaterial = material
  showMaterialDetail(material)

}

// 显示物料详情
function showMaterialDetail(material) {
  // 获取materialId，这个ID用于加载物料详情
  const materialId = material.materialId || null;

  if (!materialId) {
    console.error("无法获取物料ID:", material);
    // 显示错误信息
    $("#emptyState").show().html(`
      <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        <h5>无法加载物料信息</h5>
        <p class="text-muted">无法获取有效的物料ID，请联系系统管理员</p>
      </div>
    `);
    $("#materialFrame").hide();
    return;
  }
  
  // 隐藏空状态，显示iframe容器
  $("#emptyState").hide();
  $("#materialFrame").show();
  
  // 重置编辑按钮状态为"编辑"模式
  $("#editMaterial").html('<i class="fas fa-edit mr-1"></i>编辑');
  $("#editMaterial").data("mode", "preview");
  
  // 初始隐藏保存按钮和新增版本按钮，因为开始是预览模式
  $("#saveMaterial").hide();
  $("#newVersionMaterial").hide();
  
  // 构建iframe URL，使用预览模式
  // 检查当前页面URL，判断是否为导入预览页面
  const isImportPreview = window.location.pathname.includes('bom_import_preview');
  
  // 在导入预览页面中需要添加临时状态参数
  let iframeUrl = `/materials/add/?edit_id=${materialId}&iframe=1&preview=1&source_type=bom_list`;
  
  // 如果是导入预览页面，添加临时状态参数
  if (isImportPreview) {
    // 获取原始的临时状态和BOM状态值
    const tempState = material.materialTempState || 1;
    const bomState = material.bomState || 1;
    
    iframeUrl += `&temp_state=${tempState}&bom_state=${bomState}`;

  }
  
  // 设置iframe的src
  $("#materialIframe").attr("src", iframeUrl);
  

  
  // 添加编辑按钮事件监听
  $("#editMaterial").off("click").on("click", function() {
    const currentMode = $(this).data("mode");
    
    if (currentMode === "preview") {
      // 当前是预览模式，切换到编辑模式
      // 检查当前页面URL，判断是否为导入预览页面
      const isImportPreview = window.location.pathname.includes('bom_import_preview');
      
      // 在导入预览页面中需要添加临时状态参数
      let editUrl = `/materials/add/?edit_id=${materialId}&iframe=1&source_type=bom_list`;
      
      // 如果是导入预览页面，添加临时状态参数
      if (isImportPreview) {
        // 获取原始的临时状态和BOM状态值
        const tempState = material.materialTempState || 1;
        const bomState = material.bomState || 1;
        
        editUrl += `&temp_state=${tempState}&bom_state=${bomState}`;

      }
      
      $("#materialIframe").attr("src", editUrl);
      $(this).html('<i class="fas fa-eye mr-1"></i>预览');
      $(this).data("mode", "edit");
      // 显示保存按钮和新增版本按钮
      $("#saveMaterial").show();
      $("#newVersionMaterial").show();
      console.log("切换到编辑模式，URL:", editUrl);
    } else {
      // 当前是编辑模式，切换到预览模式
      // 检查当前页面URL，判断是否为导入预览页面
      const isImportPreview = window.location.pathname.includes('bom_import_preview');
      
      // 在导入预览页面中需要添加临时状态参数
      let previewUrl = `/materials/add/?edit_id=${materialId}&iframe=1&preview=1&source_type=bom_list`;
      
      // 如果是导入预览页面，添加临时状态参数
      if (isImportPreview) {
        // 获取原始的临时状态和BOM状态值
        const tempState = material.materialTempState || 1;
        const bomState = material.bomState || 1;
        
        previewUrl += `&temp_state=${tempState}&bom_state=${bomState}`;
        console.log(`导入预览模式查看，添加临时状态参数: temp_state=${tempState}, bom_state=${bomState}`);
      }
      
      $("#materialIframe").attr("src", previewUrl);
      $(this).html('<i class="fas fa-edit mr-1"></i>编辑');
      $(this).data("mode", "preview");
      // 隐藏保存按钮和新增版本按钮
      $("#saveMaterial").hide();
      $("#newVersionMaterial").hide();
      console.log("切换到预览模式，URL:", previewUrl);
    }
  });
  
  // 添加iframe加载完成事件处理
  $("#materialIframe").on("load", function() {
    try {
      // 获取iframe内容窗口
      const iframeWindow = this.contentWindow;
      
      // 监听iframe内部的表单提交事件
      $(iframeWindow.document).find('form').on('submit', function() {
        // 表单提交成功后，刷新父页面的树形列表

        
        // 设置一个延迟，等待后端处理完成
        setTimeout(function() {
          if (selectedCustomer) {
            showCustomerMaterialTree(selectedCustomer.id);
          }
          // 表单提交后切换回预览模式
          $("#editMaterial").html('<i class="fas fa-edit mr-1"></i>编辑');
          $("#editMaterial").data("mode", "preview");
          // 隐藏保存按钮和新增版本按钮
          $("#saveMaterial").hide();
          $("#newVersionMaterial").hide();
        }, 1000);
      });
      
      // 根据当前模式控制按钮的可见性
      const currentMode = $("#editMaterial").data("mode");
      if (currentMode === "preview") {
        $("#saveMaterial").hide();
        $("#newVersionMaterial").hide();
      } else {
        $("#saveMaterial").show();
        $("#newVersionMaterial").show();
      }
      
    } catch (e) {
      // 如果出现跨域问题，静默失败
    }
  });
}

// 获取BOM节点的所有子项名称列表
function getBomChildrenNames(nodeId) {
  const childrenNames = [];
  
  function collectChildrenNames(nodes) {
    if (!nodes) return;
    
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.id === nodeId && node.children && node.children.length > 0) {
        // 找到目标节点，收集其子项名称
        node.children.forEach(child => {
          const childName = child.materialName || child.name || "未命名物料";
          childrenNames.push(childName);
          
          // 递归收集子节点的子项
          if (child.children && child.children.length > 0) {
            child.children.forEach(grandChild => {
              const grandChildName = grandChild.materialName || grandChild.name || "未命名物料";
              childrenNames.push("  - " + grandChildName);
            });
          }
        });
        return true;
      }
      
      // 递归搜索子节点
      if (node.children && node.children.length > 0) {
        if (collectChildrenNames(node.children)) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  // 从整个树形结构中搜索
  collectChildrenNames(mockTreeData);
  
  return childrenNames;
}

// 初始化时显示空状态
$(document).ready(function() {
  showEmptyState();
});

// 将init函数暴露为全局函数，以便从外部调用
window.init = init; 