{% extends 'base.html' %}
{% load static %}

{% block title %}物料属性 - 物料清单管理系统{% endblock %}

{% block extra_css %}
<style>
    .editable-cell {
        padding: 0 !important;
    }
    .editable-input {
        width: 100%;
        border: none;
        background: transparent;
        padding: 8px;
    }
    .editable-input:focus {
        background-color: #f8f9fa;
        outline: 2px solid #007bff;
    }
    .editable-select {
        width: 100%;
        border: none;
        background: transparent;
        padding: 8px;
    }
    .editable-select:focus {
        background-color: #f8f9fa;
        outline: 2px solid #007bff;
    }
    .hidden-row {
        display: none;
    }
    .invalid-input {
        border: 1px solid #dc3545 !important;
        background-color: #f8d7da !important;
    }
    .error-tooltip {
        position: absolute;
        background-color: #dc3545;
        color: white;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1000;
        display: none;
    }
    .custom-rule-input {
        display: none;
        width: 100%;
        border: none;
        background: transparent;
        padding: 8px;
    }
</style>
{% endblock %}

{% block body_class %}{% if is_iframe %}{% else %}hold-transition sidebar-mini layout-fixed{% endif %}{% endblock %}

{% block content %}
{% if is_iframe %}
    <!-- 主体内容 -->
    <section class="content">
        <div class="container-fluid">
            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">物料属性列表</h3>
                    <div class="card-tools">
                        <button id="btn-add-row" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 添加属性
                        </button>
                        <button id="btn-save-all" class="btn btn-success btn-sm ml-2">
                            <i class="fas fa-save"></i> 保存全部
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="properties-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="5%">ID</th>
                                    <th width="25%">属性名称</th>
                                    <th width="15%">前缀编码</th>
                                    <th width="25%">中缀规则</th>
                                    <th width="10%">排序</th>
                                    <th width="20%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for property_type in properties_types %}
                                <tr data-id="{{ property_type.propertiestype_id }}">
                                    <td>{{ property_type.propertiestype_id }}</td>
                                    <td class="editable-cell">
                                        <input type="text" class="editable-input" data-field="name" value="{{ property_type.propertiestype_name }}" placeholder="属性名称">
                                    </td>
                                    <td class="editable-cell">
                                        <input type="text" class="editable-input" data-field="code" value="{{ property_type.propertiestype_code }}" placeholder="两位数字" maxlength="2">
                                    </td>
                                    <td class="editable-cell rule-cell">
                                        <select class="editable-select" data-field="rule">
                                            <option value="客户码" {% if property_type.propertiesType_Rule == '客户码' %}selected{% endif %}>客户码</option>
                                            <option value="custom" {% if property_type.propertiesType_Rule != '客户码' %}selected{% endif %}>自定义3位数字</option>
                                        </select>
                                        <input type="text" class="custom-rule-input" data-field="custom-rule" value="{% if property_type.propertiesType_Rule != '客户码' %}{{ property_type.propertiesType_Rule }}{% endif %}" placeholder="三位数字" maxlength="3" {% if property_type.propertiesType_Rule != '客户码' %}style="display:block;"{% endif %}>
                                    </td>
                                    <td class="editable-cell">
                                        <input type="number" class="editable-input" data-field="order" value="{{ property_type.propertiestype_order }}" placeholder="排序">
                                    </td>
                                    <td>
                                        <button class="btn btn-danger btn-sm btn-delete-row">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr class="no-data-row">
                                    <td colspan="6" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% else %}
    <div class="wrapper">
        <!-- 导航栏 -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- 左侧导航栏切换按钮 -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
            </ul>

            <!-- 右侧导航栏 -->
            <ul class="navbar-nav ml-auto">
                <!-- 用户信息下拉菜单 -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline">{{ user_nick|default:username }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> 个人资料
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{% url 'logout' %}" class="dropdown-item">
                            <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                        </a>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- 左侧边栏 -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- 品牌Logo -->
            <a href="#" class="brand-link">
                <span class="brand-text font-weight-light">物料清单管理系统</span>
            </a>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 用户面板 -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-light"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block">{{ user_nick|default:username }}</a>
                    </div>
                </div>

                <!-- 侧边栏菜单 -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'dashboard' %}" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>
                                    仪表盘
                                </p>
                            </a>
                        </li>
                        
                        <li class="nav-item menu-open">
                            <a href="#" class="nav-link active">
                                <i class="nav-icon fas fa-database"></i>
                                <p>
                                    基础数据管理
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{% url 'data_dictionary_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>数据字典</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'customer_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>客户信息</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'properties_type_list' %}" class="nav-link active">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>物料属性</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 主体内容 -->
            <section class="content">
                <div class="container-fluid">
                    <!-- 消息提示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">物料属性列表</h3>
                            <div class="card-tools">
                                <button id="btn-add-row" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加属性
                                </button>
                                <button id="btn-save-all" class="btn btn-success btn-sm ml-2">
                                    <i class="fas fa-save"></i> 保存全部
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="properties-table" class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th width="5%">ID</th>
                                            <th width="25%">属性名称</th>
                                            <th width="15%">前缀编码</th>
                                            <th width="25%">中缀规则</th>
                                            <th width="10%">排序</th>
                                            <th width="20%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for property_type in properties_types %}
                                        <tr data-id="{{ property_type.propertiestype_id }}">
                                            <td>{{ property_type.propertiestype_id }}</td>
                                            <td class="editable-cell">
                                                <input type="text" class="editable-input" data-field="name" value="{{ property_type.propertiestype_name }}" placeholder="属性名称">
                                            </td>
                                            <td class="editable-cell">
                                                <input type="text" class="editable-input" data-field="code" value="{{ property_type.propertiestype_code }}" placeholder="两位数字" maxlength="2">
                                            </td>
                                            <td class="editable-cell rule-cell">
                                                <select class="editable-select" data-field="rule">
                                                    <option value="客户码" {% if property_type.propertiesType_Rule == '客户码' %}selected{% endif %}>客户码</option>
                                                    <option value="custom" {% if property_type.propertiesType_Rule != '客户码' %}selected{% endif %}>自定义3位数字</option>
                                                </select>
                                                <input type="text" class="custom-rule-input" data-field="custom-rule" value="{% if property_type.propertiesType_Rule != '客户码' %}{{ property_type.propertiesType_Rule }}{% endif %}" placeholder="三位数字" maxlength="3" {% if property_type.propertiesType_Rule != '客户码' %}style="display:block;"{% endif %}>
                                            </td>
                                            <td class="editable-cell">
                                                <input type="number" class="editable-input" data-field="order" value="{{ property_type.propertiestype_order }}" placeholder="排序">
                                            </td>
                                            <td>
                                                <button class="btn btn-danger btn-sm btn-delete-row">
                                                    <i class="fas fa-trash"></i> 删除
                                                </button>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr class="no-data-row">
                                            <td colspan="6" class="text-center">暂无数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

    </div>
{% endif %}

<!-- 确认删除的模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个物料属性吗？此操作无法撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 错误提示工具提示 -->
<div class="error-tooltip"></div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 自动隐藏提示信息
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);

        // 全局变量
        let deletedIds = [];
        let rowCounter = 0;
        
        // 显示错误提示
        function showErrorTooltip(element, message) {
            const $element = $(element);
            const position = $element.offset();
            const tooltip = $('.error-tooltip');
            
            tooltip.text(message);
            tooltip.css({
                top: position.top - tooltip.outerHeight() - 5,
                left: position.left
            }).fadeIn(200);
            
            $element.addClass('invalid-input');
            
            // 3秒后隐藏提示
            setTimeout(function() {
                tooltip.fadeOut(200);
                $element.removeClass('invalid-input');
            }, 3000);
        }
        
        // 验证前缀编码
        function validateCode(input) {
            const $input = $(input);
            const code = $input.val().trim();
            const currentRow = $input.closest('tr');
            
            // 验证是否为2位数字
            if (!/^\d{2}$/.test(code)) {
                showErrorTooltip(input, '前缀编码必须是2位数字');
                return false;
            }
            
            // 检查是否与其他行重复
            let isDuplicate = false;
            $('#properties-table tbody tr:not(.hidden-row):not(.no-data-row)').each(function() {
                if ($(this).is(currentRow)) return;
                
                const rowCode = $(this).find('input[data-field="code"]').val().trim();
                if (rowCode === code) {
                    isDuplicate = true;
                    return false;
                }
            });
            
            if (isDuplicate) {
                showErrorTooltip(input, '前缀编码已存在');
                return false;
            }
            
            return true;
        }
        
        // 验证中缀规则
        function validateRule(select, customInput) {
            const $select = $(select);
            const value = $select.val();
            
            if (value === 'custom') {
                const customValue = $(customInput).val().trim();
                
                // 验证是否为3位数字
                if (!/^\d{3}$/.test(customValue)) {
                    showErrorTooltip(customInput, '中缀规则必须是3位数字');
                    return false;
                }
            }
            
            return true;
        }
        
        // 验证属性名称
        function validateName(input) {
            const $input = $(input);
            const name = $input.val().trim();
            
            if (name === '') {
                showErrorTooltip(input, '属性名称不能为空');
                return false;
            }
            
            return true;
        }
        
        // 中缀规则选择变化事件
        $(document).on('change', '.editable-select[data-field="rule"]', function() {
            const $select = $(this);
            const $customInput = $select.siblings('.custom-rule-input');
            
            if ($select.val() === 'custom') {
                $customInput.show();
            } else {
                $customInput.hide();
            }
        });
        
        // 输入框失去焦点时验证
        $(document).on('blur', 'input[data-field="code"]', function() {
            validateCode(this);
        });
        
        $(document).on('blur', 'input[data-field="name"]', function() {
            validateName(this);
        });
        
        $(document).on('blur', 'input[data-field="custom-rule"]', function() {
            validateRule($(this).siblings('.editable-select'), this);
        });

        // 添加新行
        $('#btn-add-row').click(function() {
            // 移除"暂无数据"行
            $('.no-data-row').remove();
            
            rowCounter--;
            const newRow = `
                <tr data-id="new_${rowCounter}">
                    <td>新</td>
                    <td class="editable-cell">
                        <input type="text" class="editable-input" data-field="name" value="" placeholder="属性名称">
                    </td>
                    <td class="editable-cell">
                        <input type="text" class="editable-input" data-field="code" value="" placeholder="两位数字" maxlength="2">
                    </td>
                    <td class="editable-cell rule-cell">
                        <select class="editable-select" data-field="rule">
                            <option value="客户码" selected>客户码</option>
                            <option value="custom">自定义3位数字</option>
                        </select>
                        <input type="text" class="custom-rule-input" data-field="custom-rule" value="" placeholder="三位数字" maxlength="3">
                    </td>
                    <td class="editable-cell">
                        <input type="number" class="editable-input" data-field="order" value="0" placeholder="排序">
                    </td>
                    <td>
                        <button class="btn btn-danger btn-sm btn-delete-row">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </td>
                </tr>
            `;
            $('#properties-table tbody').append(newRow);
        });

        // 删除行
        $(document).on('click', '.btn-delete-row', function() {
            const row = $(this).closest('tr');
            const rowId = row.data('id');
            
            if (String(rowId).startsWith('new_')) {
                // 如果是新添加的行，直接删除
                row.remove();
            } else {
                // 如果是已有的行，显示确认对话框
                $('#deleteConfirmModal').modal('show');
                
                $('#confirmDeleteBtn').off('click').on('click', function() {
                    // 添加到已删除ID列表
                    deletedIds.push(rowId);
                    // 隐藏行
                    row.addClass('hidden-row');
                    $('#deleteConfirmModal').modal('hide');
                });
            }
            
            // 如果表格中没有可见行，显示"暂无数据"行
            if ($('#properties-table tbody tr:not(.hidden-row)').length === 0) {
                $('#properties-table tbody').append(`
                    <tr class="no-data-row">
                        <td colspan="6" class="text-center">暂无数据</td>
                    </tr>
                `);
            }
        });

        // 批量保存
        $('#btn-save-all').click(function() {
            // 收集所有行数据
            const properties = [];
            let hasError = false;
            
            $('#properties-table tbody tr:not(.hidden-row):not(.no-data-row)').each(function() {
                const row = $(this);
                const id = row.data('id');
                const nameInput = row.find('input[data-field="name"]');
                const codeInput = row.find('input[data-field="code"]');
                const ruleSelect = row.find('select[data-field="rule"]');
                const customRuleInput = row.find('input[data-field="custom-rule"]');
                const orderInput = row.find('input[data-field="order"]');
                
                const name = nameInput.val().trim();
                const code = codeInput.val().trim();
                const ruleType = ruleSelect.val();
                const order = orderInput.val().trim() || '0';
                
                // 验证
                if (!validateName(nameInput)) {
                    hasError = true;
                    return false;
                }
                
                if (!validateCode(codeInput)) {
                    hasError = true;
                    return false;
                }
                
                if (!validateRule(ruleSelect, customRuleInput)) {
                    hasError = true;
                    return false;
                }
                
                // 获取最终的规则值
                let rule = ruleType;
                if (ruleType === 'custom') {
                    rule = customRuleInput.val().trim();
                }
                
                // 添加到数据集合
                properties.push({
                    id: String(id).startsWith('new_') ? null : id,
                    name: name,
                    code: code,
                    rule: rule,
                    order: parseInt(order) || 0
                });
            });
            
            if (hasError) return;
            
            // 发送保存请求
            $.ajax({
                url: '{% url "properties_type_list" %}',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    properties: properties,
                    deleted_ids: deletedIds
                }),
                success: function(response) {
                    if (response.status === 'success') {
                        // 显示成功消息
                        const alertHtml = `
                            <div class="alert alert-success alert-dismissible fade show">
                                ${response.message}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        `;
                        $('.container-fluid').prepend(alertHtml);
                        
                        // 刷新页面
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        alert(response.message || '保存失败');
                    }
                },
                error: function(xhr) {
                    let errorMsg = '保存失败';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    alert(errorMsg);
                }
            });
        });
    });
</script>
{% endblock %} 