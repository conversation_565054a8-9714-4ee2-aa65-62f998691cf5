{% extends 'base.html' %}
{% load static %}

{% block title %}物料清单列表{% endblock %}

{% block content %}
<div class="">
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- 左侧面板：物料列表 -->
                <div class="col-md-12" id="leftPanel">
            <!-- 搜索条件 -->
            <div class="card card-primary card-outline mb-3">
                <div class="card-body">
                    <form method="get" action="{% url 'material_list' %}" id="searchForm">
                        {% if is_iframe %}
                        <input type="hidden" name="iframe" value="1">
                        {% endif %}
                        <div class="row align-items-end">
                            <div class="col-md-2">
                                <div class="form-group mb-0">
                                    <label for="material_code">物料编码</label>
                                    <input type="text" class="form-control" id="material_code" name="material_code" value="{{ query_params.material_code }}" placeholder="支持模糊查询">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group mb-0">
                                    <label for="drawing_number">客户图号</label>
                                    <input type="text" class="form-control" id="drawing_number" name="drawing_number" value="{{ query_params.drawing_number }}" placeholder="支持模糊查询">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-0">
                                    <label for="customer_id">客户名称</label>
                                    <select class="form-control select2" id="customer_id" name="customer_id" data-placeholder="请选择或输入客户名称">
                                        <option value="">全部</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.customer_name }}" data-code="{{ customer.customer_code }}" {% if query_params.customer_id == customer.customer_name %}selected{% endif %}>{{ customer.customer_name }}({{ customer.customer_code }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group mb-0">
                                    <label for="material_state">图纸状态</label>
                                    <select class="form-control" id="material_state" name="material_state">
                                        <option value="1" {% if query_params.material_state == '1' or not query_params.material_state %}selected{% endif %}>启用</option>
                                        <option value="-1" {% if query_params.material_state == '-1' %}selected{% endif %}>全部</option>
                                        <option value="2" {% if query_params.material_state == '2' %}selected{% endif %}>停用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-0">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="button" id="clearSearch" class="btn btn-secondary ml-2">
                                        <i class="fas fa-eraser"></i> 清空条件
                                    </button>
                                    <input type="hidden" name="per_page" value="{{ query_params.per_page|default:'10' }}">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 物料列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">物料清单列表</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" id="addMaterialBtn">
                            <i class="fas fa-plus"></i> 单物料录入
                        </button>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>物料编码</th>
                                <th>客户图号</th>
                                <th>客户名称</th>
                                <th>物料分类</th>
                                <th>物料名称</th>
                                <th>版本</th>
                                <th>图纸状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for material in materials %}
                            <tr class="material-row"
                                data-material-id="{{ material.material_id }}"
                                data-material-name="{{ material.material_name }}"
                                style="cursor: pointer;"
                                title="双击编辑物料">
                                <td>{{ material.material_no }}</td>
                                <td>{{ material.material_drawingno }}</td>
                                <td>
                                    {% for customer in customers %}
                                        {% if customer.customer_code == material.material_customer %}
                                            {{ customer.customer_name }}({{ customer.customer_code }})
                                        {% endif %}
                                    {% endfor %}
                                </td>
                                <td>
                                    {% for category in material_categories %}
                                        {% if category.code == material.material_attr %}
                                            {{ category.name }}({{ category.code }})
                                        {% endif %}
                                    {% endfor %}
                                </td>
                                <td>{{ material.material_name }}</td>
                                <td>{{ material.material_version }}</td>
                                <td>
                                    {% if material.material_state == 1 %}
                                        <span class="badge badge-success">启用</span>
                                    {% elif material.material_state == 2 %}
                                        <span class="badge badge-secondary">停用</span>
                                    {% else %}
                                        <span class="badge badge-light">未知</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info edit-material-btn"
                                            data-material-id="{{ material.material_id }}"
                                            data-material-name="{{ material.material_name }}"
                                            title="编辑物料">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button type="button" class="btn btn-sm btn-success view-material-btn"
                                            data-material-id="{{ material.material_id }}"
                                            data-material-name="{{ material.material_name }}"
                                            title="查看物料">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger delete-material-btn"
                                            data-material-id="{{ material.material_id }}"
                                            data-material-name="{{ material.material_name }}"
                                            data-material-code="{{ material.material_no }}"
                                            data-material-drawing="{{ material.material_drawingno }}"
                                            data-material-version="{{ material.material_version }}">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="9" class="text-center">没有找到符合条件的物料记录</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <!-- 分页 -->
                <div class="card-footer clearfix">
                    <ul class="pagination pagination-sm m-0 float-right">
                        {% if materials.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                    首页
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ materials.previous_page_number }}{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                    上一页
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">首页</a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#">上一页</a>
                            </li>
                        {% endif %}
                        
                        {% for i in materials.paginator.page_range %}
                            {% if materials.number == i %}
                                <li class="page-item active">
                                    <a class="page-link" href="#">{{ i }}</a>
                                </li>
                            {% elif i > materials.number|add:'-3' and i < materials.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                        {{ i }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if materials.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ materials.next_page_number }}{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                    下一页
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ materials.paginator.num_pages }}{% for key, value in query_params.items %}{% if value and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% if is_iframe %}&iframe=1{% endif %}">
                                    末页
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                    <div class="float-left">
                        显示 {{ materials.start_index }} 到 {{ materials.end_index }} 条，共 {{ materials.paginator.count }} 条记录
                    </div>
                </div>
            </div>
                </div>

                <!-- 右侧面板：物料详情 -->
                <div class="right-panel" id="rightPanel">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title" id="panelTitle">物料详情</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" id="pinPanel" title="固定面板">
                                    <i class="fas fa-thumbtack"></i>
                                </button>
                                <button type="button" class="btn btn-tool" id="closePanel" title="关闭面板">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0" style="height: calc(100vh - 100px);">
                            <!-- 操作按钮区 -->
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light">
                                <h5 class="mb-0">物料详细信息</h5>
                                <div>
                                    <button class="btn btn-primary btn-sm" id="saveMaterial" title="保存">
                                        <i class="fas fa-save mr-1"></i>保存
                                    </button>
                                    <button class="btn btn-info btn-sm ml-2" id="newVersionMaterial" title="新增版本" style="display: none;">
                                        <i class="fas fa-code-branch mr-1"></i>新增版本
                                    </button>
                                    <button class="btn btn-warning btn-sm ml-2" id="bindMaterial" title="绑定物料" style="display: none;">
                                        <i class="fas fa-link mr-1"></i>绑定物料
                                    </button>
                                </div>
                            </div>

                            <!-- iframe容器 -->
                            <div id="materialFrame" style="height: calc(100% - 60px); width: 100%;">
                                <!-- iframe 嵌入 -->
                                <iframe id="materialIframe" src="" style="width: 100%; height: 100%; border: none;" frameborder="0"></iframe>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">
                    <i class="fas fa-exclamation-triangle mr-2"></i>删除物料确认
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>警告：</strong>此操作不可撤销，请谨慎操作！
                </div>

                <div id="materialInfo">
                    <h6>即将删除的物料：</h6>
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>物料名称：</strong><span id="deleteMaterialName"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>物料编码：</strong><span id="deleteMaterialCode"></span>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <strong>客户图号：</strong><span id="deleteMaterialDrawing"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>版本：</strong><span id="deleteMaterialVersion"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="bomImpactInfo" style="display: none;">
                    <h6 class="text-danger mt-3">
                        <i class="fas fa-exclamation-circle mr-2"></i>BOM影响分析：
                    </h6>
                    <div class="alert alert-danger">
                        <strong>该物料被以下BOM记录使用，删除物料将同时删除这些BOM记录：</strong>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>父物料名称</th>
                                    <th>父物料编码</th>
                                    <th>父客户图号</th>
                                    <th>层级</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="bomImpactList">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="noBomImpact" style="display: none;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        该物料未被任何BOM记录使用，可以安全删除。
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash mr-1"></i>确定删除
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
{% block extra_css %}
<!-- Select2 CSS -->
<link href="/static/css/select2.min.css" rel="stylesheet" />
<link href="/static/css/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<link rel="stylesheet" href="/static/css/jquery-ui.css">

<style>
/* ==================== 右侧面板样式 ==================== */
/* 右侧面板默认隐藏 */
#rightPanel {
    position: fixed;
    top: 0;
    right: -60%;
    width: 58.333333%; /* 相当于col-md-7 */
    height: 100vh;
    z-index: 1050;
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    overflow: hidden;
}

/* 当面板需要显示时，使用这个类 */
#rightPanel.show-panel {
    right: 0 !important;
}

/* 左侧面板样式调整 */
#leftPanel {
    width: 100% !important;
    float: left;
    transition: width 0.3s ease;
}

#leftPanel.panel-narrow {
    width: 41.666667% !important; /* 相当于col-md-5 */
}

/* 右侧面板动画 */
.right-panel {
    transition: all 0.3s ease;
}

/* 固定按钮样式 */
#pinPanel .text-primary {
    color: #007bff !important;
}

/* 确保iframe在面板中正确显示 */
#materialIframe {
    border: none;
    width: 100%;
    height: 100%;
}

/* 响应式样式 */
@media (max-width: 991.98px) {
    #rightPanel {
        width: 90%;
        right: -90%;
    }

    #leftPanel.panel-narrow {
        width: 100% !important;
    }
}

/* 面板标题样式 */
#panelTitle {
    font-size: 1.1rem;
    font-weight: 600;
}

/* 工具按钮样式 */
.card-tools .btn-tool {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.card-tools .btn-tool:hover {
    background-color: rgba(0,0,0,0.1);
    border-radius: 0.25rem;
}

/* 保存遮罩层样式（与bom_tree_view.html保持一致） */
.saving-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99999 !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.saving-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    min-width: 250px;
}

.saving-content .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 表格行选中状态样式 */
.material-row {
    transition: background-color 0.2s ease;
}

.material-row:hover {
    background-color: #f8f9fa !important;
}

.material-row.selected {
    background-color: #e3f2fd !important;
    border-left: 4px solid #2196f3;
}

.material-row.selected:hover {
    background-color: #bbdefb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="/static/js/select2.min.js"></script>
<script src="/static/js/form-logic.js"></script>
<script src="/static/js/jquery-ui.min.js"></script>
<script src="/static/js/material_list.js"></script>

<script>
$(document).ready(function() {
    let currentMaterialId = null;

    // ==================== 右侧面板控制 ====================

    // 确保右侧面板默认隐藏
    $('#rightPanel').removeClass('show-panel');
    $('#leftPanel').removeClass('panel-narrow');

    // 面板固定状态
    let isPanelPinned = false;

    // 显示右侧面板
    function showRightPanel() {
        $('#rightPanel').addClass('show-panel');
        $('#leftPanel').addClass('panel-narrow');
    }

    // 隐藏右侧面板
    function hideRightPanel() {
        $('#rightPanel').removeClass('show-panel');
        $('#leftPanel').removeClass('panel-narrow');
    }

    // 加载物料详情到iframe
    function loadMaterialDetail(materialId, mode, materialName) {
        let iframeUrl = '';
        let panelTitle = '';

        // 保存当前操作模式
        currentMode = mode;

        if (mode === 'add') {
            iframeUrl = `{% url 'material_add' %}?source_type=material_list&iframe=1`;
            panelTitle = '单物料录入';
        } else if (mode === 'edit') {
            iframeUrl = `{% url 'material_add' %}?edit_id=${materialId}&source_type=material_list&iframe=1`;
            panelTitle = `编辑物料: ${materialName}`;
        } else if (mode === 'view') {
            iframeUrl = `{% url 'material_add' %}?edit_id=${materialId}&preview=1&source_type=material_list&iframe=1`;
            panelTitle = `查看物料: ${materialName}`;
        }

        // 更新面板标题
        $('#panelTitle').text(panelTitle);

        // 根据模式显示/隐藏按钮
        updatePanelButtons(mode);

        // 设置iframe的src
        $("#materialIframe").attr("src", iframeUrl);

        console.log(`加载物料详情，模式: ${mode}, URL: ${iframeUrl}`);
    }

    // 更新面板按钮显示状态
    function updatePanelButtons(mode) {
        if (mode === 'view') {
            // 查看模式：隐藏所有操作按钮
            $('#saveMaterial, #newVersionMaterial, #bindMaterial').hide();
        } else if (mode === 'add') {
            // 新增模式：显示保存和新增版本按钮
            $('#saveMaterial, #newVersionMaterial').show();
            $('#bindMaterial').hide();
        } else if (mode === 'edit') {
            // 编辑模式：显示保存和新增版本按钮
            $('#saveMaterial, #newVersionMaterial').show();
            $('#bindMaterial').hide();
        }
    }

    // 显示保存遮罩层（与bom_tree_view.html保持一致）
    function showSavingOverlay() {
        $('#savingOverlay').fadeIn(300);
    }

    // 隐藏保存遮罩层（与bom_tree_view.html保持一致）
    function hideSavingOverlay() {
        $('#savingOverlay').fadeOut(300);
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        if (typeof toastr !== 'undefined') {
            toastr.success(message);
        } else {
            alert(message);
        }
    }

    // 显示错误消息
    function showErrorMessage(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            alert(message);
        }
    }

    // 显示信息消息
    function showInfoMessage(message) {
        if (typeof toastr !== 'undefined') {
            toastr.info(message);
        } else {
            alert(message);
        }
    }

    // 固定面板按钮事件
    $('#pinPanel').click(function() {
        isPanelPinned = !isPanelPinned;
        if (isPanelPinned) {
            $(this).find('i').removeClass('fas fa-thumbtack').addClass('fas fa-thumbtack text-primary');
            $(this).attr('title', '取消固定');
        } else {
            $(this).find('i').removeClass('fas fa-thumbtack text-primary').addClass('fas fa-thumbtack');
            $(this).attr('title', '固定面板');
        }
    });

    // 关闭面板按钮事件
    $('#closePanel').click(function() {
        hideRightPanel();
        isPanelPinned = false;
        $('#pinPanel').find('i').removeClass('fas fa-thumbtack text-primary').addClass('fas fa-thumbtack');
        $('#pinPanel').attr('title', '固定面板');
    });

    // 点击其他地方时隐藏面板（如果未固定）
    $(document).click(function(e) {
        if (!isPanelPinned && !$(e.target).closest('#rightPanel, .edit-material-btn, .view-material-btn, #addMaterialBtn').length) {
            if ($('#rightPanel').hasClass('show-panel')) {
                hideRightPanel();
            }
        }
    });

    // ==================== 按钮事件处理 ====================

    // 单物料录入按钮
    $('#addMaterialBtn').click(function() {
        loadMaterialDetail(null, 'add', '');
        showRightPanel();
    });

    // 编辑物料按钮
    $('.edit-material-btn').click(function() {
        const materialId = $(this).data('material-id');
        const materialName = $(this).data('material-name');

        // 设置对应行为选中状态
        $('.material-row').removeClass('selected');
        $(this).closest('.material-row').addClass('selected');

        loadMaterialDetail(materialId, 'edit', materialName);
        showRightPanel();
    });

    // 查看物料按钮
    $('.view-material-btn').click(function() {
        const materialId = $(this).data('material-id');
        const materialName = $(this).data('material-name');

        // 设置对应行为选中状态
        $('.material-row').removeClass('selected');
        $(this).closest('.material-row').addClass('selected');

        loadMaterialDetail(materialId, 'view', materialName);
        showRightPanel();
    });

    // ==================== 双击交互处理 ====================

    // 表格行双击事件
    $('.material-row').dblclick(function() {
        const materialId = $(this).data('material-id');
        const materialName = $(this).data('material-name');

        // 移除其他行的选中状态
        $('.material-row').removeClass('selected');

        // 设置当前行为选中状态
        $(this).addClass('selected');

        // 默认进入编辑模式（后续可根据用户角色判断）
        loadMaterialDetail(materialId, 'edit', materialName);
        showRightPanel();
    });

    // 单击事件处理（仅选中行，不打开面板）
    $('.material-row').click(function(e) {
        // 如果点击的是按钮，不处理行选中
        if ($(e.target).closest('button').length > 0) {
            return;
        }

        // 移除其他行的选中状态
        $('.material-row').removeClass('selected');

        // 设置当前行为选中状态
        $(this).addClass('selected');
    });

    // 防止双击时选中文本
    $('.material-row').on('selectstart', function() {
        return false;
    });

    // ==================== 操作按钮事件处理 ====================

    // 当前操作模式
    let currentMode = '';

    // 注意：保存物料按钮事件已在material_list.js中处理

    // 新增版本按钮事件（与bom_tree_view.html保持一致）
    $("#newVersionMaterial").on("click", function() {
        const iframe = document.getElementById('materialIframe');
        if (iframe && iframe.contentWindow) {
            try {
                // 使用postMessage发送新增版本命令
                iframe.contentWindow.postMessage({action: 'newVersion'}, '*');
                console.log('发送新增版本命令到iframe');
            } catch (e) {
                console.error('新增版本失败:', e);
                // 降级处理：直接尝试访问iframe内容
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const newVersionBtn = iframeDoc.querySelector('.new-version-btn');
                    if (newVersionBtn) {
                        newVersionBtn.click();
                    } else {
                        showErrorMessage("新增版本失败：未找到相应按钮");
                    }
                } catch (e) {
                    showErrorMessage("新增版本失败：无法访问iframe内容");
                }
            }
        } else {
            showErrorMessage("请先选择一个物料进行编辑");
        }
    });

    // 绑定物料按钮点击事件
    $('#bindMaterial').click(function() {
        showInfoMessage('绑定物料功能在此页面不适用');
    });

    // 注意：iframe消息监听已在material_list.js中处理

    // ==================== 原有删除功能 ====================

    // 删除按钮点击事件
    $('.delete-material-btn').click(function() {
        currentMaterialId = $(this).data('material-id');
        const materialName = $(this).data('material-name');
        const materialCode = $(this).data('material-code');
        const materialDrawing = $(this).data('material-drawing');
        const materialVersion = $(this).data('material-version');

        // 设置物料信息
        $('#deleteMaterialName').text(materialName || '未命名');
        $('#deleteMaterialCode').text(materialCode || '无');
        $('#deleteMaterialDrawing').text(materialDrawing || '无');
        $('#deleteMaterialVersion').text(materialVersion || '无');

        // 检查BOM影响
        checkBomImpact(currentMaterialId);

        // 显示模态框
        $('#deleteConfirmModal').modal('show');
    });

    // 确认删除按钮点击事件
    $('#confirmDeleteBtn').click(function() {
        if (currentMaterialId) {
            // 构建删除URL
            const deleteUrl = `/materials/delete/${currentMaterialId}/`;

            // 获取当前页面的查询参数
            const urlParams = new URLSearchParams(window.location.search);
            const queryString = urlParams.toString();

            // 添加查询参数到删除URL
            const finalUrl = queryString ? `${deleteUrl}?${queryString}` : deleteUrl;

            // 跳转到删除URL
            window.location.href = finalUrl;
        }
    });

    // 检查BOM影响
    function checkBomImpact(materialId) {
        $.ajax({
            url: '/check_material_delete_impact/',
            type: 'GET',
            data: {
                'material_id': materialId
            },
            success: function(response) {
                if (response.success) {
                    if (response.has_impact) {
                        // 有BOM影响
                        $('#bomImpactInfo').show();
                        $('#noBomImpact').hide();

                        // 渲染影响的BOM列表
                        renderBomImpactList(response.affected_boms);

                        // 更新确认按钮文本
                        $('#confirmDeleteBtn').html('<i class="fas fa-exclamation-triangle mr-1"></i>确定删除（包含' + response.count + '个BOM记录）');
                    } else {
                        // 无BOM影响
                        $('#bomImpactInfo').hide();
                        $('#noBomImpact').show();

                        // 恢复确认按钮文本
                        $('#confirmDeleteBtn').html('<i class="fas fa-trash mr-1"></i>确定删除');
                    }
                } else {
                    alert('检查BOM影响失败：' + response.error);
                }
            },
            error: function(xhr, status, error) {
                alert('检查BOM影响失败：' + error);
            }
        });
    }

    // 渲染BOM影响列表
    function renderBomImpactList(bomList) {
        const tbody = $('#bomImpactList');
        tbody.empty();

        bomList.forEach(function(bom) {
            const row = `
                <tr>
                    <td>${bom.parent_material_name}</td>
                    <td>${bom.parent_material_code || '无'}</td>
                    <td>${bom.parent_material_drawing || '无'}</td>
                    <td>第${bom.bom_level}层</td>
                    <td>
                        <span class="badge ${bom.bom_state === '启用' ? 'badge-success' : 'badge-secondary'}">
                            ${bom.bom_state}
                        </span>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }
});
</script>

<!-- 保存遮罩层将由JavaScript动态创建 -->

{% endblock %}