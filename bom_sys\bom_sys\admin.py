from django.contrib import admin
from .models import User, Material, Customer, Bom, Datadictionary, PropertiesType, ProcessRoute, Equipment


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    """用户管理配置"""
    list_display = ('user_id', 'user_name', 'user_nick', 'user_role', 'user_logintime')
    search_fields = ('user_name', 'user_nick')
    list_filter = ('user_role',)
    ordering = ('user_id',)


@admin.register(Material)
class MaterialAdmin(admin.ModelAdmin):
    """物料管理配置"""
    list_display = ('material_id', 'material_no', 'material_drawingno', 'material_quality', 'material_spec')
    search_fields = ('material_no', 'material_drawingno')
    list_filter = ('material_state', 'material_customer')
    ordering = ('-material_uptime',)


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    """客户管理配置"""
    list_display = ('customer_id', 'customer_name', 'customer_code', 'customer_addtime', 'customer_order')
    search_fields = ('customer_name', 'customer_code')
    list_filter = ('customer_addtime',)
    ordering = ('customer_order', 'customer_id')


@admin.register(Bom)
class BomAdmin(admin.ModelAdmin):
    """BOM管理配置"""
    list_display = ('bom_id',)


@admin.register(Datadictionary)
class DatadictionaryAdmin(admin.ModelAdmin):
    """数据字典管理配置"""
    list_display = ('datadictionary_id', 'datadictionary_name', 'datadictionary_code', 'datadictionary_tag', 'datadictionary_order')
    search_fields = ('datadictionary_name', 'datadictionary_code')
    list_filter = ('datadictionary_tag',)
    ordering = ('datadictionary_order', 'datadictionary_id')





@admin.register(ProcessRoute)
class ProcessRouteAdmin(admin.ModelAdmin):
    """工艺路线管理配置"""
    list_display = ('processroute_id', 'processroute_no', 'processroute_name', 'processroute_order', 'get_process_count')
    search_fields = ('processroute_no', 'processroute_name', 'processroute_content')
    list_filter = ('processroute_no',)
    ordering = ('processroute_order', 'processroute_id')

    fieldsets = (
        ('基本信息', {
            'fields': ('processroute_no', 'processroute_name', 'processroute_order')
        }),
        ('工序内容', {
            'fields': ('processroute_content',),
            'description': '工序清单，以|分割多个工序'
        }),
        ('工序详情', {
            'fields': (
                'processroute_times', 'processroute_device', 'processroute_price',
                'processroute_type', 'processroute_outsupplier', 'processroute_outprice',
                'processroute_important'
            ),
            'description': '各字段都以|分割，顺序与工序清单一致',
            'classes': ('collapse',)
        }),
    )

    def get_process_count(self, obj):
        """获取工序数量"""
        return len(obj.get_process_list())
    get_process_count.short_description = '工序数量'


@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    """设备管理配置"""
    list_display = ('equipment_id', 'equipment_no', 'equipment_name', 'equipment_department', 'equipment_line', 'equipment_spec', 'equipment_quantity')
    search_fields = ('equipment_no', 'equipment_name', 'equipment_department')
    list_filter = ('equipment_department', 'equipment_line', 'equipment_origin')
    ordering = ('equipment_id',) 