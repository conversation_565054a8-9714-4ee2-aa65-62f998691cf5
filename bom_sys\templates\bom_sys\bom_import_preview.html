{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}导入BOM预览 - {{ selected_customer.customer_name }}{% endblock %}

{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}

{% block content %}
<div class="content-wrapper">
    {% csrf_token %}
    
    <!-- 导入预览模式的头部 -->
    <section class="content-header">
        <div class="container-fluid">
            
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible" data-auto-dismiss="false">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
            
            {% if error_details %}
            <div class="mt-3">
                <button class="btn btn-warning" type="button" data-toggle="collapse" data-target="#errorDetails" aria-expanded="false" aria-controls="errorDetails">
                    显示/隐藏导入错误详情 ({{ error_details|length }}个错误)
                </button>
                <div class="collapse mt-2" id="errorDetails">
                    <div class="card card-body bg-danger text-white">
                        <h5>导入错误详情</h5>
                        <div style="max-height: 300px; overflow-y: auto;">
                            <ol>
                                {% for error in error_details %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- 导入结果统计信息 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">导入统计</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="info-box bg-success">
                                        <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">成功导入</span>
                                            <span class="info-box-number">{{ success_count }}</span>
                                            
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-danger">
                                        <span class="info-box-icon"><i class="fas fa-times"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">导入失败</span>
                                            <span class="info-box-number">{{ fail_count }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-info">
                                        <span class="info-box-icon"><i class="fas fa-plus"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">新增物料</span>
                                            <span class="info-box-number">{{ new_materials }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-warning">
                                        <span class="info-box-icon"><i class="fas fa-edit"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">更新物料</span>
                                            <span class="info-box-number">{{ updated_materials }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% if top_material_name %}
                            <div>
                                <p><strong>顶级物料信息：</strong> {{ top_material_name }} (编号: {{ top_material_no }}{% if top_material_version %}, 版本: {{ top_material_version }}{% endif %})</p>
                            </div>
                            {% endif %}
                            
                            <!-- 删除重复的错误详情区域 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h5><i class="icon fas fa-info-circle"></i> 导入预览</h5>
                这里显示的是导入的临时数据，请检查导入的物料结构，确认无误后点击"确认导入"，或进行必要的修改后再确认。
            </div>
            
        </div>
    </section>
    
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- 结果列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">导入的BOM清单</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>客户图号</th>
                                <th>物料编码</th>
                                <th>版本</th>
                                <th>存货名称</th>
                                <th>客户名称</th>
                                <th>导入状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if imported_materials %}
                                {% for material in imported_materials %}
                                    <tr class="clickable-row" data-bom-id="{{ material.bom_id }}" data-material-id="{{ material.material_id }}" data-customer-code="{{ material.customer_code }}" data-customer-name="{{ material.customer_name }}">
                                        <td>{{ forloop.counter|stringformat:"04d" }}</td>
                                        <td>{{ material.material_drawingno }}</td>
                                        <td>{{ material.material_no }}</td>
                                        <td>{{ material.material_version }}</td>
                                        <td>{{ material.material_name }}</td>
                                        <td>{{ material.customer_name }}({{ material.customer_code }})</td>
                                        <td>
                                            <span class="badge badge-warning">临时数据</span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-info view-bom-tree" data-bom-id="{{ material.bom_id }}" data-material-id="{{ material.material_id }}" title="查看BOM树">
                                                <i class="fas fa-sitemap"></i>
                                            </button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">暂无导入的BOM数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 导入操作按钮区 -->
            <div class="action-buttons mt-3">
                <form method="post">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="confirm_import">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check mr-1"></i> 确认导入
                    </button>

                    <button type="button" class="btn btn-secondary ml-2" id="cancelImportBtn">
                        <i class="fas fa-times mr-1"></i> 取消导入
                    </button>
                </form>

                <form id="cancelImportForm" method="post" style="display:none;">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="cancel_import">
                </form>
            </div>
        </div>
    </section>
</div>

<!-- BOM树形视图模态窗口 -->
<div class="modal fade" id="bomTreeModal" tabindex="-1" role="dialog" aria-labelledby="bomTreeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document" style="max-width: 100%; height: 100vh; margin: 0;">
        <div class="modal-content" style="height: 100%; border-radius: 0;">
            <div class="modal-header">
                <h5 class="modal-title" id="bomTreeModalLabel">
                    <i class="fas fa-sitemap mr-2"></i>
                    BOM树形视图: <span id="modalMaterialName"></span>
                    <small class="text-muted ml-2" id="modalMaterialInfo"></small>
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-0" style="height: calc(100vh - 120px);">
                <div class="row bom-container h-100">
                    <!-- 左侧面板：BOM列表 -->
                    <div class="col-12 left-panel" id="modalLeftPanel">
                        <div class="card h-100">
                            <div class="card-header">
                                <h3 class="card-title">BOM结构</h3>
                                <div class="card-tools d-flex align-items-center">
                                    <button class="btn btn-secondary btn-sm mr-2" id="modalRefreshBomData">
                                        <i class="fas fa-sync-alt mr-1"></i>刷新
                                    </button>
                                    <div class="input-group input-group-sm" style="width: 200px;">
                                        <input type="text" id="modalSearchBomInput" class="form-control" placeholder="搜索...">
                                        <div class="input-group-append">
                                            <button type="button" id="modalSearchBomBtn" class="btn btn-default">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body table-responsive p-0" style="max-height: calc(100vh - 220px); overflow-y: auto;">
                                <table class="table table-hover table-head-fixed text-nowrap" id="modalBomTable">
                                    <thead>
                                        <tr>
                                            <th style="width: 40px">序号</th>
                                            <th style="min-width: 60px">层级</th>
                                            <th style="min-width: 140px">物料编码</th>
                                            <th style="min-width: 120px">客户图号</th>
                                            <th style="min-width: 180px">物料名称</th>
                                            <th style="width: 40px">数量</th>
                                            <th style="width: 60px">材质</th>
                                            <th style="width: 80px">规格</th>
                                            <th style="width: 40px">版本</th>
                                            <th style="width: 60px">生产数量</th>
                                            <th style="width: 60px">零件用量</th>
                                            <th style="width: 60px">计量单位</th>
                                            <th style="width: 60px">损耗率</th>
                                            <th style="width: 40px">客供</th>
                                            <th style="width: 40px">外购</th>
                                            <th style="width: 40px">销售</th>
                                            <th style="width: 40px">自制</th>
                                            <th style="width: 40px">委外</th>
                                            <th style="width: 60px">生产耗用</th>
                                            <th style="width: 60px">批号管理</th>
                                            <th style="width: 80px">下料尺寸</th>
                                        </tr>
                                    </thead>
                                    <tbody id="modalBomTableBody">
                                        <!-- 数据将通过JavaScript动态加载 -->
                                        <tr>
                                            <td colspan="21" class="text-center">
                                                <i class="fas fa-spinner fa-spin mr-2"></i>正在加载BOM数据...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧面板：物料详情 -->
                    <div class="col-md-7 right-panel" id="modalRightPanel" style="display: none;">
                        <div class="card h-100">
                            <div class="card-header">
                                <h3 class="card-title">物料详情</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" id="modalPinPanel" title="固定面板">
                                        <i class="fas fa-thumbtack"></i>
                                    </button>
                                    <button type="button" class="btn btn-tool" id="modalClosePanel" title="关闭面板">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-0" style="height: calc(100vh - 200px);">
                                <!-- iframe容器 -->
                                <div id="modalMaterialFrame" style="height: 100%; width: 100%;">
                                    <!-- 操作按钮区 -->
                                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light">
                                        <h5 class="mb-0">物料详细信息 <span class="badge badge-warning">临时数据</span></h5>
                                        <div>
                                            <button class="btn btn-primary btn-sm" id="modalSaveMaterial">
                                                <i class="fas fa-save mr-1"></i>保存
                                            </button>
                                            <button class="btn btn-info btn-sm ml-2" id="modalNewVersionMaterial">
                                                <i class="fas fa-code-branch mr-1"></i>新增版本
                                            </button>
                                            <button class="btn btn-warning btn-sm ml-2" id="modalBindMaterial">
                                                <i class="fas fa-link mr-1"></i>绑定物料
                                            </button>
                                        </div>
                                    </div>

                                    <!-- iframe 嵌入 -->
                                    <iframe id="modalMaterialIframe" src="" style="width: 100%; height: calc(100% - 50px); border: none;" frameborder="0"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 绑定物料模态框 -->
<div class="modal fade" id="modalBindMaterialModal" tabindex="-1" role="dialog" aria-labelledby="modalBindMaterialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalBindMaterialModalLabel">
                    <i class="fas fa-link mr-2"></i>绑定物料
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                {% csrf_token %}
                <!-- 搜索区域 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="modalMaterialSearchInput" placeholder="输入物料名称或编号搜索">
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" id="modalMaterialStateFilter">
                            <option value="1">在用</option>
                            <option value="1,2">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-primary" id="modalSearchMaterialBtn">
                            <i class="fas fa-search mr-1"></i>搜索
                        </button>
                    </div>
                    <div class="col-md-5">
                        <small class="text-muted">当前客户：<span id="modalCurrentCustomerName"></span></small>
                    </div>
                </div>

                <!-- 物料列表表格 -->
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="modalMaterialBindTable">
                        <thead class="thead-light">
                            <tr>
                                <th width="15%">物料编号</th>
                                <th width="25%">物料名称</th>
                                <th width="15%">客户图号</th>
                                <th width="10%">版本</th>
                                <th width="15%">物料分类</th>
                                <th width="10%">状态</th>
                                <th width="10%">绑定</th>
                            </tr>
                        </thead>
                        <tbody id="modalMaterialBindTableBody">
                            <tr>
                                <td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div id="modalMaterialPaginationInfo" class="text-muted"></div>
                    </div>
                    <div class="col-md-6">
                        <nav aria-label="物料分页">
                            <ul class="pagination pagination-sm justify-content-end" id="modalMaterialPagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
    .content-wrapper {
        margin-left: 0 !important;
    }

    /* 确保右侧面板默认隐藏，不受Bootstrap类影响 */
    #modalRightPanel {
        display: none !important;
    }

    /* 当面板需要显示时，使用这个类 */
    #modalRightPanel.show-panel {
        display: block !important;
    }

    /* 添加临时数据标记样式 */
    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }

    /* 导入操作按钮区样式 */
    .action-buttons {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 表格行悬停效果 */
    .table-hover-highlight {
        background-color: rgba(0, 123, 255, 0.1) !important;
    }

    .clickable-row {
        transition: background-color 0.2s;
    }

    /* 模态框BOM表格层级样式 */
    #modalBomTable .level-indent {
        display: inline-block;
        width: 20px;
        height: 1px;
    }

    #modalBomTable .selected-row {
        background-color: rgba(0, 123, 255, 0.1);
    }

    /* 层级样式，每个层级使用不同的左边距和颜色 */
    #modalBomTable .level-0 {
        font-weight: bold;
    }

    #modalBomTable .level-1 {
        padding-left: 10px !important;
        border-left: 3px solid #007bff;
    }

    #modalBomTable .level-2 {
        padding-left: 20px !important;
        border-left: 3px solid #28a745;
    }

    #modalBomTable .level-3 {
        padding-left: 30px !important;
        border-left: 3px solid #fd7e14;
    }

    #modalBomTable .level-4 {
        padding-left: 40px !important;
        border-left: 3px solid #dc3545;
    }

    #modalBomTable .level-5 {
        padding-left: 50px !important;
        border-left: 3px solid #6f42c1;
    }

    #modalBomTable .level-6 {
        padding-left: 60px !important;
        border-left: 3px solid #20c997;
    }

    /* BOM树模态窗口样式 */
    #bomTreeModal .modal-dialog {
        margin: 1rem;
    }

    #bomTreeModal .bom-container {
        margin: 0;
    }

    #bomTreeModal .bom-row {
        cursor: pointer;
    }

    #bomTreeModal .bom-row:hover {
        background-color: #f8f9fa;
    }

    #bomTreeModal .bom-row.selected {
        background-color: #e3f2fd;
    }

    /* 表格固定表头 */
    #modalBomTable thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 10;
    }

    /* 修改状态的行标记为黄色 */
    #modalBomTable .modified-row {
        background-color: #fff3cd !important;
        border-left: 4px solid #ffc107;
    }

    #modalBomTable .modified-row:hover {
        background-color: #ffeaa7 !important;
    }

    #modalBomTable .modified-row.selected {
        background-color: #ffe082 !important;
        border-left: 4px solid #ff9800;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
window.selectedBomId = null;
window.selectedMaterialId = null;
window.currentCustomer = null;
window.currentMaterialPage = 1;

$(document).ready(function() {
    // 确保右侧面板默认隐藏
    $('#modalRightPanel').removeClass('show-panel');
    $('#modalLeftPanel').removeClass('col-md-5').addClass('col-12');

    // 关闭可能残留的加载框
    try {
        // 首先尝试关闭当前页面的等待框
        if ($('#loadingModal').length) {
            $('#loadingModal').modal('hide');
        }

        // 尝试关闭父页面的等待框（如果在iframe中）
        if (window.parent && window.parent.$) {
            window.parent.$('#loadingModal').modal('hide');
        }
        console.log('等待框关闭成功');
    } catch(err) {
        console.error('关闭等待框时出错:', err);
    }

    // 取消导入按钮点击事件
    $('#cancelImportBtn').on('click', function() {
        if (confirm('确定要取消导入吗？所有导入的临时数据将被删除。')) {
            $('#cancelImportForm').submit();
        }
    });

    // 确保AdminLTE卡片折叠功能正常工作
    $('.card-header .btn-tool[data-card-widget="collapse"]').on('click', function() {
        var $card = $(this).closest('.card');
        var $cardBody = $card.find('.card-body');
        var $icon = $(this).find('i');

        if ($cardBody.is(':visible')) {
            $cardBody.slideUp();
            $icon.removeClass('fa-minus').addClass('fa-plus');
        } else {
            $cardBody.slideDown();
            $icon.removeClass('fa-plus').addClass('fa-minus');
        }
    });

    // 表格行双击事件和按钮点击事件
    function openBomTreeModal(bomId, materialId) {
        if (bomId && materialId) {
            // 显示模态窗口
            $('#bomTreeModal').modal('show');

            // 加载BOM树数据
            loadBomTreeData(bomId, materialId);
        }
    }

    // 表格行双击事件
    $(".clickable-row").dblclick(function() {
        var bomId = $(this).data("bom-id");
        var materialId = $(this).data("material-id");
        openBomTreeModal(bomId, materialId);
    });

    // 查看BOM树按钮点击事件
    $(".view-bom-tree").click(function() {
        var bomId = $(this).data("bom-id");
        var materialId = $(this).data("material-id");
        openBomTreeModal(bomId, materialId);
    });

    // 添加鼠标悬停效果，提示可双击
    $(".clickable-row").hover(
        function() {
            $(this).css("cursor", "pointer");
            $(this).addClass("table-hover-highlight");
        },
        function() {
            $(this).removeClass("table-hover-highlight");
        }
    );

    // 加载BOM树数据的函数
    function loadBomTreeData(bomId, materialId) {
        console.log('loadBomTreeData 被调用，参数:', {bomId, materialId});

        // 存储当前BOM ID和Material ID
        $('#bomTreeModal').data('current-bom-id', bomId);
        $('#bomTreeModal').data('current-material-id', materialId);

        // 获取当前用户ID
        var userId = {{ request.session.user_id|default:0 }};
        console.log('当前用户ID:', userId);

        // 显示加载状态
        $('#modalBomTableBody').html('<tr><td colspan="21" class="text-center"><i class="fas fa-spinner fa-spin mr-2"></i>正在加载BOM数据...</td></tr>');

        // 发送AJAX请求获取临时BOM数据
        console.log('发送AJAX请求到:', '{% url "get_bom_tree_data" %}');
        $.ajax({
            url: '{% url "get_bom_tree_data" %}',
            type: 'GET',
            data: {
                'bom_id': bomId,
                'is_temp': true,  // 标记为临时数据
                'user_id': userId
            },
            success: function(response) {
                console.log('AJAX请求成功，响应:', response);
                if (response.success) {
                    // 更新模态窗口标题
                    if (response.material) {
                        $('#modalMaterialName').text(response.material.material_name || '');
                        $('#modalMaterialInfo').text((response.material.material_drawingno || '') + ' - ' + (response.material.material_version || ''));
                    }

                    // 设置客户信息（从当前页面的客户信息获取）
                    {% if selected_customer %}
                    window.currentCustomer = {
                        id: {{ selected_customer.customer_id }},
                        name: "{{ selected_customer.customer_name }}",
                        code: "{{ selected_customer.customer_code }}"
                    };
                    {% endif %}

                    // 渲染BOM树表格
                    renderBomTreeTable(response.bom_tree);
                } else {
                    console.error('BOM数据加载失败:', response.message);
                    $('#modalBomTableBody').html('<tr><td colspan="21" class="text-center text-danger">加载失败: ' + (response.message || '未知错误') + '</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX请求失败:', {xhr, status, error});
                $('#modalBomTableBody').html('<tr><td colspan="21" class="text-center text-danger">加载失败: ' + error + '</td></tr>');
            }
        });
    }

    // 渲染BOM树表格
    function renderBomTreeTable(bomTree) {
        var tbody = $('#modalBomTableBody');
        tbody.empty();

        if (!bomTree || bomTree.length === 0) {
            tbody.html('<tr><td colspan="21" class="text-center">暂无BOM数据</td></tr>');
            return;
        }

        var rowIndex = 1;
        bomTree.forEach(function(node) {
            renderBomNode(tbody, node, rowIndex++);
        });
    }

    // 渲染单个BOM节点
    function renderBomNode(tbody, node, index) {
        var levelIndent = '&nbsp;'.repeat(node.level * 4);
        var row = $('<tr class="bom-row level-' + (node.level || 0) + '" data-material-id="' + (node.material_id || '') + '" data-bom-id="' + (node.bom_id || '') + '" data-material-tempstate="' + (node.material_tempstate || 0) + '" data-bom-state="' + (node.bom_state || 0) + '">');

        // 检查是否需要标记为黄色（Material_tempState=2 或 bom_State=2）
        var isModified = (node.material_tempstate == 2) || (node.bom_state == 2);
        if (isModified) {
            row.addClass('modified-row');
        }

        row.html(`
            <td>${index}</td>
            <td>${levelIndent}${node.level}</td>
            <td class="level-${node.level || 0}">${node.material_no || ''}</td>
            <td>${node.material_drawingno || ''}</td>
            <td>${node.material_name || ''}</td>
            <td>${node.bom_num || ''}</td>
            <td>${node.material_quality || ''}</td>
            <td>${node.material_spec || ''}</td>
            <td>${node.material_version || ''}</td>
            <td>${node.bom_producecount || ''}</td>
            <td>${node.bom_partcount || ''}</td>
            <td>${node.material_unit || ''}</td>
            <td>${node.bom_lossrate || ''}</td>
            <td>${node.material_customersupply ? '是' : '否'}</td>
            <td>${node.material_outsourcing ? '是' : '否'}</td>
            <td>${node.material_sales ? '是' : '否'}</td>
            <td>${node.material_selfmade ? '是' : '否'}</td>
            <td>${node.material_subcontract ? '是' : '否'}</td>
            <td>${node.material_productionconsumption ? '是' : '否'}</td>
            <td>${node.material_batchmanagement ? '是' : '否'}</td>
            <td>${node.material_cuttingsize || ''}</td>
        `);

        // 添加点击事件
        row.click(function() {
            // 移除其他行的选中状态
            $('.bom-row').removeClass('selected');
            // 添加当前行的选中状态
            $(this).addClass('selected');

            // 设置选中的BOM ID和物料ID
            window.selectedBomId = node.bom_id;
            window.selectedMaterialId = node.material_id;

            // 显示物料详情，传递状态信息
            showMaterialDetail(node.material_id, node.material_tempstate, node.bom_state);
        });

        tbody.append(row);

        // 递归渲染子节点
        if (node.children && node.children.length > 0) {
            node.children.forEach(function(child) {
                renderBomNode(tbody, child, ++index);
            });
        }
    }

    // 显示物料详情
    function showMaterialDetail(materialId, materialTempState, bomState) {
        if (!materialId) return;

        // 显示右侧面板
        $('#modalRightPanel').addClass('show-panel');
        $('#modalLeftPanel').removeClass('col-12').addClass('col-md-5');

        // 构建iframe URL，临时数据默认进入编辑模式（不使用preview=1）
        // 传递临时状态参数，确保保存时保持原状态
        var iframeUrl = `/materials/add/?edit_id=${materialId}&iframe=1&source_type=bom_list&temp_mode=1&temp_state=${materialTempState || 1}&bom_state=${bomState || 1}`;

        // 监听iframe加载完成
        $('#modalMaterialIframe').off('load').on('load', function() {
            // 延迟一点时间确保iframe内容完全加载
            setTimeout(function() {
                // iframe内容已完全加载
            }, 1000);
        });

        $('#modalMaterialIframe').attr('src', iframeUrl);

        // 存储当前选中的物料ID和状态
        window.selectedMaterialId = materialId;
        window.selectedMaterialTempState = materialTempState;
        window.selectedBomState = bomState;


    }

    // 模态窗口关闭面板按钮
    $('#modalClosePanel').click(function() {
        $('#modalRightPanel').removeClass('show-panel');
        $('#modalLeftPanel').removeClass('col-md-5').addClass('col-12');
    });

    // 模态窗口搜索功能
    $('#modalSearchBomBtn').click(function() {
        var searchText = $('#modalSearchBomInput').val().toLowerCase();
        filterBomTable(searchText);
    });

    $('#modalSearchBomInput').keypress(function(e) {
        if (e.which == 13) { // Enter键
            var searchText = $(this).val().toLowerCase();
            filterBomTable(searchText);
        }
    });

    // 过滤BOM表格
    function filterBomTable(searchText) {
        $('#modalBomTableBody tr').each(function() {
            var row = $(this);
            var text = row.text().toLowerCase();
            if (searchText === '' || text.indexOf(searchText) > -1) {
                row.show();
            } else {
                row.hide();
            }
        });
    }

    // 保存物料按钮事件
    $('#modalSaveMaterial').click(function() {
        console.log("点击保存按钮");
        const iframe = document.getElementById('modalMaterialIframe');
        if (iframe && iframe.contentWindow) {
            try {
                // 检查是否为新增模式
                let isAddMode = false;
                let currentBomId = window.selectedBomId;

                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const formModeInput = iframeDoc.getElementById('formMode');
                    isAddMode = formModeInput && formModeInput.value === 'add';
                } catch (e) {
                    // 如果无法访问iframe内容，从URL判断
                    const iframeSrc = iframe.src;
                    isAddMode = !iframeSrc.includes('edit_id=');
                }

                // 使用postMessage发送保存命令，包含必要参数
                const messageData = {
                    action: 'save',
                    bomId: currentBomId,
                    isAddMode: isAddMode,
                    tempState: window.selectedMaterialTempState || 1,  // 保持原临时状态
                    bomState: window.selectedBomState || 1  // 保持原BOM状态
                };

                console.log("发送保存命令到iframe，参数:", messageData);
                console.log("当前选中的BOM ID:", window.selectedBomId);
                console.log("当前选中的物料ID:", window.selectedMaterialId);
                console.log("模态框存储的BOM ID:", $('#bomTreeModal').data('current-bom-id'));
                console.log("模态框存储的物料ID:", $('#bomTreeModal').data('current-material-id'));

                iframe.contentWindow.postMessage(messageData, '*');

                // 添加备用刷新机制：如果3秒后没有收到刷新消息，手动刷新
                window.saveTimeout = setTimeout(function() {
                    console.log('3秒后没有收到刷新消息，手动刷新BOM数据');
                    var bomId = $('#bomTreeModal').data('current-bom-id');
                    var materialId = $('#bomTreeModal').data('current-material-id');
                    if (bomId && materialId) {
                        loadBomTreeData(bomId, materialId);
                        showModalSuccessMessage('物料保存完成，BOM数据已更新');
                    }
                }, 3000);
            } catch (error) {
                console.error("发送消息失败:", error);
                alert("保存失败：无法与iframe通信");
            }
        } else {
            console.error("iframe不存在或无法访问");
            alert("请先选择一个物料进行编辑");
        }
    });

    // 新增版本按钮事件
    $('#modalNewVersionMaterial').click(function() {
        const iframe = document.getElementById('modalMaterialIframe');
        if (iframe && iframe.contentWindow) {
            try {
                // 使用postMessage发送新增版本命令
                iframe.contentWindow.postMessage({action: 'newVersion'}, '*');
                console.log("发送新增版本命令到iframe");
            } catch (error) {
                console.error("发送消息失败:", error);
                // 降级处理：直接尝试访问iframe内容
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const newVersionBtn = iframeDoc.querySelector('.new-version-btn');
                    if (newVersionBtn) {
                        newVersionBtn.click();
                    } else {
                        alert("新增版本失败：未找到相应按钮");
                    }
                } catch (e) {
                    alert("新增版本失败：无法访问iframe内容");
                }
            }
        } else {
            alert("请先选择一个物料进行编辑");
        }
    });

    // 手动刷新BOM数据按钮事件
    $('#modalRefreshBomData').click(function() {
        console.log('手动刷新BOM数据');
        var bomId = $('#bomTreeModal').data('current-bom-id');
        var materialId = $('#bomTreeModal').data('current-material-id');
        console.log('手动刷新参数:', {bomId, materialId});
        if (bomId && materialId) {
            loadBomTreeData(bomId, materialId);
            showModalSuccessMessage('BOM数据已刷新');
        } else {
            console.error('无法手动刷新：缺少BOM ID或物料ID');
            alert('无法刷新：缺少必要的参数');
        }
    });

    // 绑定物料按钮事件
    $('#modalBindMaterial').click(function() {
        if (!window.selectedBomId) {
            alert("请先选择一个BOM节点");
            return;
        }

        // 检查客户信息
        if (!window.currentCustomer || !window.currentCustomer.code) {
            alert("无法获取客户信息，请重新进入页面");
            return;
        }

        // 设置当前客户名称
        $('#modalCurrentCustomerName').text(window.currentCustomer.name);

        // 重置搜索条件
        $('#modalMaterialSearchInput').val('');
        $('#modalMaterialStateFilter').val('1');

        // 显示模态框
        $('#modalBindMaterialModal').modal('show');

        // 默认加载数据
        window.currentMaterialPage = 1;
        searchModalMaterials();
    });

    // 测试函数：模拟接收刷新消息
    window.testRefreshMessage = function() {
        console.log('测试刷新消息');
        window.postMessage({
            action: 'refreshBomData',
            success: true
        }, '*');
    };

    // 监听来自iframe的消息
    window.addEventListener('message', function(event) {

        // 处理物料保存成功的消息
        if (event.data && event.data.action === 'refreshBomData' && event.data.success) {
            console.log('收到iframe保存成功消息，刷新BOM数据');

            // 清除备用刷新定时器
            if (window.saveTimeout) {
                clearTimeout(window.saveTimeout);
                window.saveTimeout = null;
                console.log('清除备用刷新定时器');
            }

            console.log('准备刷新，当前模态框数据:', {
                bomId: $('#bomTreeModal').data('current-bom-id'),
                materialId: $('#bomTreeModal').data('current-material-id')
            });

            // 重新加载BOM树形数据
            var bomId = $('#bomTreeModal').data('current-bom-id');
            var materialId = $('#bomTreeModal').data('current-material-id');
            if (bomId && materialId) {
                console.log('开始重新加载BOM数据:', bomId, materialId);
                loadBomTreeData(bomId, materialId);
            } else {
                console.error('无法刷新：缺少BOM ID或物料ID', {bomId, materialId});
            }

            // 显示成功提示
            showModalSuccessMessage('物料保存成功，BOM数据已更新');
        }
        // 处理物料绑定成功的消息
        else if (event.data && event.data.action === 'materialBound' && event.data.success) {
            console.log('收到物料绑定成功消息，刷新BOM数据');
            // 重新加载BOM树形数据
            var bomId = $('#bomTreeModal').data('current-bom-id');
            var materialId = $('#bomTreeModal').data('current-material-id');
            if (bomId && materialId) {
                loadBomTreeData(bomId, materialId);
            }

            // 显示成功提示
            showModalSuccessMessage('物料绑定成功，BOM数据已更新');
        }
        // 处理其他刷新请求
        else if (event.data && event.data.action === 'refresh') {
            console.log('收到刷新请求，刷新BOM数据');
            // 重新加载BOM树形数据
            var bomId = $('#bomTreeModal').data('current-bom-id');
            var materialId = $('#bomTreeModal').data('current-material-id');
            if (bomId && materialId) {
                loadBomTreeData(bomId, materialId);
            }
        }
        else {
            // 消息不匹配刷新条件
        }
    });

    // 显示成功消息的函数
    function showModalSuccessMessage(message) {
        // 创建成功提示
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
                <i class="fas fa-check-circle mr-2"></i>${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        // 在页面顶部显示提示
        $('body').append(alertHtml);

        // 3秒后自动隐藏
        setTimeout(function() {
            $('.alert-success').alert('close');
        }, 3000);
    }

    // 搜索物料的函数
    function searchModalMaterials() {
        var keyword = $('#modalMaterialSearchInput').val().trim();
        var state = $('#modalMaterialStateFilter').val();
        var customerCode = window.currentCustomer ? window.currentCustomer.code : '';

        // 显示加载状态
        $('#modalMaterialBindTableBody').html('<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</td></tr>');

        // 发送AJAX请求搜索物料
        $.ajax({
            url: '{% url "search_materials_for_bind" %}',
            type: 'GET',
            data: {
                'customer': customerCode,
                'keyword': keyword,
                'state': state,
                'page': window.currentMaterialPage || 1,
                'page_size': 10
            },
            success: function(response) {
                if (response.success && response.materials) {
                    renderMaterialBindTable(response.materials);
                    updateMaterialPagination(response.pagination);
                } else {
                    $('#modalMaterialBindTableBody').html('<tr><td colspan="7" class="text-center">暂无数据</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                $('#modalMaterialBindTableBody').html('<tr><td colspan="7" class="text-center text-danger">加载失败: ' + error + '</td></tr>');
            }
        });
    }

    // 渲染物料绑定表格
    function renderMaterialBindTable(materials) {
        var tbody = $('#modalMaterialBindTableBody');
        tbody.empty();

        if (!materials || materials.length === 0) {
            tbody.html('<tr><td colspan="7" class="text-center">暂无数据</td></tr>');
            return;
        }

        materials.forEach(function(material) {
            var row = $('<tr>');
            row.html(`
                <td>${material.no || ''}</td>
                <td>${material.name || ''}</td>
                <td>${material.drawing_no || ''}</td>
                <td>${material.version || ''}</td>
                <td>${material.attr_name || ''}</td>
                <td>${material.state == 1 ? '在用' : '停用'}</td>
                <td>
                    <button class="btn btn-sm btn-primary bind-material-btn" data-material-id="${material.id}" data-material-name="${material.name}">
                        <i class="fas fa-link mr-1"></i>绑定
                    </button>
                </td>
            `);
            tbody.append(row);
        });

        // 绑定按钮点击事件
        $('.bind-material-btn').off('click').on('click', function() {
            var materialId = $(this).data('material-id');
            var materialName = $(this).data('material-name');
            bindMaterialToBom(materialId, materialName, $(this));
        });
    }

    // 绑定物料到BOM
    function bindMaterialToBom(materialId, materialName, buttonElement) {
        if (!window.selectedBomId) {
            alert('请先选择一个BOM节点');
            return;
        }

        if (!confirm(`确定要绑定物料"${materialName}"吗？\n\n注意：这将把选中物料的数据拷贝到当前临时物料中，不会修改原始BOM结构。`)) {
            return;
        }

        // 发送绑定请求 - 使用临时绑定API
        $.ajax({
            url: '{% url "bind_material_to_temp_bom" %}',
            type: 'POST',
            data: {
                'bom_id': window.selectedBomId,
                'material_id': materialId,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            beforeSend: function() {
                buttonElement.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>绑定中...');
            },
            success: function(response) {
                if (response.success) {
                    // 关闭绑定模态框
                    $('#modalBindMaterialModal').modal('hide');

                    // 刷新BOM树数据
                    var bomId = $('#bomTreeModal').data('current-bom-id');
                    var materialId = $('#bomTreeModal').data('current-material-id');
                    if (bomId && materialId) {
                        loadBomTreeData(bomId, materialId);
                    }

                    // 显示成功消息
                    showModalSuccessMessage(`物料"${materialName}"绑定成功，临时物料数据已更新`);
                } else {
                    alert('绑定失败：' + (response.message || '未知错误'));
                    buttonElement.prop('disabled', false).html('<i class="fas fa-link mr-1"></i>绑定');
                }
            },
            error: function(xhr, status, error) {
                alert('绑定失败：' + error);
                buttonElement.prop('disabled', false).html('<i class="fas fa-link mr-1"></i>绑定');
            }
        });
    }

    // 更新分页信息
    function updateMaterialPagination(pagination) {
        if (!pagination) return;

        $('#modalMaterialPaginationInfo').text(`显示 ${pagination.start} - ${pagination.end} 条，共 ${pagination.total} 条`);

        // 这里可以添加分页按钮的逻辑
        // 暂时简化处理
    }

    // 绑定搜索按钮事件
    $('#modalSearchMaterialBtn').off('click').on('click', function() {
        window.currentMaterialPage = 1;
        searchModalMaterials();
    });

    // 绑定搜索输入框回车事件
    $('#modalMaterialSearchInput').off('keypress').on('keypress', function(e) {
        if (e.which == 13) {
            window.currentMaterialPage = 1;
            searchModalMaterials();
        }
    });
});
</script>
{% endblock %}