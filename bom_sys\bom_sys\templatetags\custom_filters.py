from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """获取字典中的值，用于模板中访问字典"""
    return dictionary.get(key)

@register.filter
def split(value, key):
    """
    在模板中分割字符串
    用法: {{ value|split:"/" }}
    返回分割后的列表
    """
    return value.split(key)

@register.filter
def get_index(list_value, index):
    """
    在模板中获取列表中的值
    用法: {{ list_value|get_index:0 }}
    """
    try:
        return list_value[index]
    except (IndexError, TypeError):
        return ''

@register.filter
def filename_from_path(path):
    """
    从路径中提取文件名
    用法: {{ path|filename_from_path }}
    """
    if not path:
        return ''
    
    return path.split('/')[-1] 

@register.filter(name='percentage')
def percentage(value, total):
    """
    计算百分比
    """
    if total == 0:
        return 0
    return int((value / total) * 100)

# 其他自定义过滤器可以在这里添加 