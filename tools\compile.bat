@echo off
setlocal enabledelayedexpansion

echo 编译C#应用程序...
set CSC_PATH=%SystemRoot%\Microsoft.NET\Framework\v4.0.30319\csc.exe

if not exist "%CSC_PATH%" (
    echo 找不到C#编译器，尝试查找其他.NET版本...
    for /f "delims=" %%a in ('dir /b /ad /on "%SystemRoot%\Microsoft.NET\Framework\v4*"') do (
        set CSC_PATH=%SystemRoot%\Microsoft.NET\Framework\%%a\csc.exe
        echo 找到编译器: !CSC_PATH!
        goto :found
    )
    
    echo 找不到.NET Framework 4.x，尝试任意版本...
    for /f "delims=" %%a in ('dir /b /ad /on "%SystemRoot%\Microsoft.NET\Framework\v*"') do (
        set CSC_PATH=%SystemRoot%\Microsoft.NET\Framework\%%a\csc.exe
        if exist "!CSC_PATH!" (
            echo 找到编译器: !CSC_PATH!
            goto :found
        )
    )
    
    echo 错误: 找不到C#编译器，请确保已安装.NET Framework
    exit /b 1
)

:found
echo 使用编译器: %CSC_PATH%
"%CSC_PATH%" /out:OpenFolder.exe OpenFolder.cs

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败!
    exit /b 1
)

echo 编译成功！
echo 正在更新注册表...

rem 获取当前目录的完整路径
for %%i in (.) do set CURRENT_DIR=%%~fi

echo Windows Registry Editor Version 5.00 > set_csharp.reg
echo. >> set_csharp.reg
echo [HKEY_CLASSES_ROOT\universalLink] >> set_csharp.reg
echo "URL Protocol"="" >> set_csharp.reg
echo @="URL:UniversalLink Protocol" >> set_csharp.reg
echo. >> set_csharp.reg
echo [HKEY_CLASSES_ROOT\universalLink\shell\open\command] >> set_csharp.reg
echo @="\"C:\\tools\\OpenFolder.exe\" \"%%1\"" >> set_csharp.reg

echo 注册表文件已创建，请手动导入或运行以下命令:
echo regedit /s set_csharp.reg

echo 完成！ 
pause