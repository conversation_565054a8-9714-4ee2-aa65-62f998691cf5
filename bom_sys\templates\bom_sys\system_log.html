{% extends 'base.html' %}
{% load static %}

{% block title %}日志管理{% endblock %}

{% block content %}
<div class="">
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- 搜索表单 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">日志查询</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form method="get" class="form-inline">
                        <div class="form-group mr-3">
                            <label for="action" class="mr-2">操作类型:</label>
                            <select name="action" id="action" class="form-control">
                                <option value="">全部</option>
                                {% for choice in action_choices %}
                                <option value="{{ choice }}" {% if query_params.action == choice %}selected{% endif %}>{{ choice }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="form-group mr-3">
                            <label for="module" class="mr-2">模块类型:</label>
                            <select name="module" id="module" class="form-control">
                                <option value="">全部</option>
                                {% for choice in module_choices %}
                                <option value="{{ choice }}" {% if query_params.module == choice %}selected{% endif %}>{{ choice }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="form-group mr-3">
                            <label for="user_name" class="mr-2">用户名:</label>
                            <input type="text" name="user_name" id="user_name" class="form-control" value="{{ query_params.user_name }}" placeholder="输入用户名">
                        </div>
                        
                        <div class="form-group mr-3">
                            <label for="start_date" class="mr-2">开始日期:</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="{{ query_params.start_date }}">
                        </div>
                        
                        <div class="form-group mr-3">
                            <label for="end_date" class="mr-2">结束日期:</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="{{ query_params.end_date }}">
                        </div>
                        
                        <div class="form-group mr-3">
                            <label for="page_size" class="mr-2">每页显示:</label>
                            <select name="page_size" id="page_size" class="form-control">
                                <option value="10" {% if query_params.page_size == 10 %}selected{% endif %}>10</option>
                                <option value="20" {% if query_params.page_size == 20 %}selected{% endif %}>20</option>
                                <option value="50" {% if query_params.page_size == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if query_params.page_size == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary mr-2">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        
                        <a href="{% url 'system_log' %}" class="btn btn-secondary">
                            <i class="fas fa-refresh"></i> 重置
                        </a>
                    </form>
                </div>
            </div>

            <!-- 日志清除 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">日志清除</h3>
                </div>
                <div class="card-body">
                    <form method="post" onsubmit="return confirm('确定要清除指定天数前的日志吗？此操作不可恢复！');">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="clear_logs">
                        <div class="form-inline">
                            <div class="form-group mr-3">
                                <label for="days" class="mr-2">清除</label>
                                <input type="number" name="days" id="days" class="form-control" value="30" min="1" max="365" style="width: 80px;">
                                <label class="ml-2">天前的日志</label>
                            </div>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> 清除日志
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 日志列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">日志列表 (共 {{ total_logs }} 条记录)</h3>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>操作时间</th>
                                <th>用户名</th>
                                <th>操作类型</th>
                                <th>模块类型</th>
                                <th>目标对象</th>
                                <th>操作描述</th>
                                <th>IP地址</th>
                                <th>详细信息</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr>
                                <td>{{ log.log_id }}</td>
                                <td>{{ log.log_time|date:"Y-m-d H:i:s" }}</td>
                                <td>{{ log.log_user_name|default:"未知用户" }}</td>
                                <td>
                                    <span class="badge badge-info">{{ log.log_action|default:"未知操作" }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-secondary">{{ log.log_module|default:"未知模块" }}</span>
                                </td>
                                <td>
                                    {% if log.log_target_name %}
                                        {{ log.log_target_name }}
                                        {% if log.log_target_id %}(ID: {{ log.log_target_id }}){% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ log.log_description|default:"-" }}</td>
                                <td>{{ log.log_ip|default:"-" }}</td>
                                <td>
                                    {% if log.log_details %}
                                        <button type="button" class="btn btn-sm btn-outline-info" data-toggle="modal" data-target="#detailModal{{ log.log_id }}">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="9" class="text-center">暂无日志记录</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if logs.has_other_pages %}
                <div class="card-footer clearfix">
                    <ul class="pagination pagination-sm m-0 float-right">
                        {% if logs.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ logs.previous_page_number }}&action={{ query_params.action }}&module={{ query_params.module }}&user_name={{ query_params.user_name }}&start_date={{ query_params.start_date }}&end_date={{ query_params.end_date }}&page_size={{ query_params.page_size }}">上一页</a>
                            </li>
                        {% endif %}
                        
                        {% for num in logs.paginator.page_range %}
                            {% if logs.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > logs.number|add:'-3' and num < logs.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}&action={{ query_params.action }}&module={{ query_params.module }}&user_name={{ query_params.user_name }}&start_date={{ query_params.start_date }}&end_date={{ query_params.end_date }}&page_size={{ query_params.page_size }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if logs.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ logs.next_page_number }}&action={{ query_params.action }}&module={{ query_params.module }}&user_name={{ query_params.user_name }}&start_date={{ query_params.start_date }}&end_date={{ query_params.end_date }}&page_size={{ query_params.page_size }}">下一页</a>
                            </li>
                        {% endif %}
                    </ul>
                    
                    <div class="float-left">
                        显示第 {{ logs.start_index }} 到 {{ logs.end_index }} 条记录，共 {{ logs.paginator.count }} 条
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </section>
</div>

<!-- 详细信息模态框 -->
{% for log in logs %}
{% if log.log_details %}
<div class="modal fade" id="detailModal{{ log.log_id }}" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">日志详细信息</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <pre>{{ log.log_details }}</pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}
