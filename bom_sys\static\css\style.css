/* 全局样式 */
body {
    font-family: "Microsoft YaHei", "Segoe UI", <PERSON>l, sans-serif;
}

/* 登录页样式 */
.login-page {
    background: #f4f6f9;
}

.login-box {
    margin-top: 10vh;
}

.login-logo a {
    color: #007bff;
    font-weight: 600;
}

/* 侧边栏样式 */
.sidebar-dark-primary {
    background-color: #343a40;
}

.sidebar-dark-primary .brand-link {
    border-bottom: 1px solid #4b545c;
}

/* 内容区域样式 */
.content-wrapper {

    background-color: #f4f6f9;
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
}

/* 卡片样式 */
.card {
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

/* 按钮样式 */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* 表单样式 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 标签页样式 */
#nav-tabs {
    margin-right: 10px;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    max-width: 90%;
    height: 40px;
    align-items: center;
}

#nav-tabs::-webkit-scrollbar {
    height: 3px;
}

#nav-tabs::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
}

#nav-tabs .nav-item {
    white-space: nowrap;
    margin: 0;
}

#nav-tabs .nav-link {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #f8f9fa;
    color: #495057;
    margin-right: 2px;
    position: relative;
    white-space: nowrap;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.2s;
    height: 38px;
    display: flex;
    align-items: center;
}

#nav-tabs .nav-link.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
    border-bottom: 2px solid #0056b3;
    z-index: 2;
}

#nav-tabs .nav-link:not(.active):hover {
    background-color: #e9ecef;
}

#nav-tabs .tab-close {
    margin-left: 8px;
    font-size: 0.8rem;
    padding: 0;
    background: transparent;
    border: 0;
    opacity: 0.5;
    color: inherit;
}

#nav-tabs .nav-link.active .tab-close {
    opacity: 0.8;
    color: white;
}

#nav-tabs .tab-close:hover {
    opacity: 1;
}

.tab-content {
    height: calc(100vh - 57px);
    overflow: hidden;
    border-top: 1px solid #dee2e6;
}

.tab-pane {
    height: 100%;
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane iframe {
    border: none;
    height: 100%;
    width: 100%;
}

/* 修复嵌套问题 */
.tab-iframe {
    width: 100%;
    
    border: none !important;
    overflow: auto;
}

/* 确保iframe内容不会嵌套显示侧边栏 */
.tab-pane.active {
    position: relative;
    z-index: 1;
}

.main-sidebar {
    z-index: 100;
}

/* 滑块开关样式 */
.switch-container {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.switch-label {
    margin-right: 10px;
    font-weight: 500;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-bottom: 0;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #28a745;
}

input:focus + .slider {
    box-shadow: 0 0 1px #28a745;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider-text {
    margin-left: 10px;
    font-size: 14px;
}

/* 存货属性标签页样式 */
#inventory .form-group {
    margin-bottom: 20px;
}

#inventory .switch-container {
    display: flex;
    align-items: center;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .login-box {
        width: 90%;
    }
    
    #nav-tabs {
        max-width: 50%;
    }
} 

@media (max-width: 768px) {
    .switch-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .slider-text {
        margin-left: 0;
        margin-top: 5px;
    }
} 

/* 文件链接样式 */
.file-link {
    color: #333;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.2s ease;
}

.file-link:hover {
    color: #007bff;
    text-decoration: underline;
}

/* 文件列表项样式 */
.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 5px;
    transition: background-color 0.2s ease;
}

.file-item:hover {
    background-color: #e9ecef;
}

/* 保存遮罩层样式 */
.saving-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    display: none;
}

.saving-content {
    background-color: #fff;
    padding: 30px 50px;
    border-radius: 5px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.saving-content p {
    margin-top: 15px;
    font-size: 16px;
    color: #333;
}

.saving-content i {
    color: #17a2b8;
} 

/* BOM树形结构样式 */
.tree-container {
    margin-top: 15px;
    overflow-y: auto;
    max-height: calc(100vh - 200px);
}

.custom-tree {
    padding: 10px;
}

.tree-node {
    margin: 5px 0;
    border-radius: 4px;
    transition: background-color 0.2s;
    cursor: pointer;
}

.tree-node:hover {
    background-color: #f0f0f0;
}

.tree-node.selected {
    background-color: #e9f2fd;
    border-left: 3px solid #007bff;
}

.tree-node.highlighted {
    background-color: #f8f9fa;
}

.node-content {
    padding: 8px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.node-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.node-text {
    margin-left: 5px;
}

.node-children {
    margin-left: 20px;
    border-left: 1px dashed #ccc;
    padding-left: 10px;
}

/* 支持多级树形结构 */
.tree-node.level-3 .node-text,
.tree-node.level-4 .node-text,
.tree-node.level-5 .node-text,
.tree-node.level-6 .node-text,
.tree-node.level-7 .node-text,
.tree-node.level-8 .node-text,
.tree-node.level-9 .node-text,
.tree-node.level-10 .node-text {
    margin-left: 5px;
}

/* 展开/折叠图标样式 */
.expand-icon {
    width: 20px;
    height: 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s ease;
    color: #6c757d;
    margin-right: 5px;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

/* 全部展开/折叠按钮样式 */
#expandAll, #collapseAll {
    transition: all 0.2s ease;
}

#expandAll:hover, #collapseAll:hover {
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

#expandAll:focus, #collapseAll:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

/* BOM列表页面布局 */
.bom-container {
    height: calc(100vh - 60px);
    margin-top: 15px;
}

/* 左侧面板样式 */
.left-panel {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    height: 100%;
    transition: all 0.3s ease;
    overflow: hidden;
}

.left-panel.collapsed {
    padding: 15px 5px;
}

.left-panel .toolbar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.left-panel .breadcrumb-nav {
    margin-bottom: 15px;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.left-panel .search-box {
    margin-bottom: 15px;
}

/* 右侧面板样式 */
.right-panel {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    height: 100%;
    transition: all 0.3s ease;
    overflow: auto;
}

/* 浮动切换按钮 */
#floatingToggle {
    position: fixed;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #fff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    display: none;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
    border: 1px solid #ddd;
}

#floatingToggle.show {
    display: flex !important;
}

#floatingToggle:hover {
    background-color: #f8f9fa;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    transform: translateY(-50%) scale(1.1);
}

#floatingToggle i {
    font-size: 18px;
    color: #007bff;
} 

/* 通知浮窗样式 */
.notification-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    width: 350px;
}

.notification {
    margin-bottom: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    animation: slide-in 0.3s ease-out;
    opacity: 0.95;
}

.notification:hover {
    opacity: 1;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

@keyframes slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 0.95;
    }
}

@keyframes fade-out {
    from {
        transform: translateX(0);
        opacity: 0.95;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.fade-out {
    animation: fade-out 0.3s ease-in forwards;
}

/* 图片预览样式 */
.image-preview-container {
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-preview-container img {
    max-width: 100%;
    height: auto;
}

.image-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.image-preview-overlay.active {
    opacity: 1;
    visibility: visible;
}

.image-preview-overlay img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.image-preview-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.image-preview-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
} 