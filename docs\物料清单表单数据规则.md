
# 物料清单总表表单数据规则

## 物料属性部分：

- 调整客户图号和客户名称的位置 当前版本默认为空
- 从编辑模式切换回新增模式后，清空所有表单
- 新增版本时，图纸信息相关表单需要清空

- 客户图号：输入框，如填写了系统中存在的图号和客户，自动填充相关信息，进入（编辑/新增版本）模式，如填写了系统中不存在的图号，则进入新增模式(必填) （不能输入特殊符号，文件夹不能识别的）
- 客户名称：选择框,从客户信息模块中录入的客户信息模糊查询并快速选择(必填)
- 物料属性分类：选择框,从数据字典中选择物料属性分类(必填)
- 物料编码：文本框，根据选择的客户和物料属性分类自动生成一个默认的编码（物料属性分类码[2位]+客户编码[3位]+自增长编号[4位]），用户也可以自行修改，但不能重复(必填) --中缀规则自定义
- 物料名称（存货名称）：文本框 (必填) 
- 版本：文本框（默认从A-Z排）(必填) （支持数字的字母两种模式，01-99 A-Z）
   - 如果图号已存在，则进入（编辑/新增版本）模式，此时版本号默认+1，用户可以修改版本号（也只能在01-99 A-Z范围内），但不能和已有的重复
   - 如果图号不存在，则进入新增模式，版本号默认为空 ，用户可以修改版本号

- 材质：文本框  (非必填) 
- 材料规格：文本框  (非必填) 
- 半成品状态码：选择框，从数据字典中选择半成品状态码(非必填)
- 损耗率：数字框，0-100%，精确到小数点后3位（非必填）
- 数量：数字框，大于等于0的整数（非必填）
- 生产数量：数字框，大于等于0的整数（非必填）
- 零件用量：数字框，大于等于0的整数  (非必填) 
- 计量单位：选择框，从数据字典中选择计量单位(必填)
- 生产车间：选择框，从数据字典中选择生产车间（非必填）
- 图片：上传单张图片：图片格式为jpg、jpeg、png、gif，（非必填）
- 负责人：文本框，默认填充当前登录系统的用户名称，也可自由填写（非必填）
- 上传日期：日期框，默认当前日期，也可自由填写（非必填）

## 图纸信息：（文件会同步到SMB服务器，同步的地址，文件夹的存放规则，还需要确定）
- 客户图纸：PDF单文件，支持在线预览，文件同步到SMB服务器，（非必填）
- 成品图纸：PDF单文件，支持在线预览，文件同步到SMB服务器，（非必填）
- 工艺图纸：PDF单文件，支持在线预览，文件同步到SMB服务器，（非必填）
- 检测单：多文件上传，没有文件格式限制，文件同步到SMB服务器，（非必填）
- 资料文件夹：多文件上传，没有文件格式限制，文件同步到SMB服务器，（非必填）

## 存货属性 修改成滑块开关模式
- 客供：是/否  (必填)
- 外购：是/否  (必填)
- 销售：是/否  (必填)
- 自制：是/否  (必填)
- 委外：是/否  (必填)
- 生产耗用：是/否  (必填)
- 批号管理：是/否  (必填)
- 下料尺寸：文本框  (非必填) 

## 供应商
- 铸造：文本框  (非必填) 
- 机加：文本框  (非必填) 
- 钣金：文本框  (非必填) 
- 采购：文本框  (非必填) 

## 工艺
- 工艺序号：根据工艺路线表查询，如果存在，则自动填充，否则为空  (非必填) 
- 工艺1-N：选择框 从数据字典中选择工序字典获取选择内容 (非必填) 

## 其他字段
- 与BOM关联的相关信息
- 物料版本状态：（停用、启用），如果上传了新的版本，旧版本自动变为停用，新的版本默认为启用

## 相关说明
- 上面提到的数据字典都是可以配置的，配置完成后，在选择框选择使用，比如：计量单位、半成品状态码、物料属性分类等等。
- 目前已知的选择框有：客户信息选择，工艺路线选择，物料属性分类，半成品状态码，计量单位，生产车间，工序。（如果有其他需要选择的，请在此文档中补充）
- 文档中提到的输入方式，输入验证，是否必填等信息都是个人根据提供材料的理解，如有不正确的地方，请在此文档中补充或联系开发人员进行修改。
- 文档中提到的文件上传，同步到SMB服务器，文件夹的存放规则，还需要确定。
- 文档中的字段需要增加、删除，或者修改顺序，或者字段所属的类别不正确，请联系开发人员进行修改。


## 图纸信息相关

修改图纸信息相关的文件存放规则
1.客户图纸:{smb_base}/(01) 客户图纸/({Material_Customer}) {Material_CustomerName}/(Material_Attr) {Material_AttrName}/{Material_No后四位} {Material_DrawingNo} {Material_Name} {Material_Version}.pdf 
2.成品图纸：{smb_base}/(02) 成品图纸/{Material_No} {Material_Name} {Material_Version}.pdf
3.工艺图纸：{smb_base}/(03) 工艺图纸/{Material_No} {Material_Name} {Material_Version}-{两位顺序码不足的补零}.pdf
4.检测表单：{smb_base}/(04) 检测表单/{Material_No} {Material_Name} {Material_Version}-{两位顺序码不足的补零}.xlsx
5.资料文件夹：{smb_base}/(05) 资料文件夹/({Material_Customer}) {Material_CustomerName}/(Material_Attr) {Material_AttrName}/{Material_No后四位} {Material_DrawingNo} {Material_Name}/{Material_Version}/

1.在添加和编辑保存物料时，客户图纸、成品图纸、工艺图纸、检测表单按照上述文件命名规则存放。检测表单与工艺图纸的命名规则相同，只是文件后缀不同。
2.在添加和编辑保存物料时，资料文件夹只需要保存文件路径，不需要保存文件
3.两位顺序码说明：保存文件时，如果同目录下存在{Material_No} {Material_Name} {Material_Version}都相同，则找到同名的最大顺序码+1，如果{Material_No} {Material_Name} {Material_Version}都不同，则顺序码为01。
4.如果不存在相关的文件夹，程序自动创建文件。
5.删除物料时不删除文件，只删除数据库记录。

## BOM导入相关
1.BOM是否允许手动单条录入。如果允许，交互方式如何？从物料清单中选择？还是重新填写表单。 手动填写表单新增，或从已有的清单列表中选择
   根据客户名称和图号查询数据库中的物料编码，如果有，填充带入，如果没有，根据图号，客户名称，分类最大+1.
   版本：如果有相同的版本，从数据库中带出，如果没有相同的版本，从excel中带入

2.BOM导入规则：根据excel客户零件图号和存货分类、版本查找是否存在相同的数据（客户名称、分类、客户图号）
   2.1 如果有相同：则时候数据库中的数据带入相关表单填写填充到表单中
   2.2 如果没有，则创建新的数据，并在物料清单中添加对应的物料，同时填充的表单中（这条数据需要用特殊颜色标记，以代表该条数据是新生成的）
   2.3 点击保存后，保存整个BOM的树形结构，并将BOM的物料信息保存到物料清单表中。
   2.4 点击取消，删除整个BOM的树形结构，并删除物料清单中对应的物料。（需要讨论是否需要保留物料清单中的物料数据）
   
3.导入excel,上传BOM前，必填：1、客户名称

单独做一个物料查询界面，查询列包含（物料编码	客户图号	客户名称 版本 客户图纸 生产图纸 工艺图纸 检测单 资料文件夹）

根据客户名称和图号查询数据库中的物料编码，如果有，填充带入，如果没有，根据图号，客户名称，分类最大+1.																	
版本：如果有相同的版本，从数据库中带出，如果没有相同的版本，从excel中带入										

导入规则：
1.物料名称中特殊符号的处理。 保存数据时，物料名称还是带特殊符号，但是创建文件或文件夹时，过滤特殊符号 ok
2.同一张excel表中BOM结构的重复处理方式
3.excel表中和数据库中的重复处理方式
4.bom层级和已有bom层级的关联问题 始终根据最顶级的客户+图号+版本 是否相同来判断是否覆盖
5.版本分类为空的特殊处理 如果版本为空。默认为A 如果没有图号、属性分类，不允许导入 ok
6.BOM导入加入数量 ok


导入excel规则
读取excel表的内容：分别写入到bom表和 material表
excel和表的对应关系如下：
列B-I：对应bom表的level关系。B：0，C：1，D：2，E：3，F：4，G：5，H：6，I：7，按照规则。excel中的B列里只会有0或者为空，C列里只会有1或者为空，D列里只会有2或者为空，E列里只会有3或者为空，F列里只会有4或者为空，G列里只会有5或者为空，H列里只会有6或者为空，I列里只会有7或者为空。excel中没有bom_Pid的列，需要依靠excel中的顺序来判断每行的pid是谁。
列J：物料编码。对应material表的Material_No。根据客户名称和图号和版本查询数据库中的物料编码，如果有，则其他所有字段都不需要从excel中读取，直接从数据库中读取。如果没有，则需要从excel中读取，如果excel中也没有该字段的内容，则需要生成一个新的编码。
列M：图片。对应material表的material_Pic。
列N：物料名称。对应material表的Material_Name。
列O：材质。对应material表的Material_Quality。
列P：规格。对应material表的Material_Spec。
列Q：版本。对应material表的Material_Version。
列T：计量单位。对应material表的Material_Unit。
列X：物料属性分类。对应material表的Material_AttrName。同时需要去propertiestype表查询propertiesType_Name=Material_AttrName。如果找不到，提示该条数据有误，没有找到对应的属性分类，如果找的到。Material_Attr=propertiesType_Code
列Y(客供),Z（外购）,AA（销售）,AB（自制）,AC（生产耗用）,AE（批号管理）,AF（下料尺寸）。对应material表的Material_inventory中的json格式。
如果列中的内容是“Y”，则对应的值为“是”，否则对应的值为“否”。
{"customer_supply": "是", "outsourcing": "否", "sales": "否", "self_made": "否", "subcontract": "否", "production_consumption": "否", "batch_management": "否", "cutting_size": "下料尺寸"}
列AG：铸造。对应material表的Material_Supp_Casting。
列AH：机加。对应material表的Material_Supp_Machining。
列AI：钣金。对应material表的Material_Supp_SheetMetal。
列AJ：采购。对应material表的Material_Supp_Purchase。
列AK：采购负责人。对应material表的Material_Supp_Purchase_Manager。
列AM-BP：工序清单，对应material表的Material_Process表。工序清单：顺序以|分割 如“机加|精车OP1|精车OP2”

通过excel导入的数据需要填充的额外字段
bom表
1.bom_Pid：根据excel中的B-I列里的数字，找到对应的pid。
2.bom_State=2：表示临时数据
3.bom_Material:关联Material表的Material_Id,所以要先创建Material表，然后再创建bom表。

Material表
1.Material_tempState: 1：新增，2：修改
根据 根据客户名称和图号查询数据库中的物料编码，如果没有，则为1：新增，如果有，根据物料编码和版本查询数据库中的物料，如果没有，则为1：新增，如果有，则为2：修改。
2.Material_tempTrueId: 当Material_tempState为修改的时候。需要将原来的数据拷贝一份新增，区别就是Material_tempTrueId为原来拷贝的id。
3.Material_tempUserId：当前登录用户的User_Id


## 20250719讨论内容：
1.导入时，还是需要增加生产数量的字段到BOM表中（ok）

BOM清单（新）
2.增加列：显示存货属性的全部列（ok）

工序明细需要一个单独的表维护（ok）
设备单独加一张表维护 （ok）

BOM子件版本变更后，需要判断其他涉及到物料是否需要变更

## BOM导出相关

## 模板一 规则

### 第一个sheet“物料清单”规则，导出所有选中bom_Level为0的顶级物料数据
第一列：父件编码（Material_No）
第二列：父件名称（Material_Name）
第三列：规格型号（Material_Spec）
第四列：下料尺寸（Material_Size）
第五列：版本（Material_Version）
第六列：工艺路线编码（空）
第七列：工艺路线（空）
第八列：计量单位（Material_Unit）
第九列：生产数量（Material_ProduceCount）
第十列：生产车间编码（空）
第十一列：生产车间（空）
第十二列：预入仓库编码（空）
第十三列：预入仓库（空）
第十四列：默认BOM（默认为1）
第十五列：成品率（默认为100%）
第十六列：停用（如果Material_State为2，填充为1，如果Material_State为1，填充为0）
第十七列：创建时间（Material_UpTime）

### 第二个sheet“子件明细”规则，根据所有选中的顶级物料，递归查询出选中顶级物料下面的子项
第一列：版本号（根据bom_Pid查找对应的bom数据中bom_Material，然后根据bom_Material查找Material表，找到对应的Material_Version）
第二列：父件编码（根据bom_Pid查找对应的bom数据中bom_Material，然后根据bom_Material查找Material表，找到对应的Material_No）
第三列：子件编码（Material_No）
第四列：子件名称（Material_Name）
第五列：规格型号（Material_Spec）
第六列：子件BOM（Material_Version）
第七列：子件默认BOM（默认为0）
第八列：计量单位（Material_Unit）
第九列：生产数量（bom_ProduceCount）
第十列：需用数量（bom_PartCount）
第十十一列：损耗率（Bom_Lossrate）
第十十二列：预出仓库编码（空）
第十十三列：预出仓库（空）
第十十四列：领料工序编码（空）
第十十五列：领料工序（空）
第十十六列：备注（空）
第十十七列：表体备注（空）


### 第三个sheet“工序明细”规则，根据所有选中的顶级物料、以及递归查询出选中顶级物料下面的子项
把下列数据提取分割“|”拆分成多条数据一一对应，如果这几个拆分的数量不一致，优先以Material_Processroute的数量为准
如果一条物料的 Material_Processroute 为“机加|精车OP1|精车OP2”，那么就会有3条“工序明细”的数据

   工序清单【名称】：Material_Processroute 顺序以|分割 如“机加|精车OP1|精车OP2”
	工序详情【工时】：Material_ProcessrouteTimes 顺序以|分割 如“1.5小时|2.0分钟|1小时” 与工序清单数量一致	
   工序详情【设备】：Material_ProcessrouteDevice 顺序以|分割 如“1|2|3” 与工序清单数量一致			
   工序详情【工价】：Material_ProcessroutePrice 顺序以|分割 如“1|2|3” 与工序清单数量一致			
   工序详情【加工方式】：Material_ProcessrouteType 顺序以|分割 如“自制|委外” 与工序清单数量一致
   工序详情【委外供应商】：Material_ProcessrouteOutSupplier 顺序以|分割 如“1|2|3” 与工序清单数量一致
   工序详情【委外单价】：Material_ProcessrouteOutPrice 顺序以|分割 如“1|2|3” 与工序清单数量一致
   工序详情【关键工序】：Material_ProcessrouteImportant 顺序以|分割 如“1|0|1” 与工序清单数量一致	

第一列：版本号（根据bom_Pid查找对应的bom数据中bom_Material，然后根据bom_Material查找Material表，找到对应的Material_Version）
第二列：父件编码（根据bom_Pid查找对应的bom数据中bom_Material，然后根据bom_Material查找Material表，找到对应的Material_No）
第三列：加工顺序（根据Material_Processroute拆分排序 例如 机加|精车OP1|精车OP2 拆分成3条数据 机加就是1 精车OP1就是2 精车OP2就是3）
第四列：工序编码 （根据Material_Processroute拆分 通过get_dictionary_items('processes')，找到对应的DataDictionary_Code， 例如 机加|精车OP1|精车OP2 拆分成3条数据。第一条 机加，通过get_dictionary_items('processes')返回的结果查看DataDictionary_Name="机加"的DataDictionary_Code，如果没有，则为空）
第五列：工序名称 （根据Material_Processroute拆分 例如 机加|精车OP1|精车OP2，生成的三条数据的工序名称分别为 机加 精车OP1 精车OP2）
第六列：标准工时（根据Material_ProcessrouteTimes拆分 例如 1.5小时|2.0分钟|1小时，生成的三条数据的工序名称分别为 1.5小时 2.0分钟 1小时）
第七列：设备编码（根据Material_ProcessrouteDevice拆分 例如 1|2|3，分别根据Equipment_Id=1 =2 =3查找 Equipment表，找到对应的Equipment_No，如果没有，则为空）
第八列：设备（根据Material_ProcessrouteDevice拆分 例如 1|2|3，分别根据Equipment_Id=1 =2 =3查找 Equipment表，找到对应的Equipment_Name，如果没有，则为空）
第九列：工价 （根据Material_ProcessroutePrice拆分 例如 1元|2元|3元，生成的三条数据的工序名称分别为 1元 2元 3元）
第十列：加工方式 Material_ProcessrouteType 顺序以|分割 如“自制|委外” 
第十十一列：委外供应商编码 为空
第十十二列：委外供应商 Material_ProcessrouteOutSupplier 顺序以|分割
第十十三列：委外单价 Material_ProcessrouteOutPrice 顺序以|分割
第十十四列：关键工序 Material_ProcessrouteImportant 顺序以|分割 如“1|0|1”


## 模板二 规则
### 只有一个sheet，根据所有选中的顶级物料、以及递归查询出选中顶级物料下面的子项，需要注意导入的bom层级顺序。比如把顶级物料下的所以子物料遍历循环导出后，再进行下一个顶级物料的循环

第1列：序号，自增排序，从1开始
第2-9列：bom层级，第2列如果是0就是顶级，第3列如何是1就是1级，第4列如果是2就是2级，以此类推，2-9列只会有一列会有数值
第10列：存货编码（Material_No）
第11列：客户图号（Material_DrawingNo）
第12列：数量（bom_Num）
第13列：图片（Material_Img，这里的图片存的是路径，需要把对应路径的图片下载下来放在对应的单元格中）
第14列：物料名称（Material_Name）
第15列：材质（Material_Quality）
第16列：规格（Material_Spec）
第17列：版本（Material_Version）
第18列：生产数量（bom_ProduceCount）
第19列：零件用量（bom_PartCount）
第20列：计量单位（Material_Unit）
第21列：损耗率（bom_LossRate）
第22列：客户（Material_CustomerName）
第23列：自制时填写（为空）
第24列：物料属性（Material_AttrName）
第25列：客供（Material_inventory中的json里的customer_supply属性）
第26列：外购（Material_inventory中的json里的outsourcing属性）
第27列：销售（Material_inventory中的json里的sales属性）
第28列：自制（Material_inventory中的json里的self_made属性）
第29列：生产耗用（Material_inventory中的json里的production_consumption属性）
第30列：虚拟件（Material_inventory中的json里的virtual_item属性）
第31列：批号管理（Material_inventory中的json里的batch_management属性）
第32列：下料尺寸（Material_inventory中的json里的cutting_size属性）
第33列：铸造（Material_Supp_Casting）
第34列：机加（Material_Supp_Machining）
第35列：钣金（Material_Supp_SheetMetal）
第36列：采购（Material_Supp_Purchase）
第37列：采购负责人(Material_Supp_ProchaseManager)
第38列：工艺路线编号（为空）
第（AM-BP）列：工艺路线详情 将（Material_Processroute拆分，分别填充到对应列，如“机加|精车OP1|精车OP2”，拆分后，机加填充到AM，精车OP1填充到AN，精车OP2填充到AO）


## 20250721讨论内容：
导入结构中增加虚拟件字段
版本：如果虚拟件是Y或自制是Y。一定要有版本号，当前版本设置
如果虚拟件和自制不是Y。可以没有版本号？

## 20250724待确认问题：
新增版本：关联原版本的更新问题

## 20250726
已经完成修改的功能：

BOM清单相关界面修改
1. 固定 序号	层级	物料编码	客户图号	物料名称	数量 横向滚动条只滚动后面的部分，前面的部分始终在前面
2. 点击 层级单元格 可以展开，收缩该层级下的子件
3. 在BOM清单列表增加了属性筛选的功能 通过勾选 客供	外购	销售	自制	委外	生产耗用	批号管理	虚拟件 筛选数据
4. 在物料清单详细信息列表页面。右键点击移动节点，选择目标父节点的列表中 增加了物料编码、客户图号 的显示，方便用户更好的区分，同时支持 物料编码、物料名称、客户图号 的模糊查询选择

工序相关修改
1. 工序列表前加入序号，且序号会根据工序列表顺序的变化为重新编号，工价 委外单价 保存后保留两位小数
2. 选择工艺路线的时候，根据工艺路线名称模糊查询带出工序列表（之前是通过工艺编码精准查询带出）
3. 增加单个工艺编码的概念：
   3.1 工序名称只能选择数据字典内存在的工序，且通过模糊查询选择工序（之前是允许用户自己填写工序名称），绑定工艺名称的同时，绑定工艺编码
   3.2 在导入excel时，根据工艺名称查找对应的工艺编码，如果存在，工艺编码和工艺名称都写入数据。如果不存在。只写入工艺名称

界面排版相关修改
1. 物料清单列表的编辑、查看、和单物料录入参考BOM详情中的右侧滑入的方式，不采用现在的页面跳转方式。
2. 物料清单和BOM列表的右侧滑入交互统一使用双击展开，优化了原本单机容易导致误操作的情况。

导入excel相关修改
1. 在导入excel按钮旁边增加了一个下载导入模板的按钮，点击后下载一个示例的excel模板，模板中包含所有需要导入的字段和示例数据。


待开发的功能：

1. 增加一个“缺BOM物料”的页面模块（部分细节需完善）
2. 现在的“负责人”字段移到【供应商】标签页中重命名成“采购负责人”，增加一个“技术负责人”的字段，在现在“负责人”的位置显示，默认填写为当前登录系统的用户名称，也可自由填写。导入excel模板数据填充的是“采购负责人”
3. excel导出时的零件用量用excel公式替代（不使用实际的数据带出）