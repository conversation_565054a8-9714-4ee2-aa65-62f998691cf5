{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}BOM清单管理{% endblock %}

{% block body_class %}hold-transition sidebar-mini layout-fixed{% endblock %}

{% block content %}
<div>
    {% csrf_token %}
    
        
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row bom-container">
                <!-- 左侧面板 -->
                <div class="col-md-4 left-panel">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="d-flex align-items-center">
                            <button class="btn btn-outline-secondary btn-sm me-2" id="togglePanel" title="收缩/展开面板" style='display:none;'>
                                <i class="fas fa-chevron-left" id="toggleIcon"></i>
                            </button>
                            <button class="btn btn-primary btn-sm" id="addMaterial">
                                <i class="fas fa-plus"></i>
                                <span class="btn-text">新增子件</span>
                            </button>
                            <button class="btn btn-primary btn-sm ml-2" id="addMaterialParent">
                                <i class="fas fa-plus"></i>
                                <span class="btn-text">新增父件</span>
                            </button>
                           
                            <a href="{% url 'import_bom' %}" class="btn btn-success btn-sm ml-2" id="exportBom">
                                <i class="fas fa-file-import"></i>
                                <span class="btn-text">导入BOM</span>
                            </a>
                            
                        </div>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm" id="expandAll" style='display:none;'>
                                <i class="fas fa-expand-arrows-alt"></i>
                                <span class="btn-text">全部展开</span>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm ml-1" id="collapseAll" style='display:none;'>
                                <i class="fas fa-compress-arrows-alt"></i>
                                <span class="btn-text">全部折叠</span>
                            </button>
                        </div>
                    </div>

                    <!-- 面包屑导航 -->
                    <div class="breadcrumb-nav">
                        <div id="breadcrumbNav" class="breadcrumb-content">
                            <!-- 面包屑将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    
                    <!-- 搜索框 -->
                    <div class="search-box">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="输入客户名称、图号、物料编码等进行搜索...">
                            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 树形列表 -->
                    <div class="tree-container">
                        <div id="bomTree" class="custom-tree">
                            <!-- 树形结构将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 右侧面板 -->
                <div class="col-md-8 right-panel">
                    <div id="emptyState" class="empty-state">
                        <i class="fas fa-mouse-pointer"></i>
                        <h5>请选择左侧树形列表中的物料</h5>
                        <p class="text-muted">点击左侧BOM树形列表中的任意物料节点查看详细信息</p>
                    </div>
                    
                    <!-- 将详细表单替换为iframe容器 -->
                    <div id="materialFrame" style="display: none; height: 100%; width: 100%;">
                        <!-- 操作按钮区 -->
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                            <h5 class="mb-0">物料详细信息</h5>
                            <div>
                                <button class="btn btn-outline-primary btn-sm" id="editMaterial">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                                <button class="btn btn-outline-success btn-sm ml-2" id="bindMaterial">
                                    <i class="fas fa-link mr-1"></i>绑定物料
                                </button>
                                <button class="btn btn-primary btn-sm ml-2" id="saveMaterial">
                                    <i class="fas fa-save mr-1"></i>保存
                                </button>
                                <button class="btn btn-info btn-sm ml-2" id="newVersionMaterial">
                                    <i class="fas fa-code-branch mr-1"></i>新增版本
                                </button>
                                <button class="btn btn-outline-danger btn-sm ml-2" id="deleteMaterial">
                                    <i class="fas fa-trash mr-1"></i>删除
                                </button>
                                <button class="btn btn-outline-secondary btn-sm ml-2" id="refreshMaterial">
                                    <i class="fas fa-sync-alt mr-1"></i>刷新
                                </button>
                            </div>
                        </div>
                        
                        <!-- iframe 嵌入 -->
                        <iframe id="materialIframe" src="" style="width: 100%; height: calc(100% - 50px); border: none;" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- 浮动切换按钮 -->
<div id="floatingToggle" style="display: none;">
    <i class="fas fa-chevron-right"></i>
</div>

<!-- 物料绑定模态框 -->
<div class="modal fade" id="bindMaterialModal" tabindex="-1" role="dialog" aria-labelledby="bindMaterialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bindMaterialModalLabel">绑定物料</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- 搜索框 -->
                <div class="form-group">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="materialSearchInput" placeholder="输入物料编码、客户图号或物料名称进行搜索...">
                        <div class="input-group-append">
                            <button class="btn btn-outline-primary" type="button" id="materialSearchBtn">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 物料列表 -->
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="materialTable">
                        <thead>
                            <tr>
                                <th>物料编码</th>
                                <th>客户图号</th>
                                <th>物料名称</th>
                                <th>版本</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="materialTableBody">
                            <!-- 物料列表将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页导航 -->
                <div class="pagination-container" id="materialPagination" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="pagination-info">
                            显示 <span id="pagination-start">1</span> - <span id="pagination-end">10</span> 条，共 <span id="pagination-total">0</span> 条
                        </div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item" id="pagination-first">
                                    <a class="page-link" href="javascript:void(0);" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item" id="pagination-prev">
                                    <a class="page-link" href="javascript:void(0);" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="javascript:void(0);">1</a></li>
                                <!-- 更多页码将通过JavaScript动态生成 -->
                                <li class="page-item" id="pagination-next">
                                    <a class="page-link" href="javascript:void(0);" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item" id="pagination-last">
                                    <a class="page-link" href="javascript:void(0);" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
                
                <!-- 加载状态 -->
                <div id="materialLoadingState" class="text-center py-3" style="display: none;">
                    <i class="fas fa-spinner fa-spin mr-2"></i> 正在加载物料数据...
                </div>
                
                <!-- 空状态 -->
                <div id="materialEmptyState" class="text-center py-3" style="display: none;">
                    <i class="fas fa-info-circle mr-2"></i> 没有找到符合条件的物料
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>

<!-- 新增子件模态框 -->
<div class="modal fade" id="addMaterialModal" tabindex="-1" role="dialog" aria-labelledby="addMaterialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMaterialModalLabel">新增子件</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="height: 70vh; padding: 0;">
                <iframe id="addMaterialIframe" src="" style="width: 100%; height: 100%; border: none;" frameborder="0"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveAddMaterial">
                    <i class="fas fa-save mr-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 新增父件模态框 -->
<div class="modal fade" id="addMaterialParentModal" tabindex="-1" role="dialog" aria-labelledby="addMaterialParentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMaterialParentModalLabel">新增父件</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="height: 70vh; padding: 0;">
                <iframe id="addMaterialParentIframe" src="" style="width: 100%; height: 100%; border: none;" frameborder="0"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveAddMaterialParent">
                    <i class="fas fa-save mr-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
    .content-wrapper {
        margin-left: 0 !important;
    }
    
    /* 添加高亮闪烁动画 */
    @keyframes highlight-pulse {
        0% { background-color: #e8f5e9; }
        50% { background-color: #a5d6a7; }
        100% { background-color: #e8f5e9; }
    }
    
    .bom-container {
        height: calc(100vh - 60px);
        overflow: hidden;
    }
    
    .left-panel {
        border-right: 1px solid #dee2e6;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        background: white;
        transition: all 0.3s ease;
    }

    .left-panel.collapsed {
        width: 0 !important;
        min-width: 0 !important;
        overflow: hidden;
        border-right: none;
    }

    .left-panel.collapsed .toolbar,
    .left-panel.collapsed .breadcrumb-nav,
    .left-panel.collapsed .search-box,
    .left-panel.collapsed .tree-container {
        display: none;
    }

    /* 添加浮动切换按钮 */
    .toggle-btn-floating {
        position: fixed;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1000;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
        display: none;
    }

    .toggle-btn-floating:hover {
        background: #0056b3;
        transform: translateY(-50%) scale(1.1);
    }

    .toggle-btn-floating.show {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .right-panel {
        height: 100%;
        overflow-y: auto;
        background: #f8f9fa;
    }
    
    .search-box {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        background: white;
    }
    
    .tree-container {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: white;
    }
    
    .toolbar {
        padding: 1rem;
        background: white;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    /* 自定义树形样式 */
    .custom-tree {
        font-size: 0.875rem;
    }
    
    .tree-node {
        padding: 0.5rem;
        margin-bottom: 0.25rem;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.2s;
        user-select: none;
    }
    
    .tree-node.highlighted {
        background-color: #fff8e1;
        border-left: 3px solid #ff9800;
        transform: translateX(2px);
        box-shadow: 0 2px 4px rgba(255, 152, 0, 0.1);
    }

    .tree-node.selected {
        background-color: #e8f5e9;
        border-left: 4px solid #4caf50;
        transform: translateX(3px);
        box-shadow: 0 3px 6px rgba(76, 175, 80, 0.25);
        font-weight: bold;
        color: #2e7d32;
    }
    
    .tree-node.parent {
        font-weight: 500;
        background-color: #f8f9fa;
    }
    
    .tree-node.parent:hover {
        background-color: #e9ecef;
    }
    
    .tree-node.parent.selected {
        background-color: #e8f5e9;
        color: #2e7d32;
        font-weight: bold;
    }
    
    .tree-node.child {
        margin-left: 1rem;
        border-left: 2px solid #e9ecef;
        padding-left: 1rem;
    }
    
    .tree-node.grandchild {
        margin-left: 2rem;
        border-left: 2px solid #e9ecef;
        padding-left: 1rem;
    }
    
    .node-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }
    
    .node-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
    }
    
    .node-text {
        flex: 1;
        min-width: 0;
    }

    .node-text > div {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .expand-icon {
        cursor: pointer;
        transition: all 0.2s ease;
        margin-right: 0.5rem;
        padding: 2px;
        border-radius: 2px;
        color: #666;
    }

    .expand-icon:hover {
        background-color: #f0f0f0;
        color: #333;
    }
    
    .expand-icon.expanded {
        transform: rotate(90deg);
        color: #007bff;
    }
    
    .detail-form {
        background: white;
        border-radius: 0.375rem;
        margin: 1rem;
        padding: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .nav-tabs .nav-link {
        color: #495057;
        font-weight: 500;
        font-size: 0.875rem;
    }
    
    .nav-tabs .nav-link.active {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }
    
    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        padding: 1.5rem;
        background: white;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }
    
    .form-control {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    
    .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    /* 面包屑导航样式 */
    .breadcrumb-nav {
        padding: 0.75rem 1rem;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        font-size: 0.875rem;
    }

    .breadcrumb-content {
        display: flex;
        align-items: center;
    }

    .breadcrumb-link {
        color: #007bff;
        text-decoration: none;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s;
    }

    .breadcrumb-link:hover {
        background-color: #e3f2fd;
        text-decoration: none;
        color: #0056b3;
    }

    .breadcrumb-separator {
        margin: 0 0.5rem;
        color: #6c757d;
        font-size: 0.75rem;
    }

    .breadcrumb-current {
        color: #495057;
        font-weight: 500;
    }

    /* 客户列表样式 */
    .customer-item {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.2s;
        user-select: none;
        border: 1px solid #e9ecef;
        background: white;
    }

    .customer-item.highlighted {
        background-color: #fff8e1;
        border-color: #ff9800;
        transform: translateX(2px);
        box-shadow: 0 2px 4px rgba(255, 152, 0, 0.1);
    }

    .customer-item.selected {
        background-color: #e8f5e9;
        border-color: #4caf50;
        transform: translateX(3px);
        box-shadow: 0 3px 6px rgba(76, 175, 80, 0.25);
    }

    .customer-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .customer-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
    }

    .customer-text {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-width: 0;
        margin-left: 0.5rem;
    }

    .customer-icon {
        color: #007bff;
        font-size: 1.25rem;
        margin-right: 0.5rem;
    }
    
    .customer-item.selected .customer-icon {
        color: #4caf50;
    }

    .customer-name {
        font-weight: 500;
        color: #343a40;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .customer-item.selected .customer-name {
        font-weight: bold;
        color: #2e7d32;
    }

    .customer-code {
        font-size: 0.75rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .customer-meta {
        display: flex;
        align-items: center;
    }

    .customer-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        background-color: #6c757d;
        color: white;
    }

    .customer-arrow {
        color: #adb5bd;
        transition: all 0.2s;
    }

    .customer-item:hover .customer-arrow {
        color: #007bff;
        transform: translateX(3px);
    }

    /* 按钮文字控制 */
    .btn-text {
        margin-left: 0.5rem;
    }
    
    /* 模态框样式 */
    .modal-xl {
        max-width: 90%;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/bom_tree.js' %}"></script>
<script>
    $(document).ready(function() {
        // 传递Django模板中的客户数据到JavaScript
        
        // 常规模式下显示所有客户数据
        const customersData = [
            {% for customer in customers %}
            {
                id: "customer_{{ customer.customer_id }}",
                type: "customer",
                name: "{{ customer.customer_name }}",
                Customer_Name: "{{ customer.customer_name }}",
                Customer_Code: "{{ customer.customer_code }}",
                Customer_Order: {{ customer.customer_order|default:"0" }},
                icon: "fas fa-building",
                expanded: false,
                drawingNumber: "{{ customer.customer_code }}",
                materialCode: "CUST-{{ customer.customer_code }}",
                customerName: "{{ customer.customer_name }}",
                materialName: "{{ customer.customer_name }}",
                version: "A",
                image: "",
                material: "多种材料",
                specification: "企业标准",
                materialCategory: "客户",
                semifinishedStatus: "N/A",
                lossRate: "0",
                quantity: "1",
                productionQuantity: "1",
                partUsage: "1",
                unit: "套",
                workshop: "总装车间",
                responsible: "管理员",
                uploadDate: "{{ customer.customer_addtime|date:'Y-m-d' }}",
                customerSupply: "否",
                outsourcing: "否",
                sales: "是",
                selfMade: "否",
                subcontract: "否",
                productionConsumption: "否",
                batchManagement: "是",
                cuttingSize: "",
                castingSupplier: "",
                machiningSupplier: "",
                sheetMetalSupplier: "",
                procurementSupplier: "",
                processNumber: "PR000",
                processSteps: ["总装", "检验", "包装"],
                children: []
            }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ];
       
        
        // 初始化BOM树形列表，使用从Django传递的客户数据
        init(customersData);
        
    });
</script>
{% endblock %} 