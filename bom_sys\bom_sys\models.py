from django.db import models


class User(models.Model):
    """用户模型"""
    user_id = models.BigAutoField(db_column='User_Id', primary_key=True)
    user_name = models.CharField(db_column='User_Name', max_length=255, blank=True, null=True, verbose_name='用户名')
    user_pass = models.CharField(db_column='User_Pass', max_length=255, blank=True, null=True, verbose_name='密码')
    user_role = models.IntegerField(db_column='User_Role', blank=True, null=True, verbose_name='角色')
    user_nick = models.CharField(db_column='User_Nick', max_length=255, blank=True, null=True, verbose_name='昵称')
    user_logintime = models.DateTimeField(db_column='User_LoginTime', blank=True, null=True, verbose_name='登录时间')

    class Meta:
        managed = False  # Django不管理表结构
        db_table = 'user'
        verbose_name = '用户'
        verbose_name_plural = '用户'

    def __str__(self):
        return self.user_name or ''


class Config(models.Model):
    """系统配置模型"""
    config_id = models.AutoField(db_column='Config_Id', primary_key=True)
    config_smbpath = models.CharField(db_column='Config_SMBPath', max_length=255, blank=True, null=True, verbose_name='共享路径')

    class Meta:
        managed = False
        db_table = 'config'
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'

    def __str__(self):
        return self.config_name or ''


class Material(models.Model):
    """物料模型"""
    material_id = models.AutoField(db_column='Material_Id', primary_key=True)
    material_drawingno = models.CharField(db_column='Material_DrawingNo', max_length=255, blank=True, null=True, verbose_name='客户图号')
    material_name = models.CharField(db_column='Material_Name', max_length=255, blank=True, null=True, verbose_name='物料名称')
    material_no = models.CharField(db_column='Material_No', max_length=255, blank=True, null=True, verbose_name='物料编号')
    material_count = models.IntegerField(db_column='Material_Count', blank=True, null=True, verbose_name='数量')
    material_img = models.CharField(db_column='Material_Img', max_length=255, blank=True, null=True, verbose_name='图片')
    material_quality = models.CharField(db_column='Material_Quality', max_length=255, blank=True, null=True, verbose_name='材质')
    material_spec = models.CharField(db_column='Material_Spec', max_length=255, blank=True, null=True, verbose_name='规格')
    material_version = models.CharField(db_column='Material_Version', max_length=255, blank=True, null=True, verbose_name='版本')
    material_semistate = models.IntegerField(db_column='Material_SemiState', blank=True, null=True, verbose_name='半成品状态')
    material_producecount = models.IntegerField(db_column='Material_ProduceCount', blank=True, null=True, verbose_name='生产数量')
    material_partcount = models.IntegerField(db_column='Material_PartCount', blank=True, null=True, verbose_name='部件数量')
    material_unit = models.CharField(db_column='Material_Unit',max_length=255, blank=True, null=True, verbose_name='单位')
    material_lossrate = models.FloatField(db_column='Material_LossRate', blank=True, null=True, verbose_name='损耗率')
    material_customer = models.CharField(db_column='Material_Customer', max_length=255, blank=True, null=True, verbose_name='客户')
    material_customername = models.CharField(db_column='Material_CustomerName', max_length=255, blank=True, null=True, verbose_name='客户名称')
    material_workshop = models.CharField(db_column='Material_WorkShop', max_length=255, blank=True, null=True, verbose_name='车间')
    material_attr = models.CharField(db_column='Material_Attr', max_length=255, blank=True, null=True, verbose_name='属性')
    material_attrname = models.CharField(db_column='Material_AttrName', max_length=255, blank=True, null=True, verbose_name='属性名称')
    material_inventory = models.CharField(db_column='Material_inventory', max_length=255, blank=True, null=True, verbose_name='库存')
    material_size = models.CharField(db_column='Material_Size', max_length=255, blank=True, null=True, verbose_name='尺寸')
    material_state = models.IntegerField(db_column='Material_State', blank=True, null=True, verbose_name='状态')
    material_techmanager = models.CharField(db_column='Material_TechManager', max_length=255, blank=True, null=True, verbose_name='技术负责人')
    #工艺相关
    material_processroute = models.TextField(db_column='Material_Processroute', blank=True, null=True, verbose_name='工艺路线')
    material_ProcessrouteTimes = models.TextField(db_column='Material_ProcessrouteTimes', blank=True, null=True, verbose_name='工序详情【工时】：顺序以|分割 如“1.5小时|2.0分钟|1小时” 与工序清单数量一致')
    material_ProcessrouteDevice = models.TextField(db_column='Material_ProcessrouteDevice', blank=True, null=True, verbose_name='工序详情【设备】：顺序以|分割 如“设备1|设备2|设备3” 与工序清单数量一致')
    material_ProcessroutePrice= models.TextField(db_column='Material_ProcessroutePrice', blank=True, null=True, verbose_name='工序详情【工价】：顺序以|分割 如“100元|200元|300元” 与工序清单数量一致')
    material_ProcessrouteType = models.TextField(db_column='Material_ProcessrouteType', blank=True, null=True, verbose_name='工序详情【加工方式】：顺序以|分割 如“自制|委外” 与工序清单数量一致')
    material_ProcessrouteOutSupplier = models.TextField(db_column='Material_ProcessrouteOutSupplier', blank=True, null=True, verbose_name='工序详情【委外供应商】：顺序以|分割 如“供应商1|供应商2|供应商3” 与工序清单数量一致')
    material_ProcessrouteOutPrice = models.TextField(db_column='Material_ProcessrouteOutPrice', blank=True, null=True, verbose_name='工序详情【委外单价】：顺序以|分割 如“100元|200元|300元” 与工序清单数量一致')
    material_ProcessrouteImportant= models.TextField(db_column='Material_ProcessrouteImportant', blank=True, null=True, verbose_name='工序详情【关键工序】：顺序以|分割 如“是|否|是” 与工序清单数量一致')
    material_ProcessrouteCode= models.TextField(db_column='Material_ProcessrouteCode', blank=True, null=True, verbose_name='工序详情【工序代码】：顺序以|分割 如“代码1|代码2|代码3” 与工序清单数量一致')

    # 供应商相关字段
    material_supp_casting = models.CharField(db_column='Material_Supp_Casting', max_length=255, blank=True, null=True, verbose_name='铸造供应商')
    material_supp_machining = models.CharField(db_column='Material_Supp_Machining', max_length=255, blank=True, null=True, verbose_name='机加工供应商')
    material_supp_sheetmetal = models.CharField(db_column='Material_Supp_SheetMetal', max_length=255, blank=True, null=True, verbose_name='钣金供应商')
    material_supp_purchase = models.CharField(db_column='Material_Supp_Purchase', max_length=255, blank=True, null=True, verbose_name='采购供应商')
    material_supp_prochasemanager = models.CharField(db_column='Material_Supp_ProchaseManager', max_length=255, blank=True, null=True, verbose_name='采购经理')
    
    # 文档相关字段
    material_drawing_customer = models.CharField(db_column='Material_Drawing_Customer', max_length=255, blank=True, null=True, verbose_name='客户图纸')
    material_drawing_finished = models.CharField(db_column='Material_Drawing_Finished', max_length=255, blank=True, null=True, verbose_name='成品图纸')
    material_drawing_workmanship = models.CharField(db_column='Material_Drawing_Workmanship', max_length=255, blank=True, null=True, verbose_name='工艺图纸')
    material_drawing_testingform = models.CharField(db_column='Material_Drawing_TestingForm', max_length=255, blank=True, null=True, verbose_name='检验表单')
    material_drawing_datafolder = models.CharField(db_column='Material_Drawing_Datafolder', max_length=255, blank=True, null=True, verbose_name='数据文件夹')
    
    material_uptime = models.DateTimeField(db_column='Material_UpTime', blank=True, null=True, verbose_name='更新时间')

    #临时数据相关
    material_tempstate = models.IntegerField(db_column='Material_tempState',default=0, blank=True, null=True, verbose_name='临时表状态 0:正式表 1：新增，2：修改')
    material_tempTrueId = models.IntegerField(db_column='Material_tempTrueId',default=0, blank=True, null=True, verbose_name='临时表主键')
    material_tempUserId = models.IntegerField(db_column='Material_tempUserId',default=0, blank=True, null=True, verbose_name='临时表用户ID')
    
    #时间相关
    material_addTime = models.DateTimeField(db_column='Material_AddTime', blank=True, null=True, verbose_name='添加时间')
    material_updateTime = models.DateTimeField(db_column='Material_UpdateTime', blank=True, null=True, verbose_name='更新时间')

    class Meta:
        managed = False
        db_table = 'Material'  # 注意这里使用大写的表名
        verbose_name = '物料'
        verbose_name_plural = '物料'

    def __str__(self):
        return self.material_name or self.material_no or ''


class Customer(models.Model):
    """客户模型"""
    customer_id = models.AutoField(db_column='Customer_Id', primary_key=True)
    customer_name = models.CharField(db_column='Customer_Name', max_length=255, blank=True, null=True, verbose_name='客户名称')
    customer_code = models.CharField(db_column='Customer_Code', max_length=255, blank=True, null=True, verbose_name='客户代码')
    customer_addtime = models.DateTimeField(db_column='Customer_AddTime', blank=True, null=True, verbose_name='添加时间')
    customer_order = models.IntegerField(db_column='Customer_Order', blank=True, null=True, verbose_name='排序')

    class Meta:
        managed = False
        db_table = 'customer'
        verbose_name = '客户'
        verbose_name_plural = '客户'

    def __str__(self):
        return self.customer_name or ''


class Bom(models.Model):
    """BOM模型"""
    bom_id = models.AutoField(db_column='bom_Id', primary_key=True, verbose_name='主键自增长')
    bom_pid = models.IntegerField(db_column='bom_Pid', blank=True, null=True, verbose_name='父级编号（如果为顶级则为0）')
    bom_level = models.IntegerField(db_column='bom_Level', blank=True, null=True, verbose_name='层级')
    bom_material = models.IntegerField(db_column='bom_Material', blank=True, null=True, verbose_name='对应物料数据')
    bom_state = models.IntegerField(db_column='bom_State', blank=True, null=True, verbose_name='状态：0：正式表，1：新增，2：修改')
    bom_ImportUser = models.IntegerField(db_column='bom_ImportUser', blank=True, null=True, verbose_name='导入用户')
    bom_num = models.IntegerField(db_column='bom_Num', blank=True, null=True, verbose_name='数量')
    bom_lossrate = models.FloatField(db_column='bom_LossRate', blank=True, null=True, verbose_name='损耗率')
    bom_partcount = models.IntegerField(db_column='bom_PartCount', blank=True, null=True, verbose_name='零件用量')
    bom_producecount = models.IntegerField(db_column='bom_ProduceCount', blank=True, null=True, verbose_name='生产数量')
    bom_order= models.IntegerField(db_column='bom_Order', blank=True, null=True, verbose_name='排序')
    #时间相关
    bom_addTime = models.DateTimeField(db_column='bom_AddTime', blank=True, null=True, verbose_name='添加时间')
    bom_updateTime = models.DateTimeField(db_column='bom_UpdateTime', blank=True, null=True, verbose_name='更新时间')

    class Meta:
        managed = False
        db_table = 'bom'
        verbose_name = 'BOM'
        verbose_name_plural = 'BOM'
        
    def __str__(self):
        material_info = ""
        if self.bom_material:
            try:
                material = Material.objects.get(material_id=self.bom_material)
                material_info = f"{material.material_name or ''} - {material.material_drawingno or ''}"
            except Material.DoesNotExist:
                pass
        return material_info or f"BOM-{self.bom_id}"


class Datadictionary(models.Model):
    """数据字典模型"""
    datadictionary_id = models.AutoField(db_column='DataDictionary_Id', primary_key=True)
    datadictionary_name = models.CharField(db_column='DataDictionary_Name', max_length=255, blank=True, null=True, verbose_name='名称')
    datadictionary_pid = models.IntegerField(db_column='DataDictionary_PId', blank=True, null=True, verbose_name='父ID')
    datadictionary_code = models.CharField(db_column='DataDictionary_Code', max_length=255, blank=True, null=True, verbose_name='代码')
    datadictionary_tag = models.CharField(db_column='DataDictionary_Tag', max_length=255, blank=True, null=True, verbose_name='标签')
    datadictionary_order = models.IntegerField(db_column='DataDictionary_Order', blank=True, null=True, verbose_name='排序')

    class Meta:
        managed = False
        db_table = 'datadictionary'
        verbose_name = '数据字典'
        verbose_name_plural = '数据字典'

    def __str__(self):
        return self.datadictionary_name or ''


class PropertiesType(models.Model):
    """物料属性分类模型"""
    propertiestype_id = models.AutoField(db_column='PropertiesType_Id', primary_key=True)
    propertiestype_name = models.CharField(db_column='PropertiesType_Name', max_length=255, blank=True, null=True, verbose_name='属性名称')
    propertiestype_code = models.CharField(db_column='PropertiesType_Code', max_length=255, blank=True, null=True, verbose_name='前缀编码【2位数字】')
    propertiesType_Rule = models.CharField(db_column='propertiesType_Rule', max_length=255, blank=True, null=True, verbose_name='中缀规则')
    propertiestype_order = models.IntegerField(db_column='PropertiesType_Order', blank=True, null=True, verbose_name='排序')
    

    class Meta:
        managed = False
        db_table = 'propertiestype'
        verbose_name = '物料属性分类'
        verbose_name_plural = '物料属性分类'

    def __str__(self):
        return self.propertiestype_name or ''


class ProcessRoute(models.Model):
    """工艺路线模型"""
    processroute_id = models.AutoField(db_column='ProcessRoute_Id', primary_key=True)
    processroute_no = models.CharField(db_column='ProcessRoute_No', max_length=255, blank=True, null=True, verbose_name='工艺路线编号')
    processroute_name = models.CharField(db_column='ProcessRoute_Name', max_length=255, blank=True, null=True, verbose_name='工艺路线名称')
    processroute_content = models.TextField(db_column='ProcessRoute_Content', blank=True, null=True, verbose_name='工艺路线内容')
    processroute_order = models.IntegerField(db_column='ProcessRoute_Order', blank=True, null=True, verbose_name='排序')
    processroute_times = models.TextField(db_column='Processroute_Times', blank=True, null=True, verbose_name='工序详情【工时】', help_text='顺序以|分割，如"1.5小时|2.0分钟|1小时"，与工序清单数量一致')
    processroute_device = models.TextField(db_column='Processroute_Device', blank=True, null=True, verbose_name='工序详情【设备】', help_text='顺序以|分割，如"1|2|3"，与工序清单数量一致')
    processroute_price = models.TextField(db_column='Processroute_Price', blank=True, null=True, verbose_name='工序详情【工价】', help_text='顺序以|分割，如"1|2|3"，与工序清单数量一致')
    processroute_type = models.TextField(db_column='Processroute_Type', blank=True, null=True, verbose_name='工序详情【加工方式】', help_text='顺序以|分割，如"自制|委外"，与工序清单数量一致')
    processroute_outsupplier = models.TextField(db_column='Processroute_OutSupplier', blank=True, null=True, verbose_name='工序详情【委外供应商】', help_text='顺序以|分割，如"1|2|3"，与工序清单数量一致')
    processroute_outprice = models.TextField(db_column='Processroute_OutPrice', blank=True, null=True, verbose_name='工序详情【委外单价】', help_text='顺序以|分割，如"1|2|3"，与工序清单数量一致')
    processroute_important = models.TextField(db_column='Processroute_Important', blank=True, null=True, verbose_name='工序详情【关键工序】', help_text='顺序以|分割，如"1|2|3"，与工序清单数量一致')
    processroute_code = models.TextField(db_column='ProcessRoute_Code', blank=True, null=True, verbose_name='工序详情【工序代码】', help_text='顺序以|分割，如"代码1|代码2|代码3"，与工序清单数量一致')

    class Meta:
        managed = False
        db_table = 'processroute'
        verbose_name = '工艺路线'
        verbose_name_plural = '工艺路线'

    def __str__(self):
        return self.processroute_name or ''

    def get_process_list(self):
        """获取工序清单列表"""
        if self.processroute_content:
            return [item.strip() for item in self.processroute_content.split('|') if item.strip()]
        return []

    def get_times_list(self):
        """获取工时列表"""
        if self.processroute_times:
            return [item.strip() for item in self.processroute_times.split('|') if item.strip()]
        return []

    def get_device_list(self):
        """获取设备列表"""
        if self.processroute_device:
            return [item.strip() for item in self.processroute_device.split('|') if item.strip()]
        return []

    def get_price_list(self):
        """获取工价列表"""
        if self.processroute_price:
            return [item.strip() for item in self.processroute_price.split('|') if item.strip()]
        return []

    def get_type_list(self):
        """获取加工方式列表"""
        if self.processroute_type:
            return [item.strip() for item in self.processroute_type.split('|') if item.strip()]
        return []

    def get_outsupplier_list(self):
        """获取委外供应商列表"""
        if self.processroute_outsupplier:
            return [item.strip() for item in self.processroute_outsupplier.split('|') if item.strip()]
        return []

    def get_outprice_list(self):
        """获取委外单价列表"""
        if self.processroute_outprice:
            return [item.strip() for item in self.processroute_outprice.split('|') if item.strip()]
        return []

    def get_important_list(self):
        """获取关键工序列表"""
        if self.processroute_important:
            return [item.strip() for item in self.processroute_important.split('|') if item.strip()]
        return []

    def get_process_details(self):
        """获取完整的工序详情，返回字典列表"""
        processes = self.get_process_list()
        times = self.get_times_list()
        devices = self.get_device_list()
        prices = self.get_price_list()
        types = self.get_type_list()
        outsuppliers = self.get_outsupplier_list()
        outprices = self.get_outprice_list()
        importants = self.get_important_list()

        # 确保所有列表长度一致
        max_length = max(len(processes), len(times), len(devices), len(prices),
                        len(types), len(outsuppliers), len(outprices), len(importants))

        details = []
        for i in range(max_length):
            detail = {
                'process': processes[i] if i < len(processes) else '',
                'time': times[i] if i < len(times) else '',
                'device': devices[i] if i < len(devices) else '',
                'price': prices[i] if i < len(prices) else '',
                'type': types[i] if i < len(types) else '',
                'outsupplier': outsuppliers[i] if i < len(outsuppliers) else '',
                'outprice': outprices[i] if i < len(outprices) else '',
                'important': importants[i] if i < len(importants) else '',
            }
            details.append(detail)

        return details


class Equipment(models.Model):
    """设备管理模型"""
    equipment_id = models.AutoField(db_column='Equipment_Id', primary_key=True)
    equipment_department = models.CharField(db_column='Equipment_Department', max_length=255, blank=True, null=True, verbose_name='使用部门')
    equipment_line = models.CharField(db_column='Equipment_Line', max_length=255, blank=True, null=True, verbose_name='线组别')
    equipment_no = models.CharField(db_column='Equipment_No', max_length=255, blank=True, null=True, verbose_name='设备编号')
    equipment_name = models.CharField(db_column='Equipment_Name', max_length=255, blank=True, null=True, verbose_name='设备名称')
    equipment_spec = models.CharField(db_column='Equipment_Spec', max_length=255, blank=True, null=True, verbose_name='规格型号')
    equipment_config = models.CharField(db_column='Equipment_Config', max_length=255, blank=True, null=True, verbose_name='配置')
    equipment_entry_date = models.DateField(db_column='Equipment_EntryDate', blank=True, null=True, verbose_name='入厂日期')
    equipment_factory_no = models.CharField(db_column='Equipment_FactoryNo', max_length=255, blank=True, null=True, verbose_name='出厂编号')
    equipment_origin = models.CharField(db_column='Equipment_Origin', max_length=50, blank=True, null=True, verbose_name='进口/国产',
                                       choices=[('domestic', '国产'), ('imported', '进口')])
    equipment_manufacturer = models.CharField(db_column='Equipment_Manufacturer', max_length=255, blank=True, null=True, verbose_name='制造商(品牌)')
    equipment_mfg_date = models.DateField(db_column='Equipment_MfgDate', blank=True, null=True, verbose_name='制造日期')
    equipment_system_name = models.CharField(db_column='Equipment_SystemName', max_length=255, blank=True, null=True, verbose_name='设备系统名称')
    equipment_contact = models.CharField(db_column='Equipment_Contact', max_length=255, blank=True, null=True, verbose_name='联系人')
    equipment_phone = models.CharField(db_column='Equipment_Phone', max_length=255, blank=True, null=True, verbose_name='电话')
    equipment_process_range = models.TextField(db_column='Equipment_ProcessRange', blank=True, null=True, verbose_name='加工范围')
    equipment_process_feature = models.TextField(db_column='Equipment_ProcessFeature', blank=True, null=True, verbose_name='加工特点')
    equipment_quantity = models.IntegerField(db_column='Equipment_Quantity', blank=True, null=True, verbose_name='数量/台')
    equipment_planned_quantity = models.IntegerField(db_column='Equipment_PlannedQuantity', blank=True, null=True, verbose_name='预投数量')
    equipment_rated_power = models.FloatField(db_column='Equipment_RatedPower', blank=True, null=True, verbose_name='额定功率(KW)')
    equipment_aux_power = models.FloatField(db_column='Equipment_AuxPower', blank=True, null=True, verbose_name='辅助功率(KW)')
    equipment_simultaneous_rate = models.FloatField(db_column='Equipment_SimultaneousRate', blank=True, null=True, verbose_name='同时率')
    equipment_config_power = models.FloatField(db_column='Equipment_ConfigPower', blank=True, null=True, verbose_name='配置功率(KW)')

    class Meta:
        managed = True  # 修改为True，允许Django管理表结构
        db_table = 'equipment'
        verbose_name = '设备'
        verbose_name_plural = '设备'

    def __str__(self):
        return self.equipment_name or self.equipment_no or ''

    @property
    def total_quantity(self):
        """设备总数 = 数量 + 预投数量"""
        qty = self.equipment_quantity or 0
        planned = self.equipment_planned_quantity or 0
        return qty + planned

    @property
    def total_power(self):
        """合计功率 = 额定功率 + 辅助功率"""
        rated = self.equipment_rated_power or 0
        aux = self.equipment_aux_power or 0
        return rated + aux


class SystemLog(models.Model):
    """系统日志模型"""
    log_id = models.AutoField(db_column='Log_Id', primary_key=True)
    log_user_id = models.IntegerField(db_column='Log_User_Id', blank=True, null=True, verbose_name='用户ID')
    log_user_name = models.CharField(db_column='Log_User_Name', max_length=255, blank=True, null=True, verbose_name='用户名')
    log_action = models.CharField(db_column='Log_Action', max_length=50, blank=True, null=True, verbose_name='操作类型')
    log_module = models.CharField(db_column='Log_Module', max_length=50, blank=True, null=True, verbose_name='模块类型')
    log_target_id = models.IntegerField(db_column='Log_Target_Id', blank=True, null=True, verbose_name='目标对象ID')
    log_target_name = models.CharField(db_column='Log_Target_Name', max_length=255, blank=True, null=True, verbose_name='目标对象名称')
    log_description = models.TextField(db_column='Log_Description', blank=True, null=True, verbose_name='操作描述')
    log_ip = models.CharField(db_column='Log_IP', max_length=45, blank=True, null=True, verbose_name='IP地址')
    log_time = models.DateTimeField(db_column='Log_Time', blank=True, null=True, verbose_name='操作时间')
    log_details = models.TextField(db_column='Log_Details', blank=True, null=True, verbose_name='详细信息')

    class Meta:
        managed = True
        db_table = 'SystemLog'
        verbose_name = '系统日志'
        verbose_name_plural = '系统日志'
        ordering = ['-log_time']

    def __str__(self):
        return f"{self.log_user_name} - {self.log_action} - {self.log_module}"