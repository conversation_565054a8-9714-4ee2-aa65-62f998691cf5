#!/usr/bin/env python
"""
测试数据库连接脚本
"""
import os
import sys
import django

# 添加项目路径
sys.path.append('bom_sys')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bom_sys.settings')

try:
    django.setup()
    
    from django.db import connection
    from django.core.management.color import no_style
    
    print("正在测试数据库连接...")
    
    # 测试数据库连接
    with connection.cursor() as cursor:
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✅ 数据库连接成功！")
        print(f"MySQL版本: {version[0]}")
        
        # 检查数据库
        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()
        print(f"可用数据库: {[db[0] for db in databases]}")
        
        # 检查当前数据库
        cursor.execute("SELECT DATABASE()")
        current_db = cursor.fetchone()
        print(f"当前数据库: {current_db[0]}")
        
        # 检查字符集
        cursor.execute("SHOW VARIABLES LIKE 'character_set%'")
        charset_vars = cursor.fetchall()
        print("字符集设置:")
        for var in charset_vars:
            print(f"  {var[0]}: {var[1]}")
            
    print("\n✅ 数据库连接测试完成！")
    
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")
    print("\n可能的解决方案:")
    print("1. 检查MySQL服务是否运行")
    print("2. 检查数据库配置（用户名、密码、主机、端口）")
    print("3. 检查网络连接")
    print("4. 检查数据库用户权限")
