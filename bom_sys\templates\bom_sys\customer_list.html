{% extends 'base.html' %}
{% load static %}

{% block title %}客户信息 - 物料清单管理系统{% endblock %}

{% block body_class %}{% if is_iframe %}{% else %}hold-transition sidebar-mini layout-fixed{% endif %}{% endblock %}

{% block content %}
{% if is_iframe %}
    <!-- 主体内容 -->
    <section class="content">
        <div class="container-fluid">
            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">客户信息列表</h3>
                    <div class="card-tools">
                        <a href="{% url 'customer_add' %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 添加客户
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                            <input type="text" class="form-control" id="customerSearchInput" placeholder="输入客户代码或名称进行搜索...">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>客户名称</th>
                                    <th>客户代码</th>
                                    <th>添加时间</th>
                                    <th>排序</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers %}
                                <tr>
                                    <td>{{ customer.customer_id }}</td>
                                    <td>{{ customer.customer_name }}</td>
                                    <td>{{ customer.customer_code }}</td>
                                    <td>{{ customer.customer_addtime|date:"Y-m-d H:i:s" }}</td>
                                    <td>{{ customer.customer_order }}</td>
                                    <td>
                                        <a href="{% url 'customer_edit' customer.customer_id %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-info btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="javascript:void(0)" class="btn btn-danger btn-sm" onclick="if(confirmAction('确定要删除此客户吗？')) window.location.href='{% url 'customer_delete' customer.customer_id %}{% if is_iframe %}?iframe=1{% endif %}';">
                                            <i class="fas fa-trash"></i> 删除
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% else %}
    <div class="wrapper">
        <!-- 导航栏 -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- 左侧导航栏切换按钮 -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
            </ul>

            <!-- 右侧导航栏 -->
            <ul class="navbar-nav ml-auto">
                <!-- 用户信息下拉菜单 -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline">{{ user_nick|default:username }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> 个人资料
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{% url 'logout' %}" class="dropdown-item">
                            <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                        </a>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- 左侧边栏 -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- 品牌Logo -->
            <a href="#" class="brand-link">
                <span class="brand-text font-weight-light">物料清单管理系统</span>
            </a>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 用户面板 -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-light"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block">{{ user_nick|default:username }}</a>
                    </div>
                </div>

                <!-- 侧边栏菜单 -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'dashboard' %}" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>
                                    仪表盘
                                </p>
                            </a>
                        </li>
                        
                        <li class="nav-item menu-open">
                            <a href="#" class="nav-link active">
                                <i class="nav-icon fas fa-database"></i>
                                <p>
                                    基础数据管理
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{% url 'data_dictionary_list' %}" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>数据字典</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'customer_list' %}" class="nav-link active">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>客户信息</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 主体内容 -->
            <section class="content">
                <div class="container-fluid">
                    <!-- 消息提示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">客户信息列表</h3>
                            <div class="card-tools">
                                <a href="{% url 'customer_add' %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加客户
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <div class="input-group mb-3">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="customerSearchInput" placeholder="输入客户代码或名称进行搜索...">
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>客户名称</th>
                                            <th>客户代码</th>
                                            <th>添加时间</th>
                                            <th>排序</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for customer in customers %}
                                        <tr>
                                            <td>{{ customer.customer_id }}</td>
                                            <td>{{ customer.customer_name }}</td>
                                            <td>{{ customer.customer_code }}</td>
                                            <td>{{ customer.customer_addtime|date:"Y-m-d H:i:s" }}</td>
                                            <td>{{ customer.customer_order }}</td>
                                            <td>
                                                <a href="{% url 'customer_edit' customer.customer_id %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </a>
                                                <a href="javascript:void(0)" class="btn btn-danger btn-sm" onclick="if(confirmAction('确定要删除此客户吗？')) window.location.href='{% url 'customer_delete' customer.customer_id %}{% if is_iframe %}?iframe=1{% endif %}';">
                                                    <i class="fas fa-trash"></i> 删除
                                                </a>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="6" class="text-center">暂无数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 自动隐藏提示信息
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
        
        // 客户信息实时搜索功能
        $("#customerSearchInput").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $("table tbody tr").filter(function() {
                // 获取客户名称和客户代码
                var customerName = $(this).find("td:eq(1)").text().toLowerCase();
                var customerCode = $(this).find("td:eq(2)").text().toLowerCase();
                
                // 检查是否匹配搜索条件（客户名称或客户代码）
                var isMatch = customerName.indexOf(value) > -1 || customerCode.indexOf(value) > -1;
                
                // 显示或隐藏行
                $(this).toggle(isMatch);
            });
            
            // 如果没有匹配的结果，显示"无匹配数据"行
            var visibleRows = $("table tbody tr:visible").length;
            if (visibleRows === 0) {
                // 检查是否已存在无匹配数据行
                if ($("table tbody tr.no-match-row").length === 0) {
                    $("table tbody").append('<tr class="no-match-row"><td colspan="6" class="text-center">无匹配数据</td></tr>');
                } else {
                    $("table tbody tr.no-match-row").show();
                }
            } else {
                $("table tbody tr.no-match-row").remove();
            }
        });
    });
</script>
{% endblock %} 