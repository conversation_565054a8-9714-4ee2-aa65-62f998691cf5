<div class="tree-node {% if not node.children %}tree-node-leaf{% endif %}">
    <div class="tree-node-content">
        {% if node.children %}
        <div class="tree-node-toggle">
            <i class="fas fa-minus"></i>
        </div>
        {% else %}
        <div class="tree-node-toggle" style="visibility: hidden;">
            <i class="fas fa-minus"></i>
        </div>
        {% endif %}
        
        <div class="tree-node-info">
            <strong>{{ node.name }}</strong>
            <span class="badge badge-info tree-node-badge">{{ node.code }}</span>
            {% if node.tag %}
            <span class="badge badge-secondary tree-node-badge">{{ node.tag }}</span>
            {% endif %}
        </div>
        
        <div class="tree-node-actions">
            <a href="{% url 'data_dictionary_edit' node.id %}{% if is_iframe %}?iframe=1{% endif %}" class="btn btn-info btn-sm" title="编辑">
                <i class="fas fa-edit"></i>
            </a>
            <a href="javascript:void(0)" class="btn btn-danger btn-sm" onclick="if(confirmAction('确定要删除此字典项吗？')) window.location.href='{% url 'data_dictionary_delete' node.id %}{% if is_iframe %}?iframe=1{% endif %}';" title="删除">
                <i class="fas fa-trash"></i>
            </a>
            <a href="{% url 'data_dictionary_batch_edit' %}?parent_id={{ node.id }}{% if is_iframe %}&iframe=1{% endif %}" class="btn btn-success btn-sm" title="批量编辑子级">
                <i class="fas fa-tasks"></i>
            </a>
        </div>
    </div>
    
    {% if node.children %}
    <div class="tree-node-children">
        {% for child in node.children %}
            {% include 'bom_sys/tree_node.html' with node=child is_iframe=is_iframe %}
        {% endfor %}
    </div>
    {% endif %}
</div> 