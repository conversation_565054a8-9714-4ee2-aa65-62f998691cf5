{% extends 'base.html' %}

{% block title %}登录 - 物料清单管理系统{% endblock %}

{% block body_class %}hold-transition login-page{% endblock %}

{% block extra_css %}
<style>
    .login-logo {
        margin-bottom: 30px;
    }
    .login-box {
        width: 450px;
    }
    .login-card-body {
        padding: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-box">
    <div class="login-logo">
        <a href="#">嘉兴锦明传动科技有限公司</a><br>
        <a href="#"><b>物料清单</b>管理系统</a>
    </div>
    
    <div class="card">
        <div class="card-body login-card-body">
            <p class="login-box-msg">请登录您的帐号</p>
            
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <form method="post" action="{% url 'login' %}">
                {% csrf_token %}
                
                <div class="input-group mb-3">
                    <input type="text" name="username" class="form-control" placeholder="用户名" required>
                    <div class="input-group-append">
                        <div class="input-group-text">
                            <span class="fas fa-user"></span>
                        </div>
                    </div>
                </div>
                
                <div class="input-group mb-3">
                    <input type="password" name="password" class="form-control" placeholder="密码" required>
                    <div class="input-group-append">
                        <div class="input-group-text">
                            <span class="fas fa-lock"></span>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-8">
                        <div class="icheck-primary">
                            <input type="checkbox" id="remember">
                            <label for="remember">
                                记住我
                            </label>
                        </div>
                    </div>
                    <div class="col-4">
                        <button type="submit" class="btn btn-primary btn-block">登录</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 自动隐藏提示信息
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
    });
</script>
{% endblock %} 