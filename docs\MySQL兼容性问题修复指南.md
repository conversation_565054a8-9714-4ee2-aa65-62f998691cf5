# MySQL兼容性问题修复指南

## 当前问题

错误信息：`ModuleNotFoundError: No module named 'bom_sys.mysql_backend'`

这是因为settings.py中配置了自定义的数据库后端，但该文件已被删除。

## 解决方案

### 步骤1：修复数据库配置

已将settings.py中的数据库引擎改回标准配置：
```python
'ENGINE': 'django.db.backends.mysql',
```

### 步骤2：使用兼容的Django版本

将Django版本降级到3.1.14，这个版本完全兼容MySQL 5.7：
```
Django==3.1.14
```

### 步骤3：执行修复

运行快速修复脚本：
```bash
quick_fix.bat
```

或手动执行：
```bash
# 激活虚拟环境
.venv\Scripts\activate

# 卸载当前Django
pip uninstall Django -y

# 安装兼容版本
pip install Django==3.1.14

# 安装其他依赖
pip install -r requirements.txt

# 测试数据库连接
python test_db_connection.py

# 进入项目目录
cd bom_sys

# 启动服务器
python manage.py runserver 0.0.0.0:8000
```

## 版本兼容性说明

| Django版本 | MySQL最低版本 | 状态 |
|-----------|--------------|------|
| 3.2.23    | 8.0          | ❌ 不兼容 |
| 3.2.13    | 8.0          | ❌ 不兼容 |
| 3.1.14    | 5.6          | ✅ 兼容 |
| 3.0.x     | 5.6          | ✅ 兼容 |

## 测试步骤

1. **测试数据库连接**：
   ```bash
   python test_db_connection.py
   ```

2. **检查Django配置**：
   ```bash
   cd bom_sys
   python manage.py check
   ```

3. **启动服务器**：
   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```

## 预期结果

修复后应该看到：
```
Watching for file changes with StatReloader
Performing system checks...

System check identified no issues (0 silenced).
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.
```

## 如果仍有问题

1. **检查Python版本**：确保使用Python 3.7+
2. **检查虚拟环境**：确保正确激活
3. **检查数据库服务**：确保MySQL服务运行
4. **检查网络连接**：确保能连接到数据库服务器
5. **检查权限**：确保数据库用户有足够权限

## 文件清单

修复涉及的文件：
- ✅ `bom_sys/bom_sys/settings.py` - 恢复标准数据库后端
- ✅ `requirements.txt` - 降级Django版本
- ✅ `quick_fix.bat` - 快速修复脚本
- ✅ `test_db_connection.py` - 数据库连接测试
