# BOM物料清单管理系统

## 项目介绍
本系统是一个基于Django开发的BOM(物料清单)管理系统，用于管理产品的物料清单、库存、采购等信息。系统采用Bootstrap+jQuery+AdminLTE作为前端框架，Django作为后端框架，MySQL作为数据库。

## 技术栈
- 前端: HTML + CSS + JavaScript + jQuery + Bootstrap + AdminLTE
- 后端: Python Django
- 数据库: MySQL

## 主要功能
- 物料基础信息管理
- BOM结构管理与维护
- 物料清单版本控制
- 文件上传与下载
- PDF文档预览
- Excel数据导入导出
- 用户权限管理

## 项目结构
```
bom_system/                  # 项目根目录
├── bom_system/              # Django项目配置目录
│   ├── __init__.py
│   ├── settings.py          # 项目设置
│   ├── urls.py              # URL配置
│   ├── wsgi.py              # WSGI配置
│   └── asgi.py              # ASGI配置
├── apps/                    # 应用目录
│   ├── accounts/            # 用户账户应用
│   ├── materials/           # 物料管理应用
│   ├── boms/                # BOM管理应用
│   ├── inventory/           # 库存管理应用
│   └── common/              # 公共组件应用
├── static/                  # 静态文件
│   ├── css/                 # 样式文件
│   ├── js/                  # JavaScript文件
│   ├── img/                 # 图片资源
│   ├── plugins/             # 第三方插件
│   │   ├── bootstrap/       # Bootstrap框架
│   │   ├── jquery/          # jQuery库
│   │   ├── adminlte/        # AdminLTE模板
│   │   ├── datatables/      # 数据表格插件
│   │   └── pdf.js/          # PDF预览插件
├── templates/               # 模板文件
│   ├── base.html            # 基础模板
│   ├── accounts/            # 账户模板
│   ├── materials/           # 物料模板
│   ├── boms/                # BOM模板
│   └── inventory/           # 库存模板
├── media/                   # 用户上传文件
├── docs/                    # 项目文档
├── tools/                   # 工具脚本
├── requirements.txt         # 依赖包列表
├── manage.py                # Django管理脚本
└── .gitignore               # Git忽略文件
```

## 安装与运行
1. 克隆项目代码
2. 安装依赖: `pip install -r requirements.txt`
3. 配置数据库: 修改`settings.py`中的数据库配置
4. 运行迁移: `python manage.py migrate`
5. 创建超级用户: `python manage.py createsuperuser`
6. 运行开发服务器: `python manage.py runserver`

## 开发团队
- 开发者: [您的姓名]

## 版权信息
© 2023 [公司/组织名称]. 保留所有权利。 